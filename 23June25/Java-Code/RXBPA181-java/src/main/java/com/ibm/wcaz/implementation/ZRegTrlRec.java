package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class ZRegTrlRec extends CpuRegdtlLayout {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private static final String Z_TRL_REC_VALUE = "URBZ";
    
    private String zRecordType = "";
    private String zCdfCustId = "";
    private String zCdfCustCode = "";
    private String zCreatDate = "";
    private String zCreatTime = "";
    private String zCustTrlCount = "";
    
    /** Initialize fields to non-null default values */
    public ZRegTrlRec() {}
    
    /** Initialize all fields to provided values */
    public ZRegTrlRec(String zRecordType, String zCdfCustId, String zCdfCustCode, String zCreatDate, String zCreatTime, String zCustTrlCount) {
        this.zRecordType = zRecordType;
        this.zCdfCustId = zCdfCustId;
        this.zCdfCustCode = zCdfCustCode;
        this.zCreatDate = zCreatDate;
        this.zCreatTime = zCreatTime;
        this.zCustTrlCount = zCustTrlCount;
    }
    
    @Override
    public ZRegTrlRec clone() throws CloneNotSupportedException {
        ZRegTrlRec cloned = (ZRegTrlRec) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code ZRegTrlRec} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected ZRegTrlRec(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code ZRegTrlRec} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected ZRegTrlRec(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code ZRegTrlRec} object
     * @see #setBytes(byte[], int)
     */
    public static ZRegTrlRec fromBytes(byte[] bytes, int offset) {
        return new ZRegTrlRec(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code ZRegTrlRec} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static ZRegTrlRec fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code ZRegTrlRec} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static ZRegTrlRec fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getZRecordType() {
        return this.zRecordType;
    }
    
    public void setZRecordType(String zRecordType) {
        this.zRecordType = zRecordType;
    }
    
    public boolean isZTrlRec() {
        return zRecordType.equals(Z_TRL_REC_VALUE);
    }
    
    public void setZTrlRec() {
        zRecordType = Z_TRL_REC_VALUE;
    }
    
    public String getZCdfCustId() {
        return this.zCdfCustId;
    }
    
    public void setZCdfCustId(String zCdfCustId) {
        this.zCdfCustId = zCdfCustId;
    }
    
    public String getZCdfCustCode() {
        return this.zCdfCustCode;
    }
    
    public void setZCdfCustCode(String zCdfCustCode) {
        this.zCdfCustCode = zCdfCustCode;
    }
    
    public String getZCreatDate() {
        return this.zCreatDate;
    }
    
    public void setZCreatDate(String zCreatDate) {
        this.zCreatDate = zCreatDate;
    }
    
    public String getZCreatTime() {
        return this.zCreatTime;
    }
    
    public void setZCreatTime(String zCreatTime) {
        this.zCreatTime = zCreatTime;
    }
    
    public String getZCustTrlCount() {
        return this.zCustTrlCount;
    }
    
    public void setZCustTrlCount(String zCustTrlCount) {
        this.zCustTrlCount = zCustTrlCount;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        zRecordType = "";
        zCdfCustId = "";
        zCdfCustCode = "";
        zCreatDate = "";
        zCreatTime = "";
        zCustTrlCount = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ zRecordType=\"");
        s.append(getZRecordType());
        s.append("\"");
        s.append(", zCdfCustId=\"");
        s.append(getZCdfCustId());
        s.append("\"");
        s.append(", zCdfCustCode=\"");
        s.append(getZCdfCustCode());
        s.append("\"");
        s.append(", zCreatDate=\"");
        s.append(getZCreatDate());
        s.append("\"");
        s.append(", zCreatTime=\"");
        s.append(getZCreatTime());
        s.append("\"");
        s.append(", zCustTrlCount=\"");
        s.append(getZCustTrlCount());
        s.append("\"");
        s.append(", filler5=\"");
        s.append(new String(filler5, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(ZRegTrlRec that) {
        return super.equals(that) &&
            this.zRecordType.equals(that.zRecordType) &&
            this.zCdfCustId.equals(that.zCdfCustId) &&
            this.zCdfCustCode.equals(that.zCdfCustCode) &&
            this.zCreatDate.equals(that.zCreatDate) &&
            this.zCreatTime.equals(that.zCreatTime) &&
            this.zCustTrlCount.equals(that.zCustTrlCount) &&
            Arrays.equals(this.filler5, that.filler5);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof ZRegTrlRec other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof ZRegTrlRec;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(zRecordType);
        result = 31 * result + Objects.hashCode(zCdfCustId);
        result = 31 * result + Objects.hashCode(zCdfCustCode);
        result = 31 * result + Objects.hashCode(zCreatDate);
        result = 31 * result + Objects.hashCode(zCreatTime);
        result = 31 * result + Objects.hashCode(zCustTrlCount);
        result = 31 * result + Arrays.hashCode(filler5);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(ZRegTrlRec that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.zRecordType.compareTo(that.zRecordType);
        if ( c != 0 ) return c;
        c = this.zCdfCustId.compareTo(that.zCdfCustId);
        if ( c != 0 ) return c;
        c = this.zCdfCustCode.compareTo(that.zCdfCustCode);
        if ( c != 0 ) return c;
        c = this.zCreatDate.compareTo(that.zCreatDate);
        if ( c != 0 ) return c;
        c = this.zCreatTime.compareTo(that.zCreatTime);
        if ( c != 0 ) return c;
        c = this.zCustTrlCount.compareTo(that.zCustTrlCount);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler5, that.filler5);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(CpuRegdtlLayout that) {
        if (that instanceof ZRegTrlRec other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(CpuRegdtlLayout.SIZE);
    }
    
    private static final StringField Z_RECORD_TYPE = factory.getStringField(4);
    private static final StringField Z_CDF_CUST_ID = factory.getStringField(4);
    private static final StringField Z_CDF_CUST_CODE = factory.getStringField(4);
    private static final StringField Z_CREAT_DATE = factory.getStringField(10);
    private static final StringField Z_CREAT_TIME = factory.getStringField(5);
    private static final StringField Z_CUST_TRL_COUNT = factory.getStringField(6);
    private static final ByteArrayField FILLER_5 = factory.getByteArrayField(167);
    private byte[] filler5 = new byte[167];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link CpuRegdtlLayout#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "Z-REG-TRL-REC record at RXBPASVC.cbl:74"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        Z_RECORD_TYPE.putString(zRecordType, bytes, offset);
        Z_CDF_CUST_ID.putString(zCdfCustId, bytes, offset);
        Z_CDF_CUST_CODE.putString(zCdfCustCode, bytes, offset);
        Z_CREAT_DATE.putString(zCreatDate, bytes, offset);
        Z_CREAT_TIME.putString(zCreatTime, bytes, offset);
        Z_CUST_TRL_COUNT.putString(zCustTrlCount, bytes, offset);
        FILLER_5.putByteArray(filler5, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link CpuRegdtlLayout#setBytes(byte[], int)} to set parent-class state.
     * @see "Z-REG-TRL-REC record at RXBPASVC.cbl:74"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        zRecordType = Z_RECORD_TYPE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        zCdfCustId = Z_CDF_CUST_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        zCdfCustCode = Z_CDF_CUST_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        zCreatDate = Z_CREAT_DATE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        zCreatTime = Z_CREAT_TIME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        zCustTrlCount = Z_CUST_TRL_COUNT.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_5.getByteArray(bytes, offset);
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
