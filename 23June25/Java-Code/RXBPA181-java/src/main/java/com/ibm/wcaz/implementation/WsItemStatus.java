package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class WsItemStatus implements Cloneable, Comparable<WsItemStatus> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private static final String INVALID_REC_TYPE_VALUE = "REC TYPE INVALID";
    private static final String DTL_CUST_ID_MISMATCH_VALUE = "CPU ID INVALID";
    private static final String DTL_CUST_CODE_MISMATCH_VALUE = "CPU CODE INVALID";
    private static final String DTL_CRT_DATE_MISMATCH_VALUE = "CREATE DATE INVALID";
    private static final String DTL_CRT_TIME_MISMATCH_VALUE = "CREATE TIME INVALID";
    private static final String VEND_NBR_NOT_PRESENT_VALUE = "CPU DLR MISSING";
    private static final String VEND_ID_CODE_NBR_NOT_FND_VALUE = "CPU DLR INVALID";
    private static final String VEND_ID_CODE_NBR_DUP_VALUE = "CPU DLR DUPLICATE";
    private static final String VEND_NAME_NOT_PRESENT_VALUE = "DLR NAME MISSING";
    private static final String MODEL_DESC_NOT_PRESENT_VALUE = "MOD DESC MISSING";
    private static final String MODEL_NBR_NOT_PRESENT_VALUE = "MOD- MISSING";
    private static final String SER_VIN_NOT_PRESENT_VALUE = "SER-/VIN MISSING";
    private static final String REG_COMP_DATE_NOT_PRESENT_VALUE = "REG DATE MISSING";
    private static final String REG_COMP_DATE_INVALID_VALUE = "REG DATE INVALID";
    private static final String REG_TYPE_NOT_PRESENT_VALUE = "REG TYPE MISSING";
    private static final String REG_TYPE_INVALID_VALUE = "REG TYPE INVALID";
    
    private String wsItemStatus = "";
    
    /** Initialize fields to non-null default values */
    public WsItemStatus() {}
    
    /** Initialize all fields to provided values */
    public WsItemStatus(String wsItemStatus) {
        this.wsItemStatus = wsItemStatus;
    }
    
    @Override
    public WsItemStatus clone() throws CloneNotSupportedException {
        WsItemStatus cloned = (WsItemStatus) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code WsItemStatus} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected WsItemStatus(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code WsItemStatus} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected WsItemStatus(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsItemStatus} object
     * @see #setBytes(byte[], int)
     */
    public static WsItemStatus fromBytes(byte[] bytes, int offset) {
        return new WsItemStatus(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsItemStatus} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static WsItemStatus fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code WsItemStatus} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static WsItemStatus fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getWsItemStatus() {
        return this.wsItemStatus;
    }
    
    public void setWsItemStatus(String wsItemStatus) {
        this.wsItemStatus = wsItemStatus;
    }
    
    public boolean isInvalidRecType() {
        return wsItemStatus.equals(INVALID_REC_TYPE_VALUE);
    }
    
    public void setInvalidRecType() {
        wsItemStatus = INVALID_REC_TYPE_VALUE;
    }
    
    public boolean isDtlCustIdMismatch() {
        return wsItemStatus.equals(DTL_CUST_ID_MISMATCH_VALUE);
    }
    
    public void setDtlCustIdMismatch() {
        wsItemStatus = DTL_CUST_ID_MISMATCH_VALUE;
    }
    
    public boolean isDtlCustCodeMismatch() {
        return wsItemStatus.equals(DTL_CUST_CODE_MISMATCH_VALUE);
    }
    
    public void setDtlCustCodeMismatch() {
        wsItemStatus = DTL_CUST_CODE_MISMATCH_VALUE;
    }
    
    public boolean isDtlCrtDateMismatch() {
        return wsItemStatus.equals(DTL_CRT_DATE_MISMATCH_VALUE);
    }
    
    public void setDtlCrtDateMismatch() {
        wsItemStatus = DTL_CRT_DATE_MISMATCH_VALUE;
    }
    
    public boolean isDtlCrtTimeMismatch() {
        return wsItemStatus.equals(DTL_CRT_TIME_MISMATCH_VALUE);
    }
    
    public void setDtlCrtTimeMismatch() {
        wsItemStatus = DTL_CRT_TIME_MISMATCH_VALUE;
    }
    
    public boolean isVendNbrNotPresent() {
        return wsItemStatus.equals(VEND_NBR_NOT_PRESENT_VALUE);
    }
    
    public void setVendNbrNotPresent() {
        wsItemStatus = VEND_NBR_NOT_PRESENT_VALUE;
    }
    
    public boolean isVendIdCodeNbrNotFnd() {
        return wsItemStatus.equals(VEND_ID_CODE_NBR_NOT_FND_VALUE);
    }
    
    public void setVendIdCodeNbrNotFnd() {
        wsItemStatus = VEND_ID_CODE_NBR_NOT_FND_VALUE;
    }
    
    public boolean isVendIdCodeNbrDup() {
        return wsItemStatus.equals(VEND_ID_CODE_NBR_DUP_VALUE);
    }
    
    public void setVendIdCodeNbrDup() {
        wsItemStatus = VEND_ID_CODE_NBR_DUP_VALUE;
    }
    
    public boolean isVendNameNotPresent() {
        return wsItemStatus.equals(VEND_NAME_NOT_PRESENT_VALUE);
    }
    
    public void setVendNameNotPresent() {
        wsItemStatus = VEND_NAME_NOT_PRESENT_VALUE;
    }
    
    public boolean isModelDescNotPresent() {
        return wsItemStatus.equals(MODEL_DESC_NOT_PRESENT_VALUE);
    }
    
    public void setModelDescNotPresent() {
        wsItemStatus = MODEL_DESC_NOT_PRESENT_VALUE;
    }
    
    public boolean isModelNbrNotPresent() {
        return wsItemStatus.equals(MODEL_NBR_NOT_PRESENT_VALUE);
    }
    
    public void setModelNbrNotPresent() {
        wsItemStatus = MODEL_NBR_NOT_PRESENT_VALUE;
    }
    
    public boolean isSerVinNotPresent() {
        return wsItemStatus.equals(SER_VIN_NOT_PRESENT_VALUE);
    }
    
    public void setSerVinNotPresent() {
        wsItemStatus = SER_VIN_NOT_PRESENT_VALUE;
    }
    
    public boolean isRegCompDateNotPresent() {
        return wsItemStatus.equals(REG_COMP_DATE_NOT_PRESENT_VALUE);
    }
    
    public void setRegCompDateNotPresent() {
        wsItemStatus = REG_COMP_DATE_NOT_PRESENT_VALUE;
    }
    
    public boolean isRegCompDateInvalid() {
        return wsItemStatus.equals(REG_COMP_DATE_INVALID_VALUE);
    }
    
    public void setRegCompDateInvalid() {
        wsItemStatus = REG_COMP_DATE_INVALID_VALUE;
    }
    
    public boolean isRegTypeNotPresent() {
        return wsItemStatus.equals(REG_TYPE_NOT_PRESENT_VALUE);
    }
    
    public void setRegTypeNotPresent() {
        wsItemStatus = REG_TYPE_NOT_PRESENT_VALUE;
    }
    
    public boolean isRegTypeInvalid() {
        return wsItemStatus.equals(REG_TYPE_INVALID_VALUE);
    }
    
    public void setRegTypeInvalid() {
        wsItemStatus = REG_TYPE_INVALID_VALUE;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        wsItemStatus = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ wsItemStatus=\"");
        s.append(getWsItemStatus());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(WsItemStatus that) {
        return this.wsItemStatus.equals(that.wsItemStatus);
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof WsItemStatus other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof WsItemStatus;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Objects.hashCode(wsItemStatus);
        return result;
    }
    
    @Override
    public int compareTo(WsItemStatus that) {
        int c = 0;
        c = this.wsItemStatus.compareTo(that.wsItemStatus);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final StringField WS_ITEM_STATUS = factory.getStringField(20);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsItemStatus} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code WS-ITEM-STATUS} record
     * @see "WS-ITEM-STATUS record at RXBPASVC.cbl:204"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        WS_ITEM_STATUS.putString(wsItemStatus, bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsItemStatus} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsItemStatus} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code WsItemStatus} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code WS-ITEM-STATUS} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "WS-ITEM-STATUS record at RXBPASVC.cbl:204"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        wsItemStatus = WS_ITEM_STATUS.getString(bytes, offset).replaceFirst("\\s+\\z", "");
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
