package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.BinaryAsIntField;
import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class Dclvwmcpcd implements Cloneable, Comparable<Dclvwmcpcd> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String cpuId = "";
    private String cpuCode = "";
    private int distNo;
    private int mfgNo;
    private char cpuStatCode = ' ';
    private String prodCode = "";
    private int distLocNo;
    private int mfgLocNo;
    private int postingBranchNo;
    private String altCpuCode = "";
    private int altCntlBranchNo;
    private String auditTs = "";
    private String auditLogonId = "";
    private int auditProcessTxLen;
    private String auditProcessTxText = "";
    private char auditDeleteFl = ' ';
    
    /** Initialize fields to non-null default values */
    public Dclvwmcpcd() {}
    
    /** Initialize all fields to provided values */
    public Dclvwmcpcd(String cpuId, String cpuCode, int distNo, int mfgNo, char cpuStatCode, String prodCode, int distLocNo, int mfgLocNo, int postingBranchNo, String altCpuCode, int altCntlBranchNo, String auditTs, String auditLogonId, int auditProcessTxLen, String auditProcessTxText, char auditDeleteFl) {
        this.cpuId = cpuId;
        this.cpuCode = cpuCode;
        this.distNo = distNo;
        this.mfgNo = mfgNo;
        this.cpuStatCode = cpuStatCode;
        this.prodCode = prodCode;
        this.distLocNo = distLocNo;
        this.mfgLocNo = mfgLocNo;
        this.postingBranchNo = postingBranchNo;
        this.altCpuCode = altCpuCode;
        this.altCntlBranchNo = altCntlBranchNo;
        this.auditTs = auditTs;
        this.auditLogonId = auditLogonId;
        this.auditProcessTxLen = auditProcessTxLen;
        this.auditProcessTxText = auditProcessTxText;
        this.auditDeleteFl = auditDeleteFl;
    }
    
    @Override
    public Dclvwmcpcd clone() throws CloneNotSupportedException {
        Dclvwmcpcd cloned = (Dclvwmcpcd) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code Dclvwmcpcd} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected Dclvwmcpcd(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code Dclvwmcpcd} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected Dclvwmcpcd(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code Dclvwmcpcd} object
     * @see #setBytes(byte[], int)
     */
    public static Dclvwmcpcd fromBytes(byte[] bytes, int offset) {
        return new Dclvwmcpcd(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code Dclvwmcpcd} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static Dclvwmcpcd fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code Dclvwmcpcd} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static Dclvwmcpcd fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getCpuId() {
        return this.cpuId;
    }
    
    public void setCpuId(String cpuId) {
        this.cpuId = cpuId;
    }
    
    public String getCpuCode() {
        return this.cpuCode;
    }
    
    public void setCpuCode(String cpuCode) {
        this.cpuCode = cpuCode;
    }
    
    public int getDistNo() {
        return this.distNo;
    }
    
    public void setDistNo(int distNo) {
        this.distNo = distNo;
    }
    
    public int getMfgNo() {
        return this.mfgNo;
    }
    
    public void setMfgNo(int mfgNo) {
        this.mfgNo = mfgNo;
    }
    
    public char getCpuStatCode() {
        return this.cpuStatCode;
    }
    
    public void setCpuStatCode(char cpuStatCode) {
        this.cpuStatCode = cpuStatCode;
    }
    
    public String getProdCode() {
        return this.prodCode;
    }
    
    public void setProdCode(String prodCode) {
        this.prodCode = prodCode;
    }
    
    public int getDistLocNo() {
        return this.distLocNo;
    }
    
    public void setDistLocNo(int distLocNo) {
        this.distLocNo = distLocNo;
    }
    
    public int getMfgLocNo() {
        return this.mfgLocNo;
    }
    
    public void setMfgLocNo(int mfgLocNo) {
        this.mfgLocNo = mfgLocNo;
    }
    
    public int getPostingBranchNo() {
        return this.postingBranchNo;
    }
    
    public void setPostingBranchNo(int postingBranchNo) {
        this.postingBranchNo = postingBranchNo;
    }
    
    public String getAltCpuCode() {
        return this.altCpuCode;
    }
    
    public void setAltCpuCode(String altCpuCode) {
        this.altCpuCode = altCpuCode;
    }
    
    public int getAltCntlBranchNo() {
        return this.altCntlBranchNo;
    }
    
    public void setAltCntlBranchNo(int altCntlBranchNo) {
        this.altCntlBranchNo = altCntlBranchNo;
    }
    
    public String getAuditTs() {
        return this.auditTs;
    }
    
    public void setAuditTs(String auditTs) {
        this.auditTs = auditTs;
    }
    
    public String getAuditLogonId() {
        return this.auditLogonId;
    }
    
    public void setAuditLogonId(String auditLogonId) {
        this.auditLogonId = auditLogonId;
    }
    
    public int getAuditProcessTxLen() {
        return this.auditProcessTxLen;
    }
    
    public void setAuditProcessTxLen(int auditProcessTxLen) {
        this.auditProcessTxLen = auditProcessTxLen;
    }
    
    public String getAuditProcessTxText() {
        return this.auditProcessTxText;
    }
    
    public void setAuditProcessTxText(String auditProcessTxText) {
        this.auditProcessTxText = auditProcessTxText;
    }
    
    public char getAuditDeleteFl() {
        return this.auditDeleteFl;
    }
    
    public void setAuditDeleteFl(char auditDeleteFl) {
        this.auditDeleteFl = auditDeleteFl;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        cpuId = "";
        cpuCode = "";
        distNo = 0;
        mfgNo = 0;
        cpuStatCode = ' ';
        prodCode = "";
        distLocNo = 0;
        mfgLocNo = 0;
        postingBranchNo = 0;
        altCpuCode = "";
        altCntlBranchNo = 0;
        auditTs = "";
        auditLogonId = "";
        auditProcessTxLen = 0;
        auditProcessTxText = "";
        auditDeleteFl = ' ';
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ cpuId=\"");
        s.append(getCpuId());
        s.append("\"");
        s.append(", cpuCode=\"");
        s.append(getCpuCode());
        s.append("\"");
        s.append(", distNo=\"");
        s.append(getDistNo());
        s.append("\"");
        s.append(", mfgNo=\"");
        s.append(getMfgNo());
        s.append("\"");
        s.append(", cpuStatCode=\"");
        s.append(getCpuStatCode());
        s.append("\"");
        s.append(", prodCode=\"");
        s.append(getProdCode());
        s.append("\"");
        s.append(", distLocNo=\"");
        s.append(getDistLocNo());
        s.append("\"");
        s.append(", mfgLocNo=\"");
        s.append(getMfgLocNo());
        s.append("\"");
        s.append(", postingBranchNo=\"");
        s.append(getPostingBranchNo());
        s.append("\"");
        s.append(", altCpuCode=\"");
        s.append(getAltCpuCode());
        s.append("\"");
        s.append(", altCntlBranchNo=\"");
        s.append(getAltCntlBranchNo());
        s.append("\"");
        s.append(", auditTs=\"");
        s.append(getAuditTs());
        s.append("\"");
        s.append(", auditLogonId=\"");
        s.append(getAuditLogonId());
        s.append("\"");
        s.append(", auditProcessTxLen=\"");
        s.append(getAuditProcessTxLen());
        s.append("\"");
        s.append(", auditProcessTxText=\"");
        s.append(getAuditProcessTxText());
        s.append("\"");
        s.append(", auditDeleteFl=\"");
        s.append(getAuditDeleteFl());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(Dclvwmcpcd that) {
        return this.cpuId.equals(that.cpuId) &&
            this.cpuCode.equals(that.cpuCode) &&
            this.distNo == that.distNo &&
            this.mfgNo == that.mfgNo &&
            this.cpuStatCode == that.cpuStatCode &&
            this.prodCode.equals(that.prodCode) &&
            this.distLocNo == that.distLocNo &&
            this.mfgLocNo == that.mfgLocNo &&
            this.postingBranchNo == that.postingBranchNo &&
            this.altCpuCode.equals(that.altCpuCode) &&
            this.altCntlBranchNo == that.altCntlBranchNo &&
            this.auditTs.equals(that.auditTs) &&
            this.auditLogonId.equals(that.auditLogonId) &&
            this.auditProcessTxLen == that.auditProcessTxLen &&
            this.auditProcessTxText.equals(that.auditProcessTxText) &&
            this.auditDeleteFl == that.auditDeleteFl;
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof Dclvwmcpcd other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof Dclvwmcpcd;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Objects.hashCode(cpuId);
        result = 31 * result + Objects.hashCode(cpuCode);
        result = 31 * result + Integer.hashCode(distNo);
        result = 31 * result + Integer.hashCode(mfgNo);
        result = 31 * result + Character.hashCode(cpuStatCode);
        result = 31 * result + Objects.hashCode(prodCode);
        result = 31 * result + Integer.hashCode(distLocNo);
        result = 31 * result + Integer.hashCode(mfgLocNo);
        result = 31 * result + Integer.hashCode(postingBranchNo);
        result = 31 * result + Objects.hashCode(altCpuCode);
        result = 31 * result + Integer.hashCode(altCntlBranchNo);
        result = 31 * result + Objects.hashCode(auditTs);
        result = 31 * result + Objects.hashCode(auditLogonId);
        result = 31 * result + Integer.hashCode(auditProcessTxLen);
        result = 31 * result + Objects.hashCode(auditProcessTxText);
        result = 31 * result + Character.hashCode(auditDeleteFl);
        return result;
    }
    
    @Override
    public int compareTo(Dclvwmcpcd that) {
        int c = 0;
        c = this.cpuId.compareTo(that.cpuId);
        if ( c != 0 ) return c;
        c = this.cpuCode.compareTo(that.cpuCode);
        if ( c != 0 ) return c;
        c = Integer.compare(this.distNo, that.distNo);
        if ( c != 0 ) return c;
        c = Integer.compare(this.mfgNo, that.mfgNo);
        if ( c != 0 ) return c;
        c = Character.compare(this.cpuStatCode, that.cpuStatCode);
        if ( c != 0 ) return c;
        c = this.prodCode.compareTo(that.prodCode);
        if ( c != 0 ) return c;
        c = Integer.compare(this.distLocNo, that.distLocNo);
        if ( c != 0 ) return c;
        c = Integer.compare(this.mfgLocNo, that.mfgLocNo);
        if ( c != 0 ) return c;
        c = Integer.compare(this.postingBranchNo, that.postingBranchNo);
        if ( c != 0 ) return c;
        c = this.altCpuCode.compareTo(that.altCpuCode);
        if ( c != 0 ) return c;
        c = Integer.compare(this.altCntlBranchNo, that.altCntlBranchNo);
        if ( c != 0 ) return c;
        c = this.auditTs.compareTo(that.auditTs);
        if ( c != 0 ) return c;
        c = this.auditLogonId.compareTo(that.auditLogonId);
        if ( c != 0 ) return c;
        c = Integer.compare(this.auditProcessTxLen, that.auditProcessTxLen);
        if ( c != 0 ) return c;
        c = this.auditProcessTxText.compareTo(that.auditProcessTxText);
        if ( c != 0 ) return c;
        c = Character.compare(this.auditDeleteFl, that.auditDeleteFl);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final StringField CPU_ID = factory.getStringField(4);
    private static final StringField CPU_CODE = factory.getStringField(4);
    private static final BinaryAsIntField DIST_NO = factory.getBinaryAsIntField(9, true);
    private static final BinaryAsIntField MFG_NO = factory.getBinaryAsIntField(9, true);
    private static final StringField CPU_STAT_CODE = factory.getStringField(1);
    private static final StringField PROD_CODE = factory.getStringField(4);
    private static final BinaryAsIntField DIST_LOC_NO = factory.getBinaryAsIntField(4, true);
    private static final BinaryAsIntField MFG_LOC_NO = factory.getBinaryAsIntField(4, true);
    private static final BinaryAsIntField POSTING_BRANCH_NO = factory.getBinaryAsIntField(4, true);
    private static final StringField ALT_CPU_CODE = factory.getStringField(4);
    private static final BinaryAsIntField ALT_CNTL_BRANCH_NO = factory.getBinaryAsIntField(4, true);
    private static final StringField AUDIT_TS = factory.getStringField(26);
    private static final StringField AUDIT_LOGON_ID = factory.getStringField(8);
    private static final BinaryAsIntField AUDIT_PROCESS_TX_LEN = factory.getBinaryAsIntField(4, true);
    private static final StringField AUDIT_PROCESS_TX_TEXT = factory.getStringField(30);
    private static final StringField AUDIT_DELETE_FL = factory.getStringField(1);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code Dclvwmcpcd} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code DCLVWMCPCD} record
     * @see "DCLVWMCPCD record at VWMCPCD.CPY:29"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        CPU_ID.putString(cpuId, bytes, offset);
        CPU_CODE.putString(cpuCode, bytes, offset);
        DIST_NO.putInt(distNo, bytes, offset);
        MFG_NO.putInt(mfgNo, bytes, offset);
        CPU_STAT_CODE.putString(Character.toString(cpuStatCode), bytes, offset);
        PROD_CODE.putString(prodCode, bytes, offset);
        DIST_LOC_NO.putInt(distLocNo, bytes, offset);
        MFG_LOC_NO.putInt(mfgLocNo, bytes, offset);
        POSTING_BRANCH_NO.putInt(postingBranchNo, bytes, offset);
        ALT_CPU_CODE.putString(altCpuCode, bytes, offset);
        ALT_CNTL_BRANCH_NO.putInt(altCntlBranchNo, bytes, offset);
        AUDIT_TS.putString(auditTs, bytes, offset);
        AUDIT_LOGON_ID.putString(auditLogonId, bytes, offset);
        AUDIT_PROCESS_TX_LEN.putInt(auditProcessTxLen, bytes, offset);
        AUDIT_PROCESS_TX_TEXT.putString(auditProcessTxText, bytes, offset);
        AUDIT_DELETE_FL.putString(Character.toString(auditDeleteFl), bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code Dclvwmcpcd} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code Dclvwmcpcd} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code Dclvwmcpcd} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code DCLVWMCPCD} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "DCLVWMCPCD record at VWMCPCD.CPY:29"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        cpuId = CPU_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        cpuCode = CPU_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        distNo = DIST_NO.getInt(bytes, offset);
        mfgNo = MFG_NO.getInt(bytes, offset);
        cpuStatCode = CPU_STAT_CODE.getString(bytes, offset).charAt(0);
        prodCode = PROD_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        distLocNo = DIST_LOC_NO.getInt(bytes, offset);
        mfgLocNo = MFG_LOC_NO.getInt(bytes, offset);
        postingBranchNo = POSTING_BRANCH_NO.getInt(bytes, offset);
        altCpuCode = ALT_CPU_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        altCntlBranchNo = ALT_CNTL_BRANCH_NO.getInt(bytes, offset);
        auditTs = AUDIT_TS.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        auditLogonId = AUDIT_LOGON_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        auditProcessTxLen = AUDIT_PROCESS_TX_LEN.getInt(bytes, offset);
        auditProcessTxText = AUDIT_PROCESS_TX_TEXT.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        auditDeleteFl = AUDIT_DELETE_FL.getString(bytes, offset).charAt(0);
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
