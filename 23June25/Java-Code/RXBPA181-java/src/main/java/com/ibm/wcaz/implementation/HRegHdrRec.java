package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class HRegHdrRec extends CpuRegdtlLayout {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private static final String H_HDR_REC_VALUE = "URBH";
    private static final String H_DOC_DATA_VALUE = "UNIT REGISTRATION       ";
    
    private String hRecordType = "";
    private String hCdfCustId = "";
    private String hCdfCustCode = "";
    private String hCreatDate = "";
    private String hCreatTime = "";
    private String hDocType = "";
    private String hMfgName = "";
    
    /** Initialize fields to non-null default values */
    public HRegHdrRec() {}
    
    /** Initialize all fields to provided values */
    public HRegHdrRec(String hRecordType, String hCdfCustId, String hCdfCustCode, String hCreatDate, String hCreatTime, String hDocType, String hMfgName) {
        this.hRecordType = hRecordType;
        this.hCdfCustId = hCdfCustId;
        this.hCdfCustCode = hCdfCustCode;
        this.hCreatDate = hCreatDate;
        this.hCreatTime = hCreatTime;
        this.hDocType = hDocType;
        this.hMfgName = hMfgName;
    }
    
    @Override
    public HRegHdrRec clone() throws CloneNotSupportedException {
        HRegHdrRec cloned = (HRegHdrRec) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code HRegHdrRec} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected HRegHdrRec(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code HRegHdrRec} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected HRegHdrRec(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code HRegHdrRec} object
     * @see #setBytes(byte[], int)
     */
    public static HRegHdrRec fromBytes(byte[] bytes, int offset) {
        return new HRegHdrRec(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code HRegHdrRec} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static HRegHdrRec fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code HRegHdrRec} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static HRegHdrRec fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getHRecordType() {
        return this.hRecordType;
    }
    
    public void setHRecordType(String hRecordType) {
        this.hRecordType = hRecordType;
    }
    
    public boolean isHHdrRec() {
        return hRecordType.equals(H_HDR_REC_VALUE);
    }
    
    public void setHHdrRec() {
        hRecordType = H_HDR_REC_VALUE;
    }
    
    public String getHCdfCustId() {
        return this.hCdfCustId;
    }
    
    public void setHCdfCustId(String hCdfCustId) {
        this.hCdfCustId = hCdfCustId;
    }
    
    public String getHCdfCustCode() {
        return this.hCdfCustCode;
    }
    
    public void setHCdfCustCode(String hCdfCustCode) {
        this.hCdfCustCode = hCdfCustCode;
    }
    
    public String getHCreatDate() {
        return this.hCreatDate;
    }
    
    public void setHCreatDate(String hCreatDate) {
        this.hCreatDate = hCreatDate;
    }
    
    public String getHCreatTime() {
        return this.hCreatTime;
    }
    
    public void setHCreatTime(String hCreatTime) {
        this.hCreatTime = hCreatTime;
    }
    
    public String getHDocType() {
        return this.hDocType;
    }
    
    public void setHDocType(String hDocType) {
        this.hDocType = hDocType;
    }
    
    public boolean isHDocData() {
        return hDocType.equals(H_DOC_DATA_VALUE);
    }
    
    public void setHDocData() {
        hDocType = H_DOC_DATA_VALUE;
    }
    
    public String getHMfgName() {
        return this.hMfgName;
    }
    
    public void setHMfgName(String hMfgName) {
        this.hMfgName = hMfgName;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        hRecordType = "";
        hCdfCustId = "";
        hCdfCustCode = "";
        hCreatDate = "";
        hCreatTime = "";
        hDocType = "";
        hMfgName = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ hRecordType=\"");
        s.append(getHRecordType());
        s.append("\"");
        s.append(", hCdfCustId=\"");
        s.append(getHCdfCustId());
        s.append("\"");
        s.append(", hCdfCustCode=\"");
        s.append(getHCdfCustCode());
        s.append("\"");
        s.append(", hCreatDate=\"");
        s.append(getHCreatDate());
        s.append("\"");
        s.append(", hCreatTime=\"");
        s.append(getHCreatTime());
        s.append("\"");
        s.append(", hDocType=\"");
        s.append(getHDocType());
        s.append("\"");
        s.append(", hMfgName=\"");
        s.append(getHMfgName());
        s.append("\"");
        s.append(", filler3=\"");
        s.append(new String(filler3, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(HRegHdrRec that) {
        return super.equals(that) &&
            this.hRecordType.equals(that.hRecordType) &&
            this.hCdfCustId.equals(that.hCdfCustId) &&
            this.hCdfCustCode.equals(that.hCdfCustCode) &&
            this.hCreatDate.equals(that.hCreatDate) &&
            this.hCreatTime.equals(that.hCreatTime) &&
            this.hDocType.equals(that.hDocType) &&
            this.hMfgName.equals(that.hMfgName) &&
            Arrays.equals(this.filler3, that.filler3);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof HRegHdrRec other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof HRegHdrRec;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(hRecordType);
        result = 31 * result + Objects.hashCode(hCdfCustId);
        result = 31 * result + Objects.hashCode(hCdfCustCode);
        result = 31 * result + Objects.hashCode(hCreatDate);
        result = 31 * result + Objects.hashCode(hCreatTime);
        result = 31 * result + Objects.hashCode(hDocType);
        result = 31 * result + Objects.hashCode(hMfgName);
        result = 31 * result + Arrays.hashCode(filler3);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(HRegHdrRec that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.hRecordType.compareTo(that.hRecordType);
        if ( c != 0 ) return c;
        c = this.hCdfCustId.compareTo(that.hCdfCustId);
        if ( c != 0 ) return c;
        c = this.hCdfCustCode.compareTo(that.hCdfCustCode);
        if ( c != 0 ) return c;
        c = this.hCreatDate.compareTo(that.hCreatDate);
        if ( c != 0 ) return c;
        c = this.hCreatTime.compareTo(that.hCreatTime);
        if ( c != 0 ) return c;
        c = this.hDocType.compareTo(that.hDocType);
        if ( c != 0 ) return c;
        c = this.hMfgName.compareTo(that.hMfgName);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler3, that.filler3);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(CpuRegdtlLayout that) {
        if (that instanceof HRegHdrRec other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(CpuRegdtlLayout.SIZE);
    }
    
    private static final StringField H_RECORD_TYPE = factory.getStringField(4);
    private static final StringField H_CDF_CUST_ID = factory.getStringField(4);
    private static final StringField H_CDF_CUST_CODE = factory.getStringField(4);
    private static final StringField H_CREAT_DATE = factory.getStringField(10);
    private static final StringField H_CREAT_TIME = factory.getStringField(5);
    private static final StringField H_DOC_TYPE = factory.getStringField(24);
    private static final StringField H_MFG_NAME = factory.getStringField(20);
    private static final ByteArrayField FILLER_3 = factory.getByteArrayField(129);
    private byte[] filler3 = new byte[129];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link CpuRegdtlLayout#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "H-REG-HDR-REC record at RXBPASVC.cbl:46"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        H_RECORD_TYPE.putString(hRecordType, bytes, offset);
        H_CDF_CUST_ID.putString(hCdfCustId, bytes, offset);
        H_CDF_CUST_CODE.putString(hCdfCustCode, bytes, offset);
        H_CREAT_DATE.putString(hCreatDate, bytes, offset);
        H_CREAT_TIME.putString(hCreatTime, bytes, offset);
        H_DOC_TYPE.putString(hDocType, bytes, offset);
        H_MFG_NAME.putString(hMfgName, bytes, offset);
        FILLER_3.putByteArray(filler3, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link CpuRegdtlLayout#setBytes(byte[], int)} to set parent-class state.
     * @see "H-REG-HDR-REC record at RXBPASVC.cbl:46"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        hRecordType = H_RECORD_TYPE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        hCdfCustId = H_CDF_CUST_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        hCdfCustCode = H_CDF_CUST_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        hCreatDate = H_CREAT_DATE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        hCreatTime = H_CREAT_TIME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        hDocType = H_DOC_TYPE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        hMfgName = H_MFG_NAME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_3.getByteArray(bytes, offset);
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
