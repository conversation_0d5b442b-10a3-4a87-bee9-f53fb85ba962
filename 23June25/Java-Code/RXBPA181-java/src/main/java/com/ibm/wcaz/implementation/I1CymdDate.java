package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class I1CymdDate extends DateParmArea {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String i1CymdCc = "";
    private String i1CymdYy = "";
    private String i1CymdMm = "";
    private String i1CymdDd = "";
    
    /** Initialize fields to non-null default values */
    public I1CymdDate() {}
    
    /** Initialize all fields to provided values */
    public I1CymdDate(int reqDay, int manipulateReqType, int numberOfDays, int numberOfMonths, int inputDateType, int oIsoCc, int oIsoYy, int oIsoMm, int oIsoDd, int oUsaMm, int oUsaDd, int oUsaCc, int oUsaYy, int oEurDd, int oEurMm, int oEurCc, int oEurYy, int oJisCc, int oJisYy, int oJisMm, int oJisDd, int oMdySlshMm, int oMdySlshDd, int oMdySlshYy, int oMdyMm, int oMdyDd, int oMdyYy, int oMdcySlshMm, int oMdcySlshDd, int oMdcySlshCc, int oMdcySlshYy, int oMdcyMm, int oMdcyDd, int oMdcyCc, int oMdcyYy, int oYmdYy, int oYmdMm, int oYmdDd, int oCymdCc, int oCymdYy, int oCymdMm, int oCymdDd, int oJulYy, int oJulDdd, String oExpMm, int oExpDd, int oExpCc, int oExpYy, int differenceInDays, int oDayNumber, String reqCountry, OutputNextMoYr outputNextMoYr, OutputLastMoYr outputLastMoYr, String i1CymdCc, String i1CymdYy, String i1CymdMm, String i1CymdDd) {
        super(reqDay, manipulateReqType, numberOfDays, numberOfMonths, inputDateType, oIsoCc, oIsoYy, oIsoMm, oIsoDd, oUsaMm, oUsaDd, oUsaCc, oUsaYy, oEurDd, oEurMm, oEurCc, oEurYy, oJisCc, oJisYy, oJisMm, oJisDd, oMdySlshMm, oMdySlshDd, oMdySlshYy, oMdyMm, oMdyDd, oMdyYy, oMdcySlshMm, oMdcySlshDd, oMdcySlshCc, oMdcySlshYy, oMdcyMm, oMdcyDd, oMdcyCc, oMdcyYy, oYmdYy, oYmdMm, oYmdDd, oCymdCc, oCymdYy, oCymdMm, oCymdDd, oJulYy, oJulDdd, oExpMm, oExpDd, oExpCc, oExpYy, differenceInDays, oDayNumber, reqCountry, outputNextMoYr, outputLastMoYr);
        this.i1CymdCc = i1CymdCc;
        this.i1CymdYy = i1CymdYy;
        this.i1CymdMm = i1CymdMm;
        this.i1CymdDd = i1CymdDd;
    }
    
    @Override
    public I1CymdDate clone() throws CloneNotSupportedException {
        I1CymdDate cloned = (I1CymdDate) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code I1CymdDate} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected I1CymdDate(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code I1CymdDate} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected I1CymdDate(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I1CymdDate} object
     * @see #setBytes(byte[], int)
     */
    public static I1CymdDate fromBytes(byte[] bytes, int offset) {
        return new I1CymdDate(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I1CymdDate} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static I1CymdDate fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code I1CymdDate} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static I1CymdDate fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getI1CymdCc() {
        return this.i1CymdCc;
    }
    
    public void setI1CymdCc(String i1CymdCc) {
        this.i1CymdCc = i1CymdCc;
    }
    
    public String getI1CymdYy() {
        return this.i1CymdYy;
    }
    
    public void setI1CymdYy(String i1CymdYy) {
        this.i1CymdYy = i1CymdYy;
    }
    
    public String getI1CymdMm() {
        return this.i1CymdMm;
    }
    
    public void setI1CymdMm(String i1CymdMm) {
        this.i1CymdMm = i1CymdMm;
    }
    
    public String getI1CymdDd() {
        return this.i1CymdDd;
    }
    
    public void setI1CymdDd(String i1CymdDd) {
        this.i1CymdDd = i1CymdDd;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        i1CymdCc = "";
        i1CymdYy = "";
        i1CymdMm = "";
        i1CymdDd = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ i1CymdCc=\"");
        s.append(getI1CymdCc());
        s.append("\"");
        s.append(", i1CymdYy=\"");
        s.append(getI1CymdYy());
        s.append("\"");
        s.append(", i1CymdMm=\"");
        s.append(getI1CymdMm());
        s.append("\"");
        s.append(", i1CymdDd=\"");
        s.append(getI1CymdDd());
        s.append("\"");
        s.append(", filler45=\"");
        s.append(new String(filler45, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(I1CymdDate that) {
        return super.equals(that) &&
            this.i1CymdCc.equals(that.i1CymdCc) &&
            this.i1CymdYy.equals(that.i1CymdYy) &&
            this.i1CymdMm.equals(that.i1CymdMm) &&
            this.i1CymdDd.equals(that.i1CymdDd) &&
            Arrays.equals(this.filler45, that.filler45);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof I1CymdDate other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof I1CymdDate;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(i1CymdCc);
        result = 31 * result + Objects.hashCode(i1CymdYy);
        result = 31 * result + Objects.hashCode(i1CymdMm);
        result = 31 * result + Objects.hashCode(i1CymdDd);
        result = 31 * result + Arrays.hashCode(filler45);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(I1CymdDate that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.i1CymdCc.compareTo(that.i1CymdCc);
        if ( c != 0 ) return c;
        c = this.i1CymdYy.compareTo(that.i1CymdYy);
        if ( c != 0 ) return c;
        c = this.i1CymdMm.compareTo(that.i1CymdMm);
        if ( c != 0 ) return c;
        c = this.i1CymdDd.compareTo(that.i1CymdDd);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler45, that.filler45);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(DateParmArea that) {
        if (that instanceof I1CymdDate other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(DateParmArea.SIZE);
    }
    
    private static final StringField I_1_CYMD_CC = factory.getStringField(2);
    private static final StringField I_1_CYMD_YY = factory.getStringField(2);
    private static final StringField I_1_CYMD_MM = factory.getStringField(2);
    private static final StringField I_1_CYMD_DD = factory.getStringField(2);
    private static final ByteArrayField FILLER_45 = factory.getByteArrayField(4);
    private byte[] filler45 = new byte[4];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "I1-CYMD-DATE record at MXWW01.CPY:161"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        I_1_CYMD_CC.putString(i1CymdCc, bytes, offset);
        I_1_CYMD_YY.putString(i1CymdYy, bytes, offset);
        I_1_CYMD_MM.putString(i1CymdMm, bytes, offset);
        I_1_CYMD_DD.putString(i1CymdDd, bytes, offset);
        FILLER_45.putByteArray(filler45, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#setBytes(byte[], int)} to set parent-class state.
     * @see "I1-CYMD-DATE record at MXWW01.CPY:161"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        i1CymdCc = I_1_CYMD_CC.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        i1CymdYy = I_1_CYMD_YY.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        i1CymdMm = I_1_CYMD_MM.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        i1CymdDd = I_1_CYMD_DD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_45.getByteArray(bytes, offset);
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
