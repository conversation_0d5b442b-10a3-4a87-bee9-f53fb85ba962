package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class I2MdcySlshDate extends DateParmArea {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String i2MdcySlshMm = "";
    private String i2MdcySlshDd = "";
    private String i2MdcySlshCc = "";
    private String i2MdcySlshYy = "";
    
    /** Initialize fields to non-null default values */
    public I2MdcySlshDate() {}
    
    /** Initialize all fields to provided values */
    public I2MdcySlshDate(int reqDay, int manipulateReqType, int numberOfDays, int numberOfMonths, int inputDateType, int oIsoCc, int oIsoYy, int oIsoMm, int oIsoDd, int oUsaMm, int oUsaDd, int oUsaCc, int oUsaYy, int oEurDd, int oEurMm, int oEurCc, int oEurYy, int oJisCc, int oJisYy, int oJisMm, int oJisDd, int oMdySlshMm, int oMdySlshDd, int oMdySlshYy, int oMdyMm, int oMdyDd, int oMdyYy, int oMdcySlshMm, int oMdcySlshDd, int oMdcySlshCc, int oMdcySlshYy, int oMdcyMm, int oMdcyDd, int oMdcyCc, int oMdcyYy, int oYmdYy, int oYmdMm, int oYmdDd, int oCymdCc, int oCymdYy, int oCymdMm, int oCymdDd, int oJulYy, int oJulDdd, String oExpMm, int oExpDd, int oExpCc, int oExpYy, int differenceInDays, int oDayNumber, String reqCountry, OutputNextMoYr outputNextMoYr, OutputLastMoYr outputLastMoYr, String i2MdcySlshMm, String i2MdcySlshDd, String i2MdcySlshCc, String i2MdcySlshYy) {
        super(reqDay, manipulateReqType, numberOfDays, numberOfMonths, inputDateType, oIsoCc, oIsoYy, oIsoMm, oIsoDd, oUsaMm, oUsaDd, oUsaCc, oUsaYy, oEurDd, oEurMm, oEurCc, oEurYy, oJisCc, oJisYy, oJisMm, oJisDd, oMdySlshMm, oMdySlshDd, oMdySlshYy, oMdyMm, oMdyDd, oMdyYy, oMdcySlshMm, oMdcySlshDd, oMdcySlshCc, oMdcySlshYy, oMdcyMm, oMdcyDd, oMdcyCc, oMdcyYy, oYmdYy, oYmdMm, oYmdDd, oCymdCc, oCymdYy, oCymdMm, oCymdDd, oJulYy, oJulDdd, oExpMm, oExpDd, oExpCc, oExpYy, differenceInDays, oDayNumber, reqCountry, outputNextMoYr, outputLastMoYr);
        this.i2MdcySlshMm = i2MdcySlshMm;
        this.i2MdcySlshDd = i2MdcySlshDd;
        this.i2MdcySlshCc = i2MdcySlshCc;
        this.i2MdcySlshYy = i2MdcySlshYy;
    }
    
    @Override
    public I2MdcySlshDate clone() throws CloneNotSupportedException {
        I2MdcySlshDate cloned = (I2MdcySlshDate) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code I2MdcySlshDate} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected I2MdcySlshDate(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code I2MdcySlshDate} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected I2MdcySlshDate(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I2MdcySlshDate} object
     * @see #setBytes(byte[], int)
     */
    public static I2MdcySlshDate fromBytes(byte[] bytes, int offset) {
        return new I2MdcySlshDate(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I2MdcySlshDate} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static I2MdcySlshDate fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code I2MdcySlshDate} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static I2MdcySlshDate fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getI2MdcySlshMm() {
        return this.i2MdcySlshMm;
    }
    
    public void setI2MdcySlshMm(String i2MdcySlshMm) {
        this.i2MdcySlshMm = i2MdcySlshMm;
    }
    
    public String getI2MdcySlshDd() {
        return this.i2MdcySlshDd;
    }
    
    public void setI2MdcySlshDd(String i2MdcySlshDd) {
        this.i2MdcySlshDd = i2MdcySlshDd;
    }
    
    public String getI2MdcySlshCc() {
        return this.i2MdcySlshCc;
    }
    
    public void setI2MdcySlshCc(String i2MdcySlshCc) {
        this.i2MdcySlshCc = i2MdcySlshCc;
    }
    
    public String getI2MdcySlshYy() {
        return this.i2MdcySlshYy;
    }
    
    public void setI2MdcySlshYy(String i2MdcySlshYy) {
        this.i2MdcySlshYy = i2MdcySlshYy;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        i2MdcySlshMm = "";
        i2MdcySlshDd = "";
        i2MdcySlshCc = "";
        i2MdcySlshYy = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ i2MdcySlshMm=\"");
        s.append(getI2MdcySlshMm());
        s.append("\"");
        s.append(", filler65=\"");
        s.append(new String(filler65, encoding));
        s.append("\"");
        s.append(", i2MdcySlshDd=\"");
        s.append(getI2MdcySlshDd());
        s.append("\"");
        s.append(", filler66=\"");
        s.append(new String(filler66, encoding));
        s.append("\"");
        s.append(", i2MdcySlshCc=\"");
        s.append(getI2MdcySlshCc());
        s.append("\"");
        s.append(", i2MdcySlshYy=\"");
        s.append(getI2MdcySlshYy());
        s.append("\"");
        s.append(", filler67=\"");
        s.append(new String(filler67, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(I2MdcySlshDate that) {
        return super.equals(that) &&
            this.i2MdcySlshMm.equals(that.i2MdcySlshMm) &&
            Arrays.equals(this.filler65, that.filler65) &&
            this.i2MdcySlshDd.equals(that.i2MdcySlshDd) &&
            Arrays.equals(this.filler66, that.filler66) &&
            this.i2MdcySlshCc.equals(that.i2MdcySlshCc) &&
            this.i2MdcySlshYy.equals(that.i2MdcySlshYy) &&
            Arrays.equals(this.filler67, that.filler67);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof I2MdcySlshDate other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof I2MdcySlshDate;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(i2MdcySlshMm);
        result = 31 * result + Arrays.hashCode(filler65);
        result = 31 * result + Objects.hashCode(i2MdcySlshDd);
        result = 31 * result + Arrays.hashCode(filler66);
        result = 31 * result + Objects.hashCode(i2MdcySlshCc);
        result = 31 * result + Objects.hashCode(i2MdcySlshYy);
        result = 31 * result + Arrays.hashCode(filler67);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(I2MdcySlshDate that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.i2MdcySlshMm.compareTo(that.i2MdcySlshMm);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler65, that.filler65);
        if ( c != 0 ) return c;
        c = this.i2MdcySlshDd.compareTo(that.i2MdcySlshDd);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler66, that.filler66);
        if ( c != 0 ) return c;
        c = this.i2MdcySlshCc.compareTo(that.i2MdcySlshCc);
        if ( c != 0 ) return c;
        c = this.i2MdcySlshYy.compareTo(that.i2MdcySlshYy);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler67, that.filler67);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(DateParmArea that) {
        if (that instanceof I2MdcySlshDate other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(DateParmArea.SIZE);
    }
    
    private static final StringField I_2_MDCY_SLSH_MM = factory.getStringField(2);
    private static final ByteArrayField FILLER_65 = factory.getByteArrayField(1);
    private byte[] filler65 = new byte[1];
    private static final StringField I_2_MDCY_SLSH_DD = factory.getStringField(2);
    private static final ByteArrayField FILLER_66 = factory.getByteArrayField(1);
    private byte[] filler66 = new byte[1];
    private static final StringField I_2_MDCY_SLSH_CC = factory.getStringField(2);
    private static final StringField I_2_MDCY_SLSH_YY = factory.getStringField(2);
    private static final ByteArrayField FILLER_67 = factory.getByteArrayField(2);
    private byte[] filler67 = new byte[2];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "I2-MDCY-SLSH-DATE record at MXWW01.CPY:229"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        I_2_MDCY_SLSH_MM.putString(i2MdcySlshMm, bytes, offset);
        FILLER_65.putByteArray(filler65, bytes, offset);
        I_2_MDCY_SLSH_DD.putString(i2MdcySlshDd, bytes, offset);
        FILLER_66.putByteArray(filler66, bytes, offset);
        I_2_MDCY_SLSH_CC.putString(i2MdcySlshCc, bytes, offset);
        I_2_MDCY_SLSH_YY.putString(i2MdcySlshYy, bytes, offset);
        FILLER_67.putByteArray(filler67, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#setBytes(byte[], int)} to set parent-class state.
     * @see "I2-MDCY-SLSH-DATE record at MXWW01.CPY:229"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        i2MdcySlshMm = I_2_MDCY_SLSH_MM.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_65.getByteArray(bytes, offset);
        i2MdcySlshDd = I_2_MDCY_SLSH_DD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_66.getByteArray(bytes, offset);
        i2MdcySlshCc = I_2_MDCY_SLSH_CC.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        i2MdcySlshYy = I_2_MDCY_SLSH_YY.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_67.getByteArray(bytes, offset);
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
