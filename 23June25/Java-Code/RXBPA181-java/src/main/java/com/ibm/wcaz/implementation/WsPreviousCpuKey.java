package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class WsPreviousCpuKey implements Cloneable, Comparable<WsPreviousCpuKey> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String wsPreviousCdfCustId = "";
    private String wsPreviousCdfCustCode = "";
    private String wsPreviousCreatDate = "";
    private String wsPreviousCreatTime = "";
    
    /** Initialize fields to non-null default values */
    public WsPreviousCpuKey() {}
    
    /** Initialize all fields to provided values */
    public WsPreviousCpuKey(String wsPreviousCdfCustId, String wsPreviousCdfCustCode, String wsPreviousCreatDate, String wsPreviousCreatTime) {
        this.wsPreviousCdfCustId = wsPreviousCdfCustId;
        this.wsPreviousCdfCustCode = wsPreviousCdfCustCode;
        this.wsPreviousCreatDate = wsPreviousCreatDate;
        this.wsPreviousCreatTime = wsPreviousCreatTime;
    }
    
    @Override
    public WsPreviousCpuKey clone() throws CloneNotSupportedException {
        WsPreviousCpuKey cloned = (WsPreviousCpuKey) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code WsPreviousCpuKey} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected WsPreviousCpuKey(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code WsPreviousCpuKey} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected WsPreviousCpuKey(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsPreviousCpuKey} object
     * @see #setBytes(byte[], int)
     */
    public static WsPreviousCpuKey fromBytes(byte[] bytes, int offset) {
        return new WsPreviousCpuKey(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsPreviousCpuKey} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static WsPreviousCpuKey fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code WsPreviousCpuKey} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static WsPreviousCpuKey fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getWsPreviousCdfCustId() {
        return this.wsPreviousCdfCustId;
    }
    
    public void setWsPreviousCdfCustId(String wsPreviousCdfCustId) {
        this.wsPreviousCdfCustId = wsPreviousCdfCustId;
    }
    
    public String getWsPreviousCdfCustCode() {
        return this.wsPreviousCdfCustCode;
    }
    
    public void setWsPreviousCdfCustCode(String wsPreviousCdfCustCode) {
        this.wsPreviousCdfCustCode = wsPreviousCdfCustCode;
    }
    
    public String getWsPreviousCreatDate() {
        return this.wsPreviousCreatDate;
    }
    
    public void setWsPreviousCreatDate(String wsPreviousCreatDate) {
        this.wsPreviousCreatDate = wsPreviousCreatDate;
    }
    
    public String getWsPreviousCreatTime() {
        return this.wsPreviousCreatTime;
    }
    
    public void setWsPreviousCreatTime(String wsPreviousCreatTime) {
        this.wsPreviousCreatTime = wsPreviousCreatTime;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        wsPreviousCdfCustId = "";
        wsPreviousCdfCustCode = "";
        wsPreviousCreatDate = "";
        wsPreviousCreatTime = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ wsPreviousCdfCustId=\"");
        s.append(getWsPreviousCdfCustId());
        s.append("\"");
        s.append(", wsPreviousCdfCustCode=\"");
        s.append(getWsPreviousCdfCustCode());
        s.append("\"");
        s.append(", wsPreviousCreatDate=\"");
        s.append(getWsPreviousCreatDate());
        s.append("\"");
        s.append(", wsPreviousCreatTime=\"");
        s.append(getWsPreviousCreatTime());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(WsPreviousCpuKey that) {
        return this.wsPreviousCdfCustId.equals(that.wsPreviousCdfCustId) &&
            this.wsPreviousCdfCustCode.equals(that.wsPreviousCdfCustCode) &&
            this.wsPreviousCreatDate.equals(that.wsPreviousCreatDate) &&
            this.wsPreviousCreatTime.equals(that.wsPreviousCreatTime);
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof WsPreviousCpuKey other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof WsPreviousCpuKey;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Objects.hashCode(wsPreviousCdfCustId);
        result = 31 * result + Objects.hashCode(wsPreviousCdfCustCode);
        result = 31 * result + Objects.hashCode(wsPreviousCreatDate);
        result = 31 * result + Objects.hashCode(wsPreviousCreatTime);
        return result;
    }
    
    @Override
    public int compareTo(WsPreviousCpuKey that) {
        int c = 0;
        c = this.wsPreviousCdfCustId.compareTo(that.wsPreviousCdfCustId);
        if ( c != 0 ) return c;
        c = this.wsPreviousCdfCustCode.compareTo(that.wsPreviousCdfCustCode);
        if ( c != 0 ) return c;
        c = this.wsPreviousCreatDate.compareTo(that.wsPreviousCreatDate);
        if ( c != 0 ) return c;
        c = this.wsPreviousCreatTime.compareTo(that.wsPreviousCreatTime);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final StringField WS_PREVIOUS_CDF_CUST_ID = factory.getStringField(4);
    private static final StringField WS_PREVIOUS_CDF_CUST_CODE = factory.getStringField(4);
    private static final StringField WS_PREVIOUS_CREAT_DATE = factory.getStringField(10);
    private static final StringField WS_PREVIOUS_CREAT_TIME = factory.getStringField(5);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsPreviousCpuKey} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code WS-PREVIOUS-CPU-KEY} record
     * @see "WS-PREVIOUS-CPU-KEY record at RXBPASVC.cbl:143"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        WS_PREVIOUS_CDF_CUST_ID.putString(wsPreviousCdfCustId, bytes, offset);
        WS_PREVIOUS_CDF_CUST_CODE.putString(wsPreviousCdfCustCode, bytes, offset);
        WS_PREVIOUS_CREAT_DATE.putString(wsPreviousCreatDate, bytes, offset);
        WS_PREVIOUS_CREAT_TIME.putString(wsPreviousCreatTime, bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsPreviousCpuKey} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsPreviousCpuKey} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code WsPreviousCpuKey} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code WS-PREVIOUS-CPU-KEY} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "WS-PREVIOUS-CPU-KEY record at RXBPASVC.cbl:143"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        wsPreviousCdfCustId = WS_PREVIOUS_CDF_CUST_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        wsPreviousCdfCustCode = WS_PREVIOUS_CDF_CUST_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        wsPreviousCreatDate = WS_PREVIOUS_CREAT_DATE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        wsPreviousCreatTime = WS_PREVIOUS_CREAT_TIME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
