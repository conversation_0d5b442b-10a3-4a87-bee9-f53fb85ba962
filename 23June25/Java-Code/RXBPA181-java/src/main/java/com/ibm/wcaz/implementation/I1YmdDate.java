package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class I1YmdDate extends DateParmArea {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String i1YmdYy = "";
    private String i1YmdMm = "";
    private String i1YmdDd = "";
    
    /** Initialize fields to non-null default values */
    public I1YmdDate() {}
    
    /** Initialize all fields to provided values */
    public I1YmdDate(int reqDay, int manipulateReqType, int numberOfDays, int numberOfMonths, int inputDateType, int oIsoCc, int oIsoYy, int oIsoMm, int oIsoDd, int oUsaMm, int oUsaDd, int oUsaCc, int oUsaYy, int oEurDd, int oEurMm, int oEurCc, int oEurYy, int oJisCc, int oJisYy, int oJisMm, int oJisDd, int oMdySlshMm, int oMdySlshDd, int oMdySlshYy, int oMdyMm, int oMdyDd, int oMdyYy, int oMdcySlshMm, int oMdcySlshDd, int oMdcySlshCc, int oMdcySlshYy, int oMdcyMm, int oMdcyDd, int oMdcyCc, int oMdcyYy, int oYmdYy, int oYmdMm, int oYmdDd, int oCymdCc, int oCymdYy, int oCymdMm, int oCymdDd, int oJulYy, int oJulDdd, String oExpMm, int oExpDd, int oExpCc, int oExpYy, int differenceInDays, int oDayNumber, String reqCountry, OutputNextMoYr outputNextMoYr, OutputLastMoYr outputLastMoYr, String i1YmdYy, String i1YmdMm, String i1YmdDd) {
        super(reqDay, manipulateReqType, numberOfDays, numberOfMonths, inputDateType, oIsoCc, oIsoYy, oIsoMm, oIsoDd, oUsaMm, oUsaDd, oUsaCc, oUsaYy, oEurDd, oEurMm, oEurCc, oEurYy, oJisCc, oJisYy, oJisMm, oJisDd, oMdySlshMm, oMdySlshDd, oMdySlshYy, oMdyMm, oMdyDd, oMdyYy, oMdcySlshMm, oMdcySlshDd, oMdcySlshCc, oMdcySlshYy, oMdcyMm, oMdcyDd, oMdcyCc, oMdcyYy, oYmdYy, oYmdMm, oYmdDd, oCymdCc, oCymdYy, oCymdMm, oCymdDd, oJulYy, oJulDdd, oExpMm, oExpDd, oExpCc, oExpYy, differenceInDays, oDayNumber, reqCountry, outputNextMoYr, outputLastMoYr);
        this.i1YmdYy = i1YmdYy;
        this.i1YmdMm = i1YmdMm;
        this.i1YmdDd = i1YmdDd;
    }
    
    @Override
    public I1YmdDate clone() throws CloneNotSupportedException {
        I1YmdDate cloned = (I1YmdDate) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code I1YmdDate} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected I1YmdDate(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code I1YmdDate} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected I1YmdDate(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I1YmdDate} object
     * @see #setBytes(byte[], int)
     */
    public static I1YmdDate fromBytes(byte[] bytes, int offset) {
        return new I1YmdDate(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I1YmdDate} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static I1YmdDate fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code I1YmdDate} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static I1YmdDate fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getI1YmdYy() {
        return this.i1YmdYy;
    }
    
    public void setI1YmdYy(String i1YmdYy) {
        this.i1YmdYy = i1YmdYy;
    }
    
    public String getI1YmdMm() {
        return this.i1YmdMm;
    }
    
    public void setI1YmdMm(String i1YmdMm) {
        this.i1YmdMm = i1YmdMm;
    }
    
    public String getI1YmdDd() {
        return this.i1YmdDd;
    }
    
    public void setI1YmdDd(String i1YmdDd) {
        this.i1YmdDd = i1YmdDd;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        i1YmdYy = "";
        i1YmdMm = "";
        i1YmdDd = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ i1YmdYy=\"");
        s.append(getI1YmdYy());
        s.append("\"");
        s.append(", i1YmdMm=\"");
        s.append(getI1YmdMm());
        s.append("\"");
        s.append(", i1YmdDd=\"");
        s.append(getI1YmdDd());
        s.append("\"");
        s.append(", filler44=\"");
        s.append(new String(filler44, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(I1YmdDate that) {
        return super.equals(that) &&
            this.i1YmdYy.equals(that.i1YmdYy) &&
            this.i1YmdMm.equals(that.i1YmdMm) &&
            this.i1YmdDd.equals(that.i1YmdDd) &&
            Arrays.equals(this.filler44, that.filler44);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof I1YmdDate other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof I1YmdDate;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(i1YmdYy);
        result = 31 * result + Objects.hashCode(i1YmdMm);
        result = 31 * result + Objects.hashCode(i1YmdDd);
        result = 31 * result + Arrays.hashCode(filler44);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(I1YmdDate that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.i1YmdYy.compareTo(that.i1YmdYy);
        if ( c != 0 ) return c;
        c = this.i1YmdMm.compareTo(that.i1YmdMm);
        if ( c != 0 ) return c;
        c = this.i1YmdDd.compareTo(that.i1YmdDd);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler44, that.filler44);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(DateParmArea that) {
        if (that instanceof I1YmdDate other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(DateParmArea.SIZE);
    }
    
    private static final StringField I_1_YMD_YY = factory.getStringField(2);
    private static final StringField I_1_YMD_MM = factory.getStringField(2);
    private static final StringField I_1_YMD_DD = factory.getStringField(2);
    private static final ByteArrayField FILLER_44 = factory.getByteArrayField(6);
    private byte[] filler44 = new byte[6];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "I1-YMD-DATE record at MXWW01.CPY:156"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        I_1_YMD_YY.putString(i1YmdYy, bytes, offset);
        I_1_YMD_MM.putString(i1YmdMm, bytes, offset);
        I_1_YMD_DD.putString(i1YmdDd, bytes, offset);
        FILLER_44.putByteArray(filler44, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#setBytes(byte[], int)} to set parent-class state.
     * @see "I1-YMD-DATE record at MXWW01.CPY:156"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        i1YmdYy = I_1_YMD_YY.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        i1YmdMm = I_1_YMD_MM.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        i1YmdDd = I_1_YMD_DD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_44.getByteArray(bytes, offset);
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
