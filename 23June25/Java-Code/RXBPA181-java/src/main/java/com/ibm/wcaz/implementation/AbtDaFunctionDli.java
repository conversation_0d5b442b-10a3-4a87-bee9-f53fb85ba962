package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class AbtDaFunctionDli extends AbtDataAccessInfo {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String abtDaFuncDli = "";
    private String abtDaFuncPcbType = "";
    
    /** Initialize fields to non-null default values */
    public AbtDaFunctionDli() {}
    
    /** Initialize all fields to provided values */
    public AbtDaFunctionDli(String abtU100Sub, String abtDaAccessName, String abtDaGenericStatus, String abtDaFuncDli, String abtDaFuncPcbType) {
        super(abtU100Sub, abtDaAccessName, abtDaGenericStatus);
        this.abtDaFuncDli = abtDaFuncDli;
        this.abtDaFuncPcbType = abtDaFuncPcbType;
    }
    
    @Override
    public AbtDaFunctionDli clone() throws CloneNotSupportedException {
        AbtDaFunctionDli cloned = (AbtDaFunctionDli) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code AbtDaFunctionDli} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected AbtDaFunctionDli(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code AbtDaFunctionDli} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected AbtDaFunctionDli(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code AbtDaFunctionDli} object
     * @see #setBytes(byte[], int)
     */
    public static AbtDaFunctionDli fromBytes(byte[] bytes, int offset) {
        return new AbtDaFunctionDli(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code AbtDaFunctionDli} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static AbtDaFunctionDli fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code AbtDaFunctionDli} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static AbtDaFunctionDli fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getAbtDaFuncDli() {
        return this.abtDaFuncDli;
    }
    
    public void setAbtDaFuncDli(String abtDaFuncDli) {
        this.abtDaFuncDli = abtDaFuncDli;
    }
    
    public String getAbtDaFuncPcbType() {
        return this.abtDaFuncPcbType;
    }
    
    public void setAbtDaFuncPcbType(String abtDaFuncPcbType) {
        this.abtDaFuncPcbType = abtDaFuncPcbType;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        abtDaFuncDli = "";
        abtDaFuncPcbType = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ abtDaFuncDli=\"");
        s.append(getAbtDaFuncDli());
        s.append("\"");
        s.append(", abtDaFuncPcbType=\"");
        s.append(getAbtDaFuncPcbType());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(AbtDaFunctionDli that) {
        return super.equals(that) &&
            this.abtDaFuncDli.equals(that.abtDaFuncDli) &&
            this.abtDaFuncPcbType.equals(that.abtDaFuncPcbType);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof AbtDaFunctionDli other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof AbtDaFunctionDli;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(abtDaFuncDli);
        result = 31 * result + Objects.hashCode(abtDaFuncPcbType);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(AbtDaFunctionDli that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.abtDaFuncDli.compareTo(that.abtDaFuncDli);
        if ( c != 0 ) return c;
        c = this.abtDaFuncPcbType.compareTo(that.abtDaFuncPcbType);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(AbtDataAccessInfo that) {
        if (that instanceof AbtDaFunctionDli other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(AbtDataAccessInfo.SIZE);
    }
    
    private static final StringField ABT_DA_FUNC_DLI = factory.getStringField(4);
    private static final StringField ABT_DA_FUNC_PCB_TYPE = factory.getStringField(4);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link AbtDataAccessInfo#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "ABT-DA-FUNCTION-DLI record at MXWW03.CPY:74"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        ABT_DA_FUNC_DLI.putString(abtDaFuncDli, bytes, offset);
        ABT_DA_FUNC_PCB_TYPE.putString(abtDaFuncPcbType, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link AbtDataAccessInfo#setBytes(byte[], int)} to set parent-class state.
     * @see "ABT-DA-FUNCTION-DLI record at MXWW03.CPY:74"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        abtDaFuncDli = ABT_DA_FUNC_DLI.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        abtDaFuncPcbType = ABT_DA_FUNC_PCB_TYPE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
