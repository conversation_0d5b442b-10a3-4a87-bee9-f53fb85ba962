package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class I2YmdDate extends DateParmArea {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String i2YmdYy = "";
    private String i2YmdMm = "";
    private String i2YmdDd = "";
    
    /** Initialize fields to non-null default values */
    public I2YmdDate() {}
    
    /** Initialize all fields to provided values */
    public I2YmdDate(int reqDay, int manipulateReqType, int numberOfDays, int numberOfMonths, int inputDateType, int oIsoCc, int oIsoYy, int oIsoMm, int oIsoDd, int oUsaMm, int oUsaDd, int oUsaCc, int oUsaYy, int oEurDd, int oEurMm, int oEurCc, int oEurYy, int oJisCc, int oJisYy, int oJisMm, int oJisDd, int oMdySlshMm, int oMdySlshDd, int oMdySlshYy, int oMdyMm, int oMdyDd, int oMdyYy, int oMdcySlshMm, int oMdcySlshDd, int oMdcySlshCc, int oMdcySlshYy, int oMdcyMm, int oMdcyDd, int oMdcyCc, int oMdcyYy, int oYmdYy, int oYmdMm, int oYmdDd, int oCymdCc, int oCymdYy, int oCymdMm, int oCymdDd, int oJulYy, int oJulDdd, String oExpMm, int oExpDd, int oExpCc, int oExpYy, int differenceInDays, int oDayNumber, String reqCountry, OutputNextMoYr outputNextMoYr, OutputLastMoYr outputLastMoYr, String i2YmdYy, String i2YmdMm, String i2YmdDd) {
        super(reqDay, manipulateReqType, numberOfDays, numberOfMonths, inputDateType, oIsoCc, oIsoYy, oIsoMm, oIsoDd, oUsaMm, oUsaDd, oUsaCc, oUsaYy, oEurDd, oEurMm, oEurCc, oEurYy, oJisCc, oJisYy, oJisMm, oJisDd, oMdySlshMm, oMdySlshDd, oMdySlshYy, oMdyMm, oMdyDd, oMdyYy, oMdcySlshMm, oMdcySlshDd, oMdcySlshCc, oMdcySlshYy, oMdcyMm, oMdcyDd, oMdcyCc, oMdcyYy, oYmdYy, oYmdMm, oYmdDd, oCymdCc, oCymdYy, oCymdMm, oCymdDd, oJulYy, oJulDdd, oExpMm, oExpDd, oExpCc, oExpYy, differenceInDays, oDayNumber, reqCountry, outputNextMoYr, outputLastMoYr);
        this.i2YmdYy = i2YmdYy;
        this.i2YmdMm = i2YmdMm;
        this.i2YmdDd = i2YmdDd;
    }
    
    @Override
    public I2YmdDate clone() throws CloneNotSupportedException {
        I2YmdDate cloned = (I2YmdDate) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code I2YmdDate} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected I2YmdDate(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code I2YmdDate} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected I2YmdDate(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I2YmdDate} object
     * @see #setBytes(byte[], int)
     */
    public static I2YmdDate fromBytes(byte[] bytes, int offset) {
        return new I2YmdDate(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I2YmdDate} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static I2YmdDate fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code I2YmdDate} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static I2YmdDate fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getI2YmdYy() {
        return this.i2YmdYy;
    }
    
    public void setI2YmdYy(String i2YmdYy) {
        this.i2YmdYy = i2YmdYy;
    }
    
    public String getI2YmdMm() {
        return this.i2YmdMm;
    }
    
    public void setI2YmdMm(String i2YmdMm) {
        this.i2YmdMm = i2YmdMm;
    }
    
    public String getI2YmdDd() {
        return this.i2YmdDd;
    }
    
    public void setI2YmdDd(String i2YmdDd) {
        this.i2YmdDd = i2YmdDd;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        i2YmdYy = "";
        i2YmdMm = "";
        i2YmdDd = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ i2YmdYy=\"");
        s.append(getI2YmdYy());
        s.append("\"");
        s.append(", i2YmdMm=\"");
        s.append(getI2YmdMm());
        s.append("\"");
        s.append(", i2YmdDd=\"");
        s.append(getI2YmdDd());
        s.append("\"");
        s.append(", filler69=\"");
        s.append(new String(filler69, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(I2YmdDate that) {
        return super.equals(that) &&
            this.i2YmdYy.equals(that.i2YmdYy) &&
            this.i2YmdMm.equals(that.i2YmdMm) &&
            this.i2YmdDd.equals(that.i2YmdDd) &&
            Arrays.equals(this.filler69, that.filler69);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof I2YmdDate other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof I2YmdDate;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(i2YmdYy);
        result = 31 * result + Objects.hashCode(i2YmdMm);
        result = 31 * result + Objects.hashCode(i2YmdDd);
        result = 31 * result + Arrays.hashCode(filler69);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(I2YmdDate that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.i2YmdYy.compareTo(that.i2YmdYy);
        if ( c != 0 ) return c;
        c = this.i2YmdMm.compareTo(that.i2YmdMm);
        if ( c != 0 ) return c;
        c = this.i2YmdDd.compareTo(that.i2YmdDd);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler69, that.filler69);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(DateParmArea that) {
        if (that instanceof I2YmdDate other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(DateParmArea.SIZE);
    }
    
    private static final StringField I_2_YMD_YY = factory.getStringField(2);
    private static final StringField I_2_YMD_MM = factory.getStringField(2);
    private static final StringField I_2_YMD_DD = factory.getStringField(2);
    private static final ByteArrayField FILLER_69 = factory.getByteArrayField(6);
    private byte[] filler69 = new byte[6];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "I2-YMD-DATE record at MXWW01.CPY:245"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        I_2_YMD_YY.putString(i2YmdYy, bytes, offset);
        I_2_YMD_MM.putString(i2YmdMm, bytes, offset);
        I_2_YMD_DD.putString(i2YmdDd, bytes, offset);
        FILLER_69.putByteArray(filler69, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#setBytes(byte[], int)} to set parent-class state.
     * @see "I2-YMD-DATE record at MXWW01.CPY:245"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        i2YmdYy = I_2_YMD_YY.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        i2YmdMm = I_2_YMD_MM.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        i2YmdDd = I_2_YMD_DD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_69.getByteArray(bytes, offset);
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
