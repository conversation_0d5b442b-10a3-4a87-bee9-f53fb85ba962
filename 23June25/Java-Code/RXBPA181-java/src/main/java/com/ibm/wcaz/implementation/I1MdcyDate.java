package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class I1MdcyDate extends DateParmArea {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String i1MdcyMm = "";
    private String i1MdcyDd = "";
    private String i1MdcyCc = "";
    private String i1MdcyYy = "";
    
    /** Initialize fields to non-null default values */
    public I1MdcyDate() {}
    
    /** Initialize all fields to provided values */
    public I1MdcyDate(int reqDay, int manipulateReqType, int numberOfDays, int numberOfMonths, int inputDateType, int oIsoCc, int oIsoYy, int oIsoMm, int oIsoDd, int oUsaMm, int oUsaDd, int oUsaCc, int oUsaYy, int oEurDd, int oEurMm, int oEurCc, int oEurYy, int oJisCc, int oJisYy, int oJisMm, int oJisDd, int oMdySlshMm, int oMdySlshDd, int oMdySlshYy, int oMdyMm, int oMdyDd, int oMdyYy, int oMdcySlshMm, int oMdcySlshDd, int oMdcySlshCc, int oMdcySlshYy, int oMdcyMm, int oMdcyDd, int oMdcyCc, int oMdcyYy, int oYmdYy, int oYmdMm, int oYmdDd, int oCymdCc, int oCymdYy, int oCymdMm, int oCymdDd, int oJulYy, int oJulDdd, String oExpMm, int oExpDd, int oExpCc, int oExpYy, int differenceInDays, int oDayNumber, String reqCountry, OutputNextMoYr outputNextMoYr, OutputLastMoYr outputLastMoYr, String i1MdcyMm, String i1MdcyDd, String i1MdcyCc, String i1MdcyYy) {
        super(reqDay, manipulateReqType, numberOfDays, numberOfMonths, inputDateType, oIsoCc, oIsoYy, oIsoMm, oIsoDd, oUsaMm, oUsaDd, oUsaCc, oUsaYy, oEurDd, oEurMm, oEurCc, oEurYy, oJisCc, oJisYy, oJisMm, oJisDd, oMdySlshMm, oMdySlshDd, oMdySlshYy, oMdyMm, oMdyDd, oMdyYy, oMdcySlshMm, oMdcySlshDd, oMdcySlshCc, oMdcySlshYy, oMdcyMm, oMdcyDd, oMdcyCc, oMdcyYy, oYmdYy, oYmdMm, oYmdDd, oCymdCc, oCymdYy, oCymdMm, oCymdDd, oJulYy, oJulDdd, oExpMm, oExpDd, oExpCc, oExpYy, differenceInDays, oDayNumber, reqCountry, outputNextMoYr, outputLastMoYr);
        this.i1MdcyMm = i1MdcyMm;
        this.i1MdcyDd = i1MdcyDd;
        this.i1MdcyCc = i1MdcyCc;
        this.i1MdcyYy = i1MdcyYy;
    }
    
    @Override
    public I1MdcyDate clone() throws CloneNotSupportedException {
        I1MdcyDate cloned = (I1MdcyDate) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code I1MdcyDate} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected I1MdcyDate(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code I1MdcyDate} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected I1MdcyDate(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I1MdcyDate} object
     * @see #setBytes(byte[], int)
     */
    public static I1MdcyDate fromBytes(byte[] bytes, int offset) {
        return new I1MdcyDate(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I1MdcyDate} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static I1MdcyDate fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code I1MdcyDate} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static I1MdcyDate fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getI1MdcyMm() {
        return this.i1MdcyMm;
    }
    
    public void setI1MdcyMm(String i1MdcyMm) {
        this.i1MdcyMm = i1MdcyMm;
    }
    
    public String getI1MdcyDd() {
        return this.i1MdcyDd;
    }
    
    public void setI1MdcyDd(String i1MdcyDd) {
        this.i1MdcyDd = i1MdcyDd;
    }
    
    public String getI1MdcyCc() {
        return this.i1MdcyCc;
    }
    
    public void setI1MdcyCc(String i1MdcyCc) {
        this.i1MdcyCc = i1MdcyCc;
    }
    
    public String getI1MdcyYy() {
        return this.i1MdcyYy;
    }
    
    public void setI1MdcyYy(String i1MdcyYy) {
        this.i1MdcyYy = i1MdcyYy;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        i1MdcyMm = "";
        i1MdcyDd = "";
        i1MdcyCc = "";
        i1MdcyYy = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ i1MdcyMm=\"");
        s.append(getI1MdcyMm());
        s.append("\"");
        s.append(", i1MdcyDd=\"");
        s.append(getI1MdcyDd());
        s.append("\"");
        s.append(", i1MdcyCc=\"");
        s.append(getI1MdcyCc());
        s.append("\"");
        s.append(", i1MdcyYy=\"");
        s.append(getI1MdcyYy());
        s.append("\"");
        s.append(", filler43=\"");
        s.append(new String(filler43, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(I1MdcyDate that) {
        return super.equals(that) &&
            this.i1MdcyMm.equals(that.i1MdcyMm) &&
            this.i1MdcyDd.equals(that.i1MdcyDd) &&
            this.i1MdcyCc.equals(that.i1MdcyCc) &&
            this.i1MdcyYy.equals(that.i1MdcyYy) &&
            Arrays.equals(this.filler43, that.filler43);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof I1MdcyDate other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof I1MdcyDate;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(i1MdcyMm);
        result = 31 * result + Objects.hashCode(i1MdcyDd);
        result = 31 * result + Objects.hashCode(i1MdcyCc);
        result = 31 * result + Objects.hashCode(i1MdcyYy);
        result = 31 * result + Arrays.hashCode(filler43);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(I1MdcyDate that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.i1MdcyMm.compareTo(that.i1MdcyMm);
        if ( c != 0 ) return c;
        c = this.i1MdcyDd.compareTo(that.i1MdcyDd);
        if ( c != 0 ) return c;
        c = this.i1MdcyCc.compareTo(that.i1MdcyCc);
        if ( c != 0 ) return c;
        c = this.i1MdcyYy.compareTo(that.i1MdcyYy);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler43, that.filler43);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(DateParmArea that) {
        if (that instanceof I1MdcyDate other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(DateParmArea.SIZE);
    }
    
    private static final StringField I_1_MDCY_MM = factory.getStringField(2);
    private static final StringField I_1_MDCY_DD = factory.getStringField(2);
    private static final StringField I_1_MDCY_CC = factory.getStringField(2);
    private static final StringField I_1_MDCY_YY = factory.getStringField(2);
    private static final ByteArrayField FILLER_43 = factory.getByteArrayField(4);
    private byte[] filler43 = new byte[4];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "I1-MDCY-DATE record at MXWW01.CPY:149"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        I_1_MDCY_MM.putString(i1MdcyMm, bytes, offset);
        I_1_MDCY_DD.putString(i1MdcyDd, bytes, offset);
        I_1_MDCY_CC.putString(i1MdcyCc, bytes, offset);
        I_1_MDCY_YY.putString(i1MdcyYy, bytes, offset);
        FILLER_43.putByteArray(filler43, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#setBytes(byte[], int)} to set parent-class state.
     * @see "I1-MDCY-DATE record at MXWW01.CPY:149"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        i1MdcyMm = I_1_MDCY_MM.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        i1MdcyDd = I_1_MDCY_DD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        i1MdcyCc = I_1_MDCY_CC.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        i1MdcyYy = I_1_MDCY_YY.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_43.getByteArray(bytes, offset);
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
