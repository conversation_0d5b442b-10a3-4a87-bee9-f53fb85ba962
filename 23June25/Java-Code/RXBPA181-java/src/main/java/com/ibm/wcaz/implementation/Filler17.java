package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class Filler17 extends AbtDataAccessInfo {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private char abtVsamCicsStatus = ' ';
    
    /** Initialize fields to non-null default values */
    public Filler17() {}
    
    /** Initialize all fields to provided values */
    public Filler17(String abtU100Sub, String abtDaAccessName, String abtDaGenericStatus, char abtVsamCicsStatus) {
        super(abtU100Sub, abtDaAccessName, abtDaGenericStatus);
        this.abtVsamCicsStatus = abtVsamCicsStatus;
    }
    
    @Override
    public Filler17 clone() throws CloneNotSupportedException {
        Filler17 cloned = (Filler17) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code Filler17} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected Filler17(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code Filler17} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected Filler17(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code Filler17} object
     * @see #setBytes(byte[], int)
     */
    public static Filler17 fromBytes(byte[] bytes, int offset) {
        return new Filler17(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code Filler17} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static Filler17 fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code Filler17} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static Filler17 fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public char getAbtVsamCicsStatus() {
        return this.abtVsamCicsStatus;
    }
    
    public void setAbtVsamCicsStatus(char abtVsamCicsStatus) {
        this.abtVsamCicsStatus = abtVsamCicsStatus;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        abtVsamCicsStatus = ' ';
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ abtVsamCicsStatus=\"");
        s.append(getAbtVsamCicsStatus());
        s.append("\"");
        s.append(", filler18=\"");
        s.append(new String(filler18, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(Filler17 that) {
        return super.equals(that) &&
            this.abtVsamCicsStatus == that.abtVsamCicsStatus &&
            Arrays.equals(this.filler18, that.filler18);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof Filler17 other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof Filler17;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Character.hashCode(abtVsamCicsStatus);
        result = 31 * result + Arrays.hashCode(filler18);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(Filler17 that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = Character.compare(this.abtVsamCicsStatus, that.abtVsamCicsStatus);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler18, that.filler18);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(AbtDataAccessInfo that) {
        if (that instanceof Filler17 other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(AbtDataAccessInfo.SIZE);
    }
    
    private static final StringField ABT_VSAM_CICS_STATUS = factory.getStringField(1);
    private static final ByteArrayField FILLER_18 = factory.getByteArrayField(5);
    private byte[] filler18 = new byte[5];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link AbtDataAccessInfo#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "FILLER #17 record at MXWW03.CPY:101"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        ABT_VSAM_CICS_STATUS.putString(Character.toString(abtVsamCicsStatus), bytes, offset);
        FILLER_18.putByteArray(filler18, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link AbtDataAccessInfo#setBytes(byte[], int)} to set parent-class state.
     * @see "FILLER #17 record at MXWW03.CPY:101"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        abtVsamCicsStatus = ABT_VSAM_CICS_STATUS.getString(bytes, offset).charAt(0);
        FILLER_18.getByteArray(bytes, offset);
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
