package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.ExternalDecimalAsIntField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class OutputNextMoYr implements Cloneable, Comparable<OutputNextMoYr> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private int outputNextMonth;
    
    /** Initialize fields to non-null default values */
    public OutputNextMoYr() {}
    
    /** Initialize all fields to provided values */
    public OutputNextMoYr(int outputNextMonth) {
        this.outputNextMonth = outputNextMonth;
    }
    
    @Override
    public OutputNextMoYr clone() throws CloneNotSupportedException {
        OutputNextMoYr cloned = (OutputNextMoYr) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code OutputNextMoYr} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected OutputNextMoYr(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code OutputNextMoYr} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected OutputNextMoYr(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code OutputNextMoYr} object
     * @see #setBytes(byte[], int)
     */
    public static OutputNextMoYr fromBytes(byte[] bytes, int offset) {
        return new OutputNextMoYr(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code OutputNextMoYr} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static OutputNextMoYr fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code OutputNextMoYr} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static OutputNextMoYr fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public int getOutputNextMonth() {
        return this.outputNextMonth;
    }
    
    public void setOutputNextMonth(int outputNextMonth) {
        this.outputNextMonth = outputNextMonth;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        outputNextMonth = 0;
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ outputNextMonth=\"");
        s.append(getOutputNextMonth());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(OutputNextMoYr that) {
        return this.outputNextMonth == that.outputNextMonth;
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof OutputNextMoYr other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof OutputNextMoYr;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Integer.hashCode(outputNextMonth);
        return result;
    }
    
    @Override
    public int compareTo(OutputNextMoYr that) {
        int c = 0;
        c = Integer.compare(this.outputNextMonth, that.outputNextMonth);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final ExternalDecimalAsIntField OUTPUT_NEXT_MONTH = factory.getExternalDecimalAsIntField(2, true);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code OutputNextMoYr} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code OUTPUT-NEXT-MO-YR} record
     * @see "OUTPUT-NEXT-MO-YR record at MXWW01.CPY:380"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        OUTPUT_NEXT_MONTH.putInt(outputNextMonth, bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code OutputNextMoYr} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code OutputNextMoYr} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code OutputNextMoYr} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code OUTPUT-NEXT-MO-YR} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "OUTPUT-NEXT-MO-YR record at MXWW01.CPY:380"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        outputNextMonth = OUTPUT_NEXT_MONTH.getInt(bytes, offset);
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
