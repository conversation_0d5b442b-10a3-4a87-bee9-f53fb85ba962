package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.BinaryAsIntField;
import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class WsFields implements Cloneable, Comparable<WsFields> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String wsCpuCode = "";
    private int wsFileSeqNo;
    private int wsItemSeqNo;
    private int wsDlrNo;
    
    /** Initialize fields to non-null default values */
    public WsFields() {}
    
    /** Initialize all fields to provided values */
    public WsFields(String wsCpuCode, int wsFileSeqNo, int wsItemSeqNo, int wsDlrNo) {
        this.wsCpuCode = wsCpuCode;
        this.wsFileSeqNo = wsFileSeqNo;
        this.wsItemSeqNo = wsItemSeqNo;
        this.wsDlrNo = wsDlrNo;
    }
    
    @Override
    public WsFields clone() throws CloneNotSupportedException {
        WsFields cloned = (WsFields) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code WsFields} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected WsFields(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code WsFields} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected WsFields(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsFields} object
     * @see #setBytes(byte[], int)
     */
    public static WsFields fromBytes(byte[] bytes, int offset) {
        return new WsFields(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsFields} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static WsFields fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code WsFields} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static WsFields fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getWsCpuCode() {
        return this.wsCpuCode;
    }
    
    public void setWsCpuCode(String wsCpuCode) {
        this.wsCpuCode = wsCpuCode;
    }
    
    public int getWsFileSeqNo() {
        return this.wsFileSeqNo;
    }
    
    public void setWsFileSeqNo(int wsFileSeqNo) {
        this.wsFileSeqNo = wsFileSeqNo;
    }
    
    public int getWsItemSeqNo() {
        return this.wsItemSeqNo;
    }
    
    public void setWsItemSeqNo(int wsItemSeqNo) {
        this.wsItemSeqNo = wsItemSeqNo;
    }
    
    public int getWsDlrNo() {
        return this.wsDlrNo;
    }
    
    public void setWsDlrNo(int wsDlrNo) {
        this.wsDlrNo = wsDlrNo;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        wsCpuCode = "";
        wsFileSeqNo = 0;
        wsItemSeqNo = 0;
        wsDlrNo = 0;
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ wsCpuCode=\"");
        s.append(getWsCpuCode());
        s.append("\"");
        s.append(", wsFileSeqNo=\"");
        s.append(getWsFileSeqNo());
        s.append("\"");
        s.append(", wsItemSeqNo=\"");
        s.append(getWsItemSeqNo());
        s.append("\"");
        s.append(", wsDlrNo=\"");
        s.append(getWsDlrNo());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(WsFields that) {
        return this.wsCpuCode.equals(that.wsCpuCode) &&
            this.wsFileSeqNo == that.wsFileSeqNo &&
            this.wsItemSeqNo == that.wsItemSeqNo &&
            this.wsDlrNo == that.wsDlrNo;
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof WsFields other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof WsFields;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Objects.hashCode(wsCpuCode);
        result = 31 * result + Integer.hashCode(wsFileSeqNo);
        result = 31 * result + Integer.hashCode(wsItemSeqNo);
        result = 31 * result + Integer.hashCode(wsDlrNo);
        return result;
    }
    
    @Override
    public int compareTo(WsFields that) {
        int c = 0;
        c = this.wsCpuCode.compareTo(that.wsCpuCode);
        if ( c != 0 ) return c;
        c = Integer.compare(this.wsFileSeqNo, that.wsFileSeqNo);
        if ( c != 0 ) return c;
        c = Integer.compare(this.wsItemSeqNo, that.wsItemSeqNo);
        if ( c != 0 ) return c;
        c = Integer.compare(this.wsDlrNo, that.wsDlrNo);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final StringField WS_CPU_CODE = factory.getStringField(4);
    private static final BinaryAsIntField WS_FILE_SEQ_NO = factory.getBinaryAsIntField(9, true);
    private static final BinaryAsIntField WS_ITEM_SEQ_NO = factory.getBinaryAsIntField(9, true);
    private static final BinaryAsIntField WS_DLR_NO = factory.getBinaryAsIntField(9, true);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsFields} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code WS-FIELDS} record
     * @see "WS-FIELDS record at RXBPASVC.cbl:148"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        WS_CPU_CODE.putString(wsCpuCode, bytes, offset);
        WS_FILE_SEQ_NO.putInt(wsFileSeqNo, bytes, offset);
        WS_ITEM_SEQ_NO.putInt(wsItemSeqNo, bytes, offset);
        WS_DLR_NO.putInt(wsDlrNo, bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsFields} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsFields} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code WsFields} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code WS-FIELDS} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "WS-FIELDS record at RXBPASVC.cbl:148"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        wsCpuCode = WS_CPU_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        wsFileSeqNo = WS_FILE_SEQ_NO.getInt(bytes, offset);
        wsItemSeqNo = WS_ITEM_SEQ_NO.getInt(bytes, offset);
        wsDlrNo = WS_DLR_NO.getInt(bytes, offset);
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
