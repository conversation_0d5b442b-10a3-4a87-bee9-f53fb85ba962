package com.ibm.wcaz.datamodel;

import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.ExternalDecimalAsIntField;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.sql.Date;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Objects;

public class Vwmcpcd implements Cloneable, Comparable<Vwmcpcd> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String cpuId = "";
    private String cpuCode = "";
    private int distNo;
    private int mfgNo;
    private String cpuStatCode = "";
    private String prodCode = "";
    private int distLocNo;
    private int mfgLocNo;
    private int postingBranchNo;
    private String altCpuCode = "";
    private int altCntlBranchNo;
    private Date auditTs = new Date(0);
    private String auditLogonId = "";
    private String auditProcessTx = "";
    private String auditDeleteFl = "";
    
    /** Initialize fields to non-null default values */
    public Vwmcpcd() {}
    
    /** Initialize all fields to provided values */
    public Vwmcpcd(String cpuId, String cpuCode, int distNo, int mfgNo, String cpuStatCode, String prodCode, int distLocNo, int mfgLocNo, int postingBranchNo, String altCpuCode, int altCntlBranchNo, Date auditTs, String auditLogonId, String auditProcessTx, String auditDeleteFl) {
        this.cpuId = cpuId;
        this.cpuCode = cpuCode;
        this.distNo = distNo;
        this.mfgNo = mfgNo;
        this.cpuStatCode = cpuStatCode;
        this.prodCode = prodCode;
        this.distLocNo = distLocNo;
        this.mfgLocNo = mfgLocNo;
        this.postingBranchNo = postingBranchNo;
        this.altCpuCode = altCpuCode;
        this.altCntlBranchNo = altCntlBranchNo;
        this.auditTs = auditTs;
        this.auditLogonId = auditLogonId;
        this.auditProcessTx = auditProcessTx;
        this.auditDeleteFl = auditDeleteFl;
    }
    
    @Override
    public Vwmcpcd clone() throws CloneNotSupportedException {
        Vwmcpcd cloned = (Vwmcpcd) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code Vwmcpcd} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected Vwmcpcd(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code Vwmcpcd} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected Vwmcpcd(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code Vwmcpcd} object
     * @see #setBytes(byte[], int)
     */
    public static Vwmcpcd fromBytes(byte[] bytes, int offset) {
        return new Vwmcpcd(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code Vwmcpcd} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static Vwmcpcd fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code Vwmcpcd} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static Vwmcpcd fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getCpuId() {
        return this.cpuId;
    }
    
    public void setCpuId(String cpuId) {
        this.cpuId = cpuId;
    }
    
    public String getCpuCode() {
        return this.cpuCode;
    }
    
    public void setCpuCode(String cpuCode) {
        this.cpuCode = cpuCode;
    }
    
    public int getDistNo() {
        return this.distNo;
    }
    
    public void setDistNo(int distNo) {
        this.distNo = distNo;
    }
    
    public int getMfgNo() {
        return this.mfgNo;
    }
    
    public void setMfgNo(int mfgNo) {
        this.mfgNo = mfgNo;
    }
    
    public String getCpuStatCode() {
        return this.cpuStatCode;
    }
    
    public void setCpuStatCode(String cpuStatCode) {
        this.cpuStatCode = cpuStatCode;
    }
    
    public String getProdCode() {
        return this.prodCode;
    }
    
    public void setProdCode(String prodCode) {
        this.prodCode = prodCode;
    }
    
    public int getDistLocNo() {
        return this.distLocNo;
    }
    
    public void setDistLocNo(int distLocNo) {
        this.distLocNo = distLocNo;
    }
    
    public int getMfgLocNo() {
        return this.mfgLocNo;
    }
    
    public void setMfgLocNo(int mfgLocNo) {
        this.mfgLocNo = mfgLocNo;
    }
    
    public int getPostingBranchNo() {
        return this.postingBranchNo;
    }
    
    public void setPostingBranchNo(int postingBranchNo) {
        this.postingBranchNo = postingBranchNo;
    }
    
    public String getAltCpuCode() {
        return this.altCpuCode;
    }
    
    public void setAltCpuCode(String altCpuCode) {
        this.altCpuCode = altCpuCode;
    }
    
    public int getAltCntlBranchNo() {
        return this.altCntlBranchNo;
    }
    
    public void setAltCntlBranchNo(int altCntlBranchNo) {
        this.altCntlBranchNo = altCntlBranchNo;
    }
    
    public Date getAuditTs() {
        return this.auditTs;
    }
    
    public void setAuditTs(Date auditTs) {
        this.auditTs = auditTs;
    }
    
    public String getAuditLogonId() {
        return this.auditLogonId;
    }
    
    public void setAuditLogonId(String auditLogonId) {
        this.auditLogonId = auditLogonId;
    }
    
    public String getAuditProcessTx() {
        return this.auditProcessTx;
    }
    
    public void setAuditProcessTx(String auditProcessTx) {
        this.auditProcessTx = auditProcessTx;
    }
    
    public String getAuditDeleteFl() {
        return this.auditDeleteFl;
    }
    
    public void setAuditDeleteFl(String auditDeleteFl) {
        this.auditDeleteFl = auditDeleteFl;
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ cpuId=\"");
        s.append(getCpuId());
        s.append("\"");
        s.append(", cpuCode=\"");
        s.append(getCpuCode());
        s.append("\"");
        s.append(", distNo=\"");
        s.append(getDistNo());
        s.append("\"");
        s.append(", mfgNo=\"");
        s.append(getMfgNo());
        s.append("\"");
        s.append(", cpuStatCode=\"");
        s.append(getCpuStatCode());
        s.append("\"");
        s.append(", prodCode=\"");
        s.append(getProdCode());
        s.append("\"");
        s.append(", distLocNo=\"");
        s.append(getDistLocNo());
        s.append("\"");
        s.append(", mfgLocNo=\"");
        s.append(getMfgLocNo());
        s.append("\"");
        s.append(", postingBranchNo=\"");
        s.append(getPostingBranchNo());
        s.append("\"");
        s.append(", altCpuCode=\"");
        s.append(getAltCpuCode());
        s.append("\"");
        s.append(", altCntlBranchNo=\"");
        s.append(getAltCntlBranchNo());
        s.append("\"");
        s.append(", auditTs=\"");
        s.append(getAuditTs());
        s.append("\"");
        s.append(", auditLogonId=\"");
        s.append(getAuditLogonId());
        s.append("\"");
        s.append(", auditProcessTx=\"");
        s.append(getAuditProcessTx());
        s.append("\"");
        s.append(", auditDeleteFl=\"");
        s.append(getAuditDeleteFl());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(Vwmcpcd that) {
        return this.cpuId.equals(that.cpuId) &&
            this.cpuCode.equals(that.cpuCode) &&
            this.distNo == that.distNo &&
            this.mfgNo == that.mfgNo &&
            this.cpuStatCode.equals(that.cpuStatCode) &&
            this.prodCode.equals(that.prodCode) &&
            this.distLocNo == that.distLocNo &&
            this.mfgLocNo == that.mfgLocNo &&
            this.postingBranchNo == that.postingBranchNo &&
            this.altCpuCode.equals(that.altCpuCode) &&
            this.altCntlBranchNo == that.altCntlBranchNo &&
            this.auditTs.equals(that.auditTs) &&
            this.auditLogonId.equals(that.auditLogonId) &&
            this.auditProcessTx.equals(that.auditProcessTx) &&
            this.auditDeleteFl.equals(that.auditDeleteFl);
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof Vwmcpcd other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof Vwmcpcd;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Objects.hashCode(cpuId);
        result = 31 * result + Objects.hashCode(cpuCode);
        result = 31 * result + Integer.hashCode(distNo);
        result = 31 * result + Integer.hashCode(mfgNo);
        result = 31 * result + Objects.hashCode(cpuStatCode);
        result = 31 * result + Objects.hashCode(prodCode);
        result = 31 * result + Integer.hashCode(distLocNo);
        result = 31 * result + Integer.hashCode(mfgLocNo);
        result = 31 * result + Integer.hashCode(postingBranchNo);
        result = 31 * result + Objects.hashCode(altCpuCode);
        result = 31 * result + Integer.hashCode(altCntlBranchNo);
        result = 31 * result + Objects.hashCode(auditTs);
        result = 31 * result + Objects.hashCode(auditLogonId);
        result = 31 * result + Objects.hashCode(auditProcessTx);
        result = 31 * result + Objects.hashCode(auditDeleteFl);
        return result;
    }
    
    @Override
    public int compareTo(Vwmcpcd that) {
        int c = 0;
        c = this.cpuId.compareTo(that.cpuId);
        if ( c != 0 ) return c;
        c = this.cpuCode.compareTo(that.cpuCode);
        if ( c != 0 ) return c;
        c = Integer.compare(this.distNo, that.distNo);
        if ( c != 0 ) return c;
        c = Integer.compare(this.mfgNo, that.mfgNo);
        if ( c != 0 ) return c;
        c = this.cpuStatCode.compareTo(that.cpuStatCode);
        if ( c != 0 ) return c;
        c = this.prodCode.compareTo(that.prodCode);
        if ( c != 0 ) return c;
        c = Integer.compare(this.distLocNo, that.distLocNo);
        if ( c != 0 ) return c;
        c = Integer.compare(this.mfgLocNo, that.mfgLocNo);
        if ( c != 0 ) return c;
        c = Integer.compare(this.postingBranchNo, that.postingBranchNo);
        if ( c != 0 ) return c;
        c = this.altCpuCode.compareTo(that.altCpuCode);
        if ( c != 0 ) return c;
        c = Integer.compare(this.altCntlBranchNo, that.altCntlBranchNo);
        if ( c != 0 ) return c;
        c = this.auditTs.compareTo(that.auditTs);
        if ( c != 0 ) return c;
        c = this.auditLogonId.compareTo(that.auditLogonId);
        if ( c != 0 ) return c;
        c = this.auditProcessTx.compareTo(that.auditProcessTx);
        if ( c != 0 ) return c;
        c = this.auditDeleteFl.compareTo(that.auditDeleteFl);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final StringField CPU_ID = factory.getStringField(5);
    private static final StringField CPU_CODE = factory.getStringField(5);
    private static final ExternalDecimalAsIntField DIST_NO = factory.getExternalDecimalAsIntField(5, true);
    private static final ExternalDecimalAsIntField MFG_NO = factory.getExternalDecimalAsIntField(5, true);
    private static final StringField CPU_STAT_CODE = factory.getStringField(2);
    private static final StringField PROD_CODE = factory.getStringField(5);
    private static final ExternalDecimalAsIntField DIST_LOC_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField MFG_LOC_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField POSTING_BRANCH_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField ALT_CPU_CODE = factory.getStringField(5);
    private static final ExternalDecimalAsIntField ALT_CNTL_BRANCH_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField AUDIT_TS = factory.getStringField(14);
    private static final DateTimeFormatter AUDIT_TS_FMT = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    private static final StringField AUDIT_LOGON_ID = factory.getStringField(9);
    private static final StringField AUDIT_PROCESS_TX = factory.getStringField(63);
    private static final StringField AUDIT_DELETE_FL = factory.getStringField(2);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code Vwmcpcd} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code VWMCPCD} record
     * @see "VWMCPCD record at VWMCPCD.CPY:9"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        CPU_ID.putString(cpuId, bytes, offset);
        CPU_CODE.putString(cpuCode, bytes, offset);
        DIST_NO.putInt(distNo, bytes, offset);
        MFG_NO.putInt(mfgNo, bytes, offset);
        CPU_STAT_CODE.putString(cpuStatCode, bytes, offset);
        PROD_CODE.putString(prodCode, bytes, offset);
        DIST_LOC_NO.putInt(distLocNo, bytes, offset);
        MFG_LOC_NO.putInt(mfgLocNo, bytes, offset);
        POSTING_BRANCH_NO.putInt(postingBranchNo, bytes, offset);
        ALT_CPU_CODE.putString(altCpuCode, bytes, offset);
        ALT_CNTL_BRANCH_NO.putInt(altCntlBranchNo, bytes, offset);
        AUDIT_TS.putString(auditTs.toLocalDate().format(AUDIT_TS_FMT), bytes, offset);
        AUDIT_LOGON_ID.putString(auditLogonId, bytes, offset);
        AUDIT_PROCESS_TX.putString(auditProcessTx, bytes, offset);
        AUDIT_DELETE_FL.putString(auditDeleteFl, bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code Vwmcpcd} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code Vwmcpcd} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code Vwmcpcd} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code VWMCPCD} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "VWMCPCD record at VWMCPCD.CPY:9"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        cpuId = CPU_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        cpuCode = CPU_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        distNo = DIST_NO.getInt(bytes, offset);
        mfgNo = MFG_NO.getInt(bytes, offset);
        cpuStatCode = CPU_STAT_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        prodCode = PROD_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        distLocNo = DIST_LOC_NO.getInt(bytes, offset);
        mfgLocNo = MFG_LOC_NO.getInt(bytes, offset);
        postingBranchNo = POSTING_BRANCH_NO.getInt(bytes, offset);
        altCpuCode = ALT_CPU_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        altCntlBranchNo = ALT_CNTL_BRANCH_NO.getInt(bytes, offset);
        auditTs = Date.valueOf(LocalDate.parse(AUDIT_TS.getString(bytes, offset), AUDIT_TS_FMT));
        auditLogonId = AUDIT_LOGON_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        auditProcessTx = AUDIT_PROCESS_TX.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        auditDeleteFl = AUDIT_DELETE_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
