package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.ExternalDecimalAsIntField;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class DateEditErrorAbcodeX extends DateParmArea {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private int dateEditErrorAbcode;
    
    /** Initialize fields to non-null default values */
    public DateEditErrorAbcodeX() {}
    
    /** Initialize all fields to provided values */
    public DateEditErrorAbcodeX(int reqDay, int manipulateReqType, int numberOfDays, int numberOfMonths, int inputDateType, int oIsoCc, int oIsoYy, int oIsoMm, int oIsoDd, int oUsaMm, int oUsaDd, int oUsaCc, int oUsaYy, int oEurDd, int oEurMm, int oEurCc, int oEurYy, int oJisCc, int oJisYy, int oJisMm, int oJisDd, int oMdySlshMm, int oMdySlshDd, int oMdySlshYy, int oMdyMm, int oMdyDd, int oMdyYy, int oMdcySlshMm, int oMdcySlshDd, int oMdcySlshCc, int oMdcySlshYy, int oMdcyMm, int oMdcyDd, int oMdcyCc, int oMdcyYy, int oYmdYy, int oYmdMm, int oYmdDd, int oCymdCc, int oCymdYy, int oCymdMm, int oCymdDd, int oJulYy, int oJulDdd, String oExpMm, int oExpDd, int oExpCc, int oExpYy, int differenceInDays, int oDayNumber, String reqCountry, OutputNextMoYr outputNextMoYr, OutputLastMoYr outputLastMoYr, int dateEditErrorAbcode) {
        super(reqDay, manipulateReqType, numberOfDays, numberOfMonths, inputDateType, oIsoCc, oIsoYy, oIsoMm, oIsoDd, oUsaMm, oUsaDd, oUsaCc, oUsaYy, oEurDd, oEurMm, oEurCc, oEurYy, oJisCc, oJisYy, oJisMm, oJisDd, oMdySlshMm, oMdySlshDd, oMdySlshYy, oMdyMm, oMdyDd, oMdyYy, oMdcySlshMm, oMdcySlshDd, oMdcySlshCc, oMdcySlshYy, oMdcyMm, oMdcyDd, oMdcyCc, oMdcyYy, oYmdYy, oYmdMm, oYmdDd, oCymdCc, oCymdYy, oCymdMm, oCymdDd, oJulYy, oJulDdd, oExpMm, oExpDd, oExpCc, oExpYy, differenceInDays, oDayNumber, reqCountry, outputNextMoYr, outputLastMoYr);
        this.dateEditErrorAbcode = dateEditErrorAbcode;
    }
    
    @Override
    public DateEditErrorAbcodeX clone() throws CloneNotSupportedException {
        DateEditErrorAbcodeX cloned = (DateEditErrorAbcodeX) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code DateEditErrorAbcodeX} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected DateEditErrorAbcodeX(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code DateEditErrorAbcodeX} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected DateEditErrorAbcodeX(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code DateEditErrorAbcodeX} object
     * @see #setBytes(byte[], int)
     */
    public static DateEditErrorAbcodeX fromBytes(byte[] bytes, int offset) {
        return new DateEditErrorAbcodeX(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code DateEditErrorAbcodeX} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static DateEditErrorAbcodeX fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code DateEditErrorAbcodeX} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static DateEditErrorAbcodeX fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public int getDateEditErrorAbcode() {
        return this.dateEditErrorAbcode;
    }
    
    public void setDateEditErrorAbcode(int dateEditErrorAbcode) {
        this.dateEditErrorAbcode = dateEditErrorAbcode;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        dateEditErrorAbcode = 0;
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ filler91=\"");
        s.append(new String(filler91, encoding));
        s.append("\"");
        s.append(", dateEditErrorAbcode=\"");
        s.append(getDateEditErrorAbcode());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(DateEditErrorAbcodeX that) {
        return super.equals(that) &&
            Arrays.equals(this.filler91, that.filler91) &&
            this.dateEditErrorAbcode == that.dateEditErrorAbcode;
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof DateEditErrorAbcodeX other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof DateEditErrorAbcodeX;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Arrays.hashCode(filler91);
        result = 31 * result + Integer.hashCode(dateEditErrorAbcode);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(DateEditErrorAbcodeX that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler91, that.filler91);
        if ( c != 0 ) return c;
        c = Integer.compare(this.dateEditErrorAbcode, that.dateEditErrorAbcode);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(DateParmArea that) {
        if (that instanceof DateEditErrorAbcodeX other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(DateParmArea.SIZE);
    }
    
    private static final ByteArrayField FILLER_91 = factory.getByteArrayField(2);
    private byte[] filler91 = new byte[2];
    private static final ExternalDecimalAsIntField DATE_EDIT_ERROR_ABCODE = factory.getExternalDecimalAsIntField(4, true);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "DATE-EDIT-ERROR-ABCODE-X record at MXWW01.CPY:373"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        FILLER_91.putByteArray(filler91, bytes, offset);
        DATE_EDIT_ERROR_ABCODE.putInt(dateEditErrorAbcode, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#setBytes(byte[], int)} to set parent-class state.
     * @see "DATE-EDIT-ERROR-ABCODE-X record at MXWW01.CPY:373"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        FILLER_91.getByteArray(bytes, offset);
        dateEditErrorAbcode = DATE_EDIT_ERROR_ABCODE.getInt(bytes, offset);
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
