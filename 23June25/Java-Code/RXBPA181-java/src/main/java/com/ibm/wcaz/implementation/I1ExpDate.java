package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class I1ExpDate extends DateParmArea {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String i1ExpMm = "";
    private String i1ExpDd = "";
    private String i1ExpCc = "";
    private String i1ExpYy = "";
    
    /** Initialize fields to non-null default values */
    public I1ExpDate() {}
    
    /** Initialize all fields to provided values */
    public I1ExpDate(int reqDay, int manipulateReqType, int numberOfDays, int numberOfMonths, int inputDateType, int oIsoCc, int oIsoYy, int oIsoMm, int oIsoDd, int oUsaMm, int oUsaDd, int oUsaCc, int oUsaYy, int oEurDd, int oEurMm, int oEurCc, int oEurYy, int oJisCc, int oJisYy, int oJisMm, int oJisDd, int oMdySlshMm, int oMdySlshDd, int oMdySlshYy, int oMdyMm, int oMdyDd, int oMdyYy, int oMdcySlshMm, int oMdcySlshDd, int oMdcySlshCc, int oMdcySlshYy, int oMdcyMm, int oMdcyDd, int oMdcyCc, int oMdcyYy, int oYmdYy, int oYmdMm, int oYmdDd, int oCymdCc, int oCymdYy, int oCymdMm, int oCymdDd, int oJulYy, int oJulDdd, String oExpMm, int oExpDd, int oExpCc, int oExpYy, int differenceInDays, int oDayNumber, String reqCountry, OutputNextMoYr outputNextMoYr, OutputLastMoYr outputLastMoYr, String i1ExpMm, String i1ExpDd, String i1ExpCc, String i1ExpYy) {
        super(reqDay, manipulateReqType, numberOfDays, numberOfMonths, inputDateType, oIsoCc, oIsoYy, oIsoMm, oIsoDd, oUsaMm, oUsaDd, oUsaCc, oUsaYy, oEurDd, oEurMm, oEurCc, oEurYy, oJisCc, oJisYy, oJisMm, oJisDd, oMdySlshMm, oMdySlshDd, oMdySlshYy, oMdyMm, oMdyDd, oMdyYy, oMdcySlshMm, oMdcySlshDd, oMdcySlshCc, oMdcySlshYy, oMdcyMm, oMdcyDd, oMdcyCc, oMdcyYy, oYmdYy, oYmdMm, oYmdDd, oCymdCc, oCymdYy, oCymdMm, oCymdDd, oJulYy, oJulDdd, oExpMm, oExpDd, oExpCc, oExpYy, differenceInDays, oDayNumber, reqCountry, outputNextMoYr, outputLastMoYr);
        this.i1ExpMm = i1ExpMm;
        this.i1ExpDd = i1ExpDd;
        this.i1ExpCc = i1ExpCc;
        this.i1ExpYy = i1ExpYy;
    }
    
    @Override
    public I1ExpDate clone() throws CloneNotSupportedException {
        I1ExpDate cloned = (I1ExpDate) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code I1ExpDate} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected I1ExpDate(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code I1ExpDate} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected I1ExpDate(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I1ExpDate} object
     * @see #setBytes(byte[], int)
     */
    public static I1ExpDate fromBytes(byte[] bytes, int offset) {
        return new I1ExpDate(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I1ExpDate} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static I1ExpDate fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code I1ExpDate} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static I1ExpDate fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getI1ExpMm() {
        return this.i1ExpMm;
    }
    
    public void setI1ExpMm(String i1ExpMm) {
        this.i1ExpMm = i1ExpMm;
    }
    
    public String getI1ExpDd() {
        return this.i1ExpDd;
    }
    
    public void setI1ExpDd(String i1ExpDd) {
        this.i1ExpDd = i1ExpDd;
    }
    
    public String getI1ExpCc() {
        return this.i1ExpCc;
    }
    
    public void setI1ExpCc(String i1ExpCc) {
        this.i1ExpCc = i1ExpCc;
    }
    
    public String getI1ExpYy() {
        return this.i1ExpYy;
    }
    
    public void setI1ExpYy(String i1ExpYy) {
        this.i1ExpYy = i1ExpYy;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        i1ExpMm = "";
        i1ExpDd = "";
        i1ExpCc = "";
        i1ExpYy = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ i1ExpMm=\"");
        s.append(getI1ExpMm());
        s.append("\"");
        s.append(", filler47=\"");
        s.append(new String(filler47, encoding));
        s.append("\"");
        s.append(", i1ExpDd=\"");
        s.append(getI1ExpDd());
        s.append("\"");
        s.append(", filler48=\"");
        s.append(new String(filler48, encoding));
        s.append("\"");
        s.append(", i1ExpCc=\"");
        s.append(getI1ExpCc());
        s.append("\"");
        s.append(", i1ExpYy=\"");
        s.append(getI1ExpYy());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(I1ExpDate that) {
        return super.equals(that) &&
            this.i1ExpMm.equals(that.i1ExpMm) &&
            Arrays.equals(this.filler47, that.filler47) &&
            this.i1ExpDd.equals(that.i1ExpDd) &&
            Arrays.equals(this.filler48, that.filler48) &&
            this.i1ExpCc.equals(that.i1ExpCc) &&
            this.i1ExpYy.equals(that.i1ExpYy);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof I1ExpDate other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof I1ExpDate;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(i1ExpMm);
        result = 31 * result + Arrays.hashCode(filler47);
        result = 31 * result + Objects.hashCode(i1ExpDd);
        result = 31 * result + Arrays.hashCode(filler48);
        result = 31 * result + Objects.hashCode(i1ExpCc);
        result = 31 * result + Objects.hashCode(i1ExpYy);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(I1ExpDate that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.i1ExpMm.compareTo(that.i1ExpMm);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler47, that.filler47);
        if ( c != 0 ) return c;
        c = this.i1ExpDd.compareTo(that.i1ExpDd);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler48, that.filler48);
        if ( c != 0 ) return c;
        c = this.i1ExpCc.compareTo(that.i1ExpCc);
        if ( c != 0 ) return c;
        c = this.i1ExpYy.compareTo(that.i1ExpYy);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(DateParmArea that) {
        if (that instanceof I1ExpDate other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(DateParmArea.SIZE);
    }
    
    private static final StringField I_1_EXP_MM = factory.getStringField(3);
    private static final ByteArrayField FILLER_47 = factory.getByteArrayField(1);
    private byte[] filler47 = new byte[1];
    private static final StringField I_1_EXP_DD = factory.getStringField(2);
    private static final ByteArrayField FILLER_48 = factory.getByteArrayField(2);
    private byte[] filler48 = new byte[2];
    private static final StringField I_1_EXP_CC = factory.getStringField(2);
    private static final StringField I_1_EXP_YY = factory.getStringField(2);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "I1-EXP-DATE record at MXWW01.CPY:172"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        I_1_EXP_MM.putString(i1ExpMm, bytes, offset);
        FILLER_47.putByteArray(filler47, bytes, offset);
        I_1_EXP_DD.putString(i1ExpDd, bytes, offset);
        FILLER_48.putByteArray(filler48, bytes, offset);
        I_1_EXP_CC.putString(i1ExpCc, bytes, offset);
        I_1_EXP_YY.putString(i1ExpYy, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#setBytes(byte[], int)} to set parent-class state.
     * @see "I1-EXP-DATE record at MXWW01.CPY:172"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        i1ExpMm = I_1_EXP_MM.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_47.getByteArray(bytes, offset);
        i1ExpDd = I_1_EXP_DD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_48.getByteArray(bytes, offset);
        i1ExpCc = I_1_EXP_CC.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        i1ExpYy = I_1_EXP_YY.getString(bytes, offset).replaceFirst("\\s+\\z", "");
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
