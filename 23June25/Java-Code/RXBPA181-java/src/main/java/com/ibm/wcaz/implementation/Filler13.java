package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class Filler13 extends AbtDataAccessInfo {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String abtDliStatus = "";
    
    /** Initialize fields to non-null default values */
    public Filler13() {}
    
    /** Initialize all fields to provided values */
    public Filler13(String abtU100Sub, String abtDaAccessName, String abtDaGenericStatus, String abtDliStatus) {
        super(abtU100Sub, abtDaAccessName, abtDaGenericStatus);
        this.abtDliStatus = abtDliStatus;
    }
    
    @Override
    public Filler13 clone() throws CloneNotSupportedException {
        Filler13 cloned = (Filler13) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code Filler13} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected Filler13(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code Filler13} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected Filler13(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code Filler13} object
     * @see #setBytes(byte[], int)
     */
    public static Filler13 fromBytes(byte[] bytes, int offset) {
        return new Filler13(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code Filler13} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static Filler13 fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code Filler13} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static Filler13 fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getAbtDliStatus() {
        return this.abtDliStatus;
    }
    
    public void setAbtDliStatus(String abtDliStatus) {
        this.abtDliStatus = abtDliStatus;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        abtDliStatus = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ abtDliStatus=\"");
        s.append(getAbtDliStatus());
        s.append("\"");
        s.append(", filler14=\"");
        s.append(new String(filler14, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(Filler13 that) {
        return super.equals(that) &&
            this.abtDliStatus.equals(that.abtDliStatus) &&
            Arrays.equals(this.filler14, that.filler14);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof Filler13 other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof Filler13;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(abtDliStatus);
        result = 31 * result + Arrays.hashCode(filler14);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(Filler13 that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.abtDliStatus.compareTo(that.abtDliStatus);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler14, that.filler14);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(AbtDataAccessInfo that) {
        if (that instanceof Filler13 other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(AbtDataAccessInfo.SIZE);
    }
    
    private static final StringField ABT_DLI_STATUS = factory.getStringField(2);
    private static final ByteArrayField FILLER_14 = factory.getByteArrayField(4);
    private byte[] filler14 = new byte[4];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link AbtDataAccessInfo#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "FILLER #13 record at MXWW03.CPY:95"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        ABT_DLI_STATUS.putString(abtDliStatus, bytes, offset);
        FILLER_14.putByteArray(filler14, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link AbtDataAccessInfo#setBytes(byte[], int)} to set parent-class state.
     * @see "FILLER #13 record at MXWW03.CPY:95"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        abtDliStatus = ABT_DLI_STATUS.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_14.getByteArray(bytes, offset);
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
