package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;

public class WsFlags implements Cloneable, Comparable<WsFlags> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private static final char WS_END_OF_FILE_VALUE = 'Y';
    private static final char EMPTY_CPU_FILE_VALUE = 'Y';
    private static final char HEADER_REC_READ_VALUE = 'Y';
    private static final char HEADER_REC_NOT_READ_VALUE = 'N';
    private static final char DETAIL_REC_READ_VALUE = 'Y';
    private static final char DETAIL_REC_NOT_READ_VALUE = 'N';
    private static final char TRAILER_REC_READ_VALUE = 'Y';
    private static final char TRAILER_REC_NOT_READ_VALUE = 'N';
    
    private char wsEofSw = 'N';
    private char wsCpuEofSw = 'N';
    private char wsHeaderRecReadFlag = 'N';
    private char wsDetailRecReadFlag = 'N';
    private char wsTrailerRecReadFlag = 'N';
    
    /** Initialize fields to non-null default values */
    public WsFlags() {}
    
    /** Initialize all fields to provided values */
    public WsFlags(char wsEofSw, char wsCpuEofSw, char wsHeaderRecReadFlag, char wsDetailRecReadFlag, char wsTrailerRecReadFlag) {
        this.wsEofSw = wsEofSw;
        this.wsCpuEofSw = wsCpuEofSw;
        this.wsHeaderRecReadFlag = wsHeaderRecReadFlag;
        this.wsDetailRecReadFlag = wsDetailRecReadFlag;
        this.wsTrailerRecReadFlag = wsTrailerRecReadFlag;
    }
    
    @Override
    public WsFlags clone() throws CloneNotSupportedException {
        WsFlags cloned = (WsFlags) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code WsFlags} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected WsFlags(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code WsFlags} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected WsFlags(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsFlags} object
     * @see #setBytes(byte[], int)
     */
    public static WsFlags fromBytes(byte[] bytes, int offset) {
        return new WsFlags(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsFlags} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static WsFlags fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code WsFlags} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static WsFlags fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public char getWsEofSw() {
        return this.wsEofSw;
    }
    
    public void setWsEofSw(char wsEofSw) {
        this.wsEofSw = wsEofSw;
    }
    
    public boolean isWsEndOfFile() {
        return wsEofSw == WS_END_OF_FILE_VALUE;
    }
    
    public void setWsEndOfFile() {
        wsEofSw = WS_END_OF_FILE_VALUE;
    }
    
    public char getWsCpuEofSw() {
        return this.wsCpuEofSw;
    }
    
    public void setWsCpuEofSw(char wsCpuEofSw) {
        this.wsCpuEofSw = wsCpuEofSw;
    }
    
    public boolean isEmptyCpuFile() {
        return wsCpuEofSw == EMPTY_CPU_FILE_VALUE;
    }
    
    public void setEmptyCpuFile() {
        wsCpuEofSw = EMPTY_CPU_FILE_VALUE;
    }
    
    public char getWsHeaderRecReadFlag() {
        return this.wsHeaderRecReadFlag;
    }
    
    public void setWsHeaderRecReadFlag(char wsHeaderRecReadFlag) {
        this.wsHeaderRecReadFlag = wsHeaderRecReadFlag;
    }
    
    public boolean isHeaderRecRead() {
        return wsHeaderRecReadFlag == HEADER_REC_READ_VALUE;
    }
    
    public void setHeaderRecRead() {
        wsHeaderRecReadFlag = HEADER_REC_READ_VALUE;
    }
    
    public boolean isHeaderRecNotRead() {
        return wsHeaderRecReadFlag == HEADER_REC_NOT_READ_VALUE;
    }
    
    public void setHeaderRecNotRead() {
        wsHeaderRecReadFlag = HEADER_REC_NOT_READ_VALUE;
    }
    
    public char getWsDetailRecReadFlag() {
        return this.wsDetailRecReadFlag;
    }
    
    public void setWsDetailRecReadFlag(char wsDetailRecReadFlag) {
        this.wsDetailRecReadFlag = wsDetailRecReadFlag;
    }
    
    public boolean isDetailRecRead() {
        return wsDetailRecReadFlag == DETAIL_REC_READ_VALUE;
    }
    
    public void setDetailRecRead() {
        wsDetailRecReadFlag = DETAIL_REC_READ_VALUE;
    }
    
    public boolean isDetailRecNotRead() {
        return wsDetailRecReadFlag == DETAIL_REC_NOT_READ_VALUE;
    }
    
    public void setDetailRecNotRead() {
        wsDetailRecReadFlag = DETAIL_REC_NOT_READ_VALUE;
    }
    
    public char getWsTrailerRecReadFlag() {
        return this.wsTrailerRecReadFlag;
    }
    
    public void setWsTrailerRecReadFlag(char wsTrailerRecReadFlag) {
        this.wsTrailerRecReadFlag = wsTrailerRecReadFlag;
    }
    
    public boolean isTrailerRecRead() {
        return wsTrailerRecReadFlag == TRAILER_REC_READ_VALUE;
    }
    
    public void setTrailerRecRead() {
        wsTrailerRecReadFlag = TRAILER_REC_READ_VALUE;
    }
    
    public boolean isTrailerRecNotRead() {
        return wsTrailerRecReadFlag == TRAILER_REC_NOT_READ_VALUE;
    }
    
    public void setTrailerRecNotRead() {
        wsTrailerRecReadFlag = TRAILER_REC_NOT_READ_VALUE;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        wsEofSw = ' ';
        wsCpuEofSw = ' ';
        wsHeaderRecReadFlag = ' ';
        wsDetailRecReadFlag = ' ';
        wsTrailerRecReadFlag = ' ';
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ wsEofSw=\"");
        s.append(getWsEofSw());
        s.append("\"");
        s.append(", wsCpuEofSw=\"");
        s.append(getWsCpuEofSw());
        s.append("\"");
        s.append(", wsHeaderRecReadFlag=\"");
        s.append(getWsHeaderRecReadFlag());
        s.append("\"");
        s.append(", wsDetailRecReadFlag=\"");
        s.append(getWsDetailRecReadFlag());
        s.append("\"");
        s.append(", wsTrailerRecReadFlag=\"");
        s.append(getWsTrailerRecReadFlag());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(WsFlags that) {
        return this.wsEofSw == that.wsEofSw &&
            this.wsCpuEofSw == that.wsCpuEofSw &&
            this.wsHeaderRecReadFlag == that.wsHeaderRecReadFlag &&
            this.wsDetailRecReadFlag == that.wsDetailRecReadFlag &&
            this.wsTrailerRecReadFlag == that.wsTrailerRecReadFlag;
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof WsFlags other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof WsFlags;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Character.hashCode(wsEofSw);
        result = 31 * result + Character.hashCode(wsCpuEofSw);
        result = 31 * result + Character.hashCode(wsHeaderRecReadFlag);
        result = 31 * result + Character.hashCode(wsDetailRecReadFlag);
        result = 31 * result + Character.hashCode(wsTrailerRecReadFlag);
        return result;
    }
    
    @Override
    public int compareTo(WsFlags that) {
        int c = 0;
        c = Character.compare(this.wsEofSw, that.wsEofSw);
        if ( c != 0 ) return c;
        c = Character.compare(this.wsCpuEofSw, that.wsCpuEofSw);
        if ( c != 0 ) return c;
        c = Character.compare(this.wsHeaderRecReadFlag, that.wsHeaderRecReadFlag);
        if ( c != 0 ) return c;
        c = Character.compare(this.wsDetailRecReadFlag, that.wsDetailRecReadFlag);
        if ( c != 0 ) return c;
        c = Character.compare(this.wsTrailerRecReadFlag, that.wsTrailerRecReadFlag);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final StringField WS_EOF_SW = factory.getStringField(1);
    private static final StringField WS_CPU_EOF_SW = factory.getStringField(1);
    private static final StringField WS_HEADER_REC_READ_FLAG = factory.getStringField(1);
    private static final StringField WS_DETAIL_REC_READ_FLAG = factory.getStringField(1);
    private static final StringField WS_TRAILER_REC_READ_FLAG = factory.getStringField(1);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsFlags} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code WS-FLAGS} record
     * @see "WS-FLAGS record at RXBPASVC.cbl:83"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        WS_EOF_SW.putString(Character.toString(wsEofSw), bytes, offset);
        WS_CPU_EOF_SW.putString(Character.toString(wsCpuEofSw), bytes, offset);
        WS_HEADER_REC_READ_FLAG.putString(Character.toString(wsHeaderRecReadFlag), bytes, offset);
        WS_DETAIL_REC_READ_FLAG.putString(Character.toString(wsDetailRecReadFlag), bytes, offset);
        WS_TRAILER_REC_READ_FLAG.putString(Character.toString(wsTrailerRecReadFlag), bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsFlags} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsFlags} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code WsFlags} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code WS-FLAGS} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "WS-FLAGS record at RXBPASVC.cbl:83"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        wsEofSw = WS_EOF_SW.getString(bytes, offset).charAt(0);
        wsCpuEofSw = WS_CPU_EOF_SW.getString(bytes, offset).charAt(0);
        wsHeaderRecReadFlag = WS_HEADER_REC_READ_FLAG.getString(bytes, offset).charAt(0);
        wsDetailRecReadFlag = WS_DETAIL_REC_READ_FLAG.getString(bytes, offset).charAt(0);
        wsTrailerRecReadFlag = WS_TRAILER_REC_READ_FLAG.getString(bytes, offset).charAt(0);
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
