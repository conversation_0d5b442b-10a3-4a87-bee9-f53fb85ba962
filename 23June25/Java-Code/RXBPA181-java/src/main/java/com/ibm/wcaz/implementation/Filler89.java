package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class Filler89 extends DateParmArea {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String oShortDayName = "";
    
    /** Initialize fields to non-null default values */
    public Filler89() {}
    
    /** Initialize all fields to provided values */
    public Filler89(int reqDay, int manipulateReqType, int numberOfDays, int numberOfMonths, int inputDateType, int oIsoCc, int oIsoYy, int oIsoMm, int oIsoDd, int oUsaMm, int oUsaDd, int oUsaCc, int oUsaYy, int oEurDd, int oEurMm, int oEurCc, int oEurYy, int oJisCc, int oJisYy, int oJisMm, int oJisDd, int oMdySlshMm, int oMdySlshDd, int oMdySlshYy, int oMdyMm, int oMdyDd, int oMdyYy, int oMdcySlshMm, int oMdcySlshDd, int oMdcySlshCc, int oMdcySlshYy, int oMdcyMm, int oMdcyDd, int oMdcyCc, int oMdcyYy, int oYmdYy, int oYmdMm, int oYmdDd, int oCymdCc, int oCymdYy, int oCymdMm, int oCymdDd, int oJulYy, int oJulDdd, String oExpMm, int oExpDd, int oExpCc, int oExpYy, int differenceInDays, int oDayNumber, String reqCountry, OutputNextMoYr outputNextMoYr, OutputLastMoYr outputLastMoYr, String oShortDayName) {
        super(reqDay, manipulateReqType, numberOfDays, numberOfMonths, inputDateType, oIsoCc, oIsoYy, oIsoMm, oIsoDd, oUsaMm, oUsaDd, oUsaCc, oUsaYy, oEurDd, oEurMm, oEurCc, oEurYy, oJisCc, oJisYy, oJisMm, oJisDd, oMdySlshMm, oMdySlshDd, oMdySlshYy, oMdyMm, oMdyDd, oMdyYy, oMdcySlshMm, oMdcySlshDd, oMdcySlshCc, oMdcySlshYy, oMdcyMm, oMdcyDd, oMdcyCc, oMdcyYy, oYmdYy, oYmdMm, oYmdDd, oCymdCc, oCymdYy, oCymdMm, oCymdDd, oJulYy, oJulDdd, oExpMm, oExpDd, oExpCc, oExpYy, differenceInDays, oDayNumber, reqCountry, outputNextMoYr, outputLastMoYr);
        this.oShortDayName = oShortDayName;
    }
    
    @Override
    public Filler89 clone() throws CloneNotSupportedException {
        Filler89 cloned = (Filler89) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code Filler89} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected Filler89(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code Filler89} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected Filler89(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code Filler89} object
     * @see #setBytes(byte[], int)
     */
    public static Filler89 fromBytes(byte[] bytes, int offset) {
        return new Filler89(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code Filler89} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static Filler89 fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code Filler89} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static Filler89 fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getOShortDayName() {
        return this.oShortDayName;
    }
    
    public void setOShortDayName(String oShortDayName) {
        this.oShortDayName = oShortDayName;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        oShortDayName = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ oShortDayName=\"");
        s.append(getOShortDayName());
        s.append("\"");
        s.append(", filler90=\"");
        s.append(new String(filler90, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(Filler89 that) {
        return super.equals(that) &&
            this.oShortDayName.equals(that.oShortDayName) &&
            Arrays.equals(this.filler90, that.filler90);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof Filler89 other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof Filler89;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(oShortDayName);
        result = 31 * result + Arrays.hashCode(filler90);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(Filler89 that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.oShortDayName.compareTo(that.oShortDayName);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler90, that.filler90);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(DateParmArea that) {
        if (that instanceof Filler89 other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(DateParmArea.SIZE);
    }
    
    private static final StringField O_SHORT_DAY_NAME = factory.getStringField(3);
    private static final ByteArrayField FILLER_90 = factory.getByteArrayField(6);
    private byte[] filler90 = new byte[6];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "FILLER #89 record at MXWW01.CPY:357"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        O_SHORT_DAY_NAME.putString(oShortDayName, bytes, offset);
        FILLER_90.putByteArray(filler90, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#setBytes(byte[], int)} to set parent-class state.
     * @see "FILLER #89 record at MXWW01.CPY:357"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        oShortDayName = O_SHORT_DAY_NAME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_90.getByteArray(bytes, offset);
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
