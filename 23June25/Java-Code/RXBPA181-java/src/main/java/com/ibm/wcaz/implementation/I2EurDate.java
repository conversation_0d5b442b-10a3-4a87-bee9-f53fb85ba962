package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class I2EurDate extends DateParmArea {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String i2EurDd = "";
    private String i2EurMm = "";
    private String i2EurCc = "";
    private String i2EurYy = "";
    
    /** Initialize fields to non-null default values */
    public I2EurDate() {}
    
    /** Initialize all fields to provided values */
    public I2EurDate(int reqDay, int manipulateReqType, int numberOfDays, int numberOfMonths, int inputDateType, int oIsoCc, int oIsoYy, int oIsoMm, int oIsoDd, int oUsaMm, int oUsaDd, int oUsaCc, int oUsaYy, int oEurDd, int oEurMm, int oEurCc, int oEurYy, int oJisCc, int oJisYy, int oJisMm, int oJisDd, int oMdySlshMm, int oMdySlshDd, int oMdySlshYy, int oMdyMm, int oMdyDd, int oMdyYy, int oMdcySlshMm, int oMdcySlshDd, int oMdcySlshCc, int oMdcySlshYy, int oMdcyMm, int oMdcyDd, int oMdcyCc, int oMdcyYy, int oYmdYy, int oYmdMm, int oYmdDd, int oCymdCc, int oCymdYy, int oCymdMm, int oCymdDd, int oJulYy, int oJulDdd, String oExpMm, int oExpDd, int oExpCc, int oExpYy, int differenceInDays, int oDayNumber, String reqCountry, OutputNextMoYr outputNextMoYr, OutputLastMoYr outputLastMoYr, String i2EurDd, String i2EurMm, String i2EurCc, String i2EurYy) {
        super(reqDay, manipulateReqType, numberOfDays, numberOfMonths, inputDateType, oIsoCc, oIsoYy, oIsoMm, oIsoDd, oUsaMm, oUsaDd, oUsaCc, oUsaYy, oEurDd, oEurMm, oEurCc, oEurYy, oJisCc, oJisYy, oJisMm, oJisDd, oMdySlshMm, oMdySlshDd, oMdySlshYy, oMdyMm, oMdyDd, oMdyYy, oMdcySlshMm, oMdcySlshDd, oMdcySlshCc, oMdcySlshYy, oMdcyMm, oMdcyDd, oMdcyCc, oMdcyYy, oYmdYy, oYmdMm, oYmdDd, oCymdCc, oCymdYy, oCymdMm, oCymdDd, oJulYy, oJulDdd, oExpMm, oExpDd, oExpCc, oExpYy, differenceInDays, oDayNumber, reqCountry, outputNextMoYr, outputLastMoYr);
        this.i2EurDd = i2EurDd;
        this.i2EurMm = i2EurMm;
        this.i2EurCc = i2EurCc;
        this.i2EurYy = i2EurYy;
    }
    
    @Override
    public I2EurDate clone() throws CloneNotSupportedException {
        I2EurDate cloned = (I2EurDate) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code I2EurDate} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected I2EurDate(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code I2EurDate} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected I2EurDate(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I2EurDate} object
     * @see #setBytes(byte[], int)
     */
    public static I2EurDate fromBytes(byte[] bytes, int offset) {
        return new I2EurDate(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I2EurDate} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static I2EurDate fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code I2EurDate} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static I2EurDate fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getI2EurDd() {
        return this.i2EurDd;
    }
    
    public void setI2EurDd(String i2EurDd) {
        this.i2EurDd = i2EurDd;
    }
    
    public String getI2EurMm() {
        return this.i2EurMm;
    }
    
    public void setI2EurMm(String i2EurMm) {
        this.i2EurMm = i2EurMm;
    }
    
    public String getI2EurCc() {
        return this.i2EurCc;
    }
    
    public void setI2EurCc(String i2EurCc) {
        this.i2EurCc = i2EurCc;
    }
    
    public String getI2EurYy() {
        return this.i2EurYy;
    }
    
    public void setI2EurYy(String i2EurYy) {
        this.i2EurYy = i2EurYy;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        i2EurDd = "";
        i2EurMm = "";
        i2EurCc = "";
        i2EurYy = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ i2EurDd=\"");
        s.append(getI2EurDd());
        s.append("\"");
        s.append(", filler55=\"");
        s.append(new String(filler55, encoding));
        s.append("\"");
        s.append(", i2EurMm=\"");
        s.append(getI2EurMm());
        s.append("\"");
        s.append(", filler56=\"");
        s.append(new String(filler56, encoding));
        s.append("\"");
        s.append(", i2EurCc=\"");
        s.append(getI2EurCc());
        s.append("\"");
        s.append(", i2EurYy=\"");
        s.append(getI2EurYy());
        s.append("\"");
        s.append(", filler57=\"");
        s.append(new String(filler57, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(I2EurDate that) {
        return super.equals(that) &&
            this.i2EurDd.equals(that.i2EurDd) &&
            Arrays.equals(this.filler55, that.filler55) &&
            this.i2EurMm.equals(that.i2EurMm) &&
            Arrays.equals(this.filler56, that.filler56) &&
            this.i2EurCc.equals(that.i2EurCc) &&
            this.i2EurYy.equals(that.i2EurYy) &&
            Arrays.equals(this.filler57, that.filler57);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof I2EurDate other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof I2EurDate;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(i2EurDd);
        result = 31 * result + Arrays.hashCode(filler55);
        result = 31 * result + Objects.hashCode(i2EurMm);
        result = 31 * result + Arrays.hashCode(filler56);
        result = 31 * result + Objects.hashCode(i2EurCc);
        result = 31 * result + Objects.hashCode(i2EurYy);
        result = 31 * result + Arrays.hashCode(filler57);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(I2EurDate that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.i2EurDd.compareTo(that.i2EurDd);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler55, that.filler55);
        if ( c != 0 ) return c;
        c = this.i2EurMm.compareTo(that.i2EurMm);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler56, that.filler56);
        if ( c != 0 ) return c;
        c = this.i2EurCc.compareTo(that.i2EurCc);
        if ( c != 0 ) return c;
        c = this.i2EurYy.compareTo(that.i2EurYy);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler57, that.filler57);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(DateParmArea that) {
        if (that instanceof I2EurDate other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(DateParmArea.SIZE);
    }
    
    private static final StringField I_2_EUR_DD = factory.getStringField(2);
    private static final ByteArrayField FILLER_55 = factory.getByteArrayField(1);
    private byte[] filler55 = new byte[1];
    private static final StringField I_2_EUR_MM = factory.getStringField(2);
    private static final ByteArrayField FILLER_56 = factory.getByteArrayField(1);
    private byte[] filler56 = new byte[1];
    private static final StringField I_2_EUR_CC = factory.getStringField(2);
    private static final StringField I_2_EUR_YY = factory.getStringField(2);
    private static final ByteArrayField FILLER_57 = factory.getByteArrayField(2);
    private byte[] filler57 = new byte[2];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "I2-EUR-DATE record at MXWW01.CPY:199"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        I_2_EUR_DD.putString(i2EurDd, bytes, offset);
        FILLER_55.putByteArray(filler55, bytes, offset);
        I_2_EUR_MM.putString(i2EurMm, bytes, offset);
        FILLER_56.putByteArray(filler56, bytes, offset);
        I_2_EUR_CC.putString(i2EurCc, bytes, offset);
        I_2_EUR_YY.putString(i2EurYy, bytes, offset);
        FILLER_57.putByteArray(filler57, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#setBytes(byte[], int)} to set parent-class state.
     * @see "I2-EUR-DATE record at MXWW01.CPY:199"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        i2EurDd = I_2_EUR_DD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_55.getByteArray(bytes, offset);
        i2EurMm = I_2_EUR_MM.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_56.getByteArray(bytes, offset);
        i2EurCc = I_2_EUR_CC.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        i2EurYy = I_2_EUR_YY.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_57.getByteArray(bytes, offset);
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
