package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class WsDlrNoChar extends WsFields {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String wsDlrNo1St3 = "";
    private String wsDlrNoLast6 = "";
    
    /** Initialize fields to non-null default values */
    public WsDlrNoChar() {}
    
    /** Initialize all fields to provided values */
    public WsDlrNoChar(String wsCpuCode, int wsFileSeqNo, int wsItemSeqNo, int wsDlrNo, String wsDlrNo1St3, String wsDlrNoLast6) {
        super(wsCpuCode, wsFileSeqNo, wsItemSeqNo, wsDlrNo);
        this.wsDlrNo1St3 = wsDlrNo1St3;
        this.wsDlrNoLast6 = wsDlrNoLast6;
    }
    
    @Override
    public WsDlrNoChar clone() throws CloneNotSupportedException {
        WsDlrNoChar cloned = (WsDlrNoChar) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code WsDlrNoChar} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected WsDlrNoChar(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code WsDlrNoChar} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected WsDlrNoChar(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsDlrNoChar} object
     * @see #setBytes(byte[], int)
     */
    public static WsDlrNoChar fromBytes(byte[] bytes, int offset) {
        return new WsDlrNoChar(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsDlrNoChar} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static WsDlrNoChar fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code WsDlrNoChar} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static WsDlrNoChar fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getWsDlrNo1St3() {
        return this.wsDlrNo1St3;
    }
    
    public void setWsDlrNo1St3(String wsDlrNo1St3) {
        this.wsDlrNo1St3 = wsDlrNo1St3;
    }
    
    public String getWsDlrNoLast6() {
        return this.wsDlrNoLast6;
    }
    
    public void setWsDlrNoLast6(String wsDlrNoLast6) {
        this.wsDlrNoLast6 = wsDlrNoLast6;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        wsDlrNo1St3 = "";
        wsDlrNoLast6 = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ wsDlrNo1St3=\"");
        s.append(getWsDlrNo1St3());
        s.append("\"");
        s.append(", wsDlrNoLast6=\"");
        s.append(getWsDlrNoLast6());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(WsDlrNoChar that) {
        return super.equals(that) &&
            this.wsDlrNo1St3.equals(that.wsDlrNo1St3) &&
            this.wsDlrNoLast6.equals(that.wsDlrNoLast6);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof WsDlrNoChar other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof WsDlrNoChar;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(wsDlrNo1St3);
        result = 31 * result + Objects.hashCode(wsDlrNoLast6);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(WsDlrNoChar that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.wsDlrNo1St3.compareTo(that.wsDlrNo1St3);
        if ( c != 0 ) return c;
        c = this.wsDlrNoLast6.compareTo(that.wsDlrNoLast6);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(WsFields that) {
        if (that instanceof WsDlrNoChar other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(WsFields.SIZE);
    }
    
    private static final StringField WS_DLR_NO_1_ST_3 = factory.getStringField(3);
    private static final StringField WS_DLR_NO_LAST_6 = factory.getStringField(6);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link WsFields#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "WS-DLR-NO-CHAR record at RXBPASVC.cbl:153"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        WS_DLR_NO_1_ST_3.putString(wsDlrNo1St3, bytes, offset);
        WS_DLR_NO_LAST_6.putString(wsDlrNoLast6, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link WsFields#setBytes(byte[], int)} to set parent-class state.
     * @see "WS-DLR-NO-CHAR record at RXBPASVC.cbl:153"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        wsDlrNo1St3 = WS_DLR_NO_1_ST_3.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        wsDlrNoLast6 = WS_DLR_NO_LAST_6.getString(bytes, offset).replaceFirst("\\s+\\z", "");
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
