package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.ExternalDecimalAsIntField;
import java.nio.charset.Charset;
import java.util.Arrays;

public class Filler94 extends OutputLastMoYr {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private int outputLastCent;
    
    /** Initialize fields to non-null default values */
    public Filler94() {}
    
    /** Initialize all fields to provided values */
    public Filler94(int outputLastMonth, int outputLastCent) {
        super(outputLastMonth);
        this.outputLastCent = outputLastCent;
    }
    
    @Override
    public Filler94 clone() throws CloneNotSupportedException {
        Filler94 cloned = (Filler94) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code Filler94} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected Filler94(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code Filler94} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected Filler94(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code Filler94} object
     * @see #setBytes(byte[], int)
     */
    public static Filler94 fromBytes(byte[] bytes, int offset) {
        return new Filler94(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code Filler94} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static Filler94 fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code Filler94} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static Filler94 fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public int getOutputLastCent() {
        return this.outputLastCent;
    }
    
    public void setOutputLastCent(int outputLastCent) {
        this.outputLastCent = outputLastCent;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        outputLastCent = 0;
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ outputLastCent=\"");
        s.append(getOutputLastCent());
        s.append("\"");
        s.append(", filler95=\"");
        s.append(new String(filler95, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(Filler94 that) {
        return super.equals(that) &&
            this.outputLastCent == that.outputLastCent &&
            Arrays.equals(this.filler95, that.filler95);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof Filler94 other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof Filler94;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Integer.hashCode(outputLastCent);
        result = 31 * result + Arrays.hashCode(filler95);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(Filler94 that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = Integer.compare(this.outputLastCent, that.outputLastCent);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler95, that.filler95);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(OutputLastMoYr that) {
        if (that instanceof Filler94 other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(OutputLastMoYr.SIZE);
    }
    
    private static final ExternalDecimalAsIntField OUTPUT_LAST_CENT = factory.getExternalDecimalAsIntField(2, true);
    private static final ByteArrayField FILLER_95 = factory.getByteArrayField(2);
    private byte[] filler95 = new byte[2];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link OutputLastMoYr#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "FILLER #94 record at MXWW01.CPY:394"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        OUTPUT_LAST_CENT.putInt(outputLastCent, bytes, offset);
        FILLER_95.putByteArray(filler95, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link OutputLastMoYr#setBytes(byte[], int)} to set parent-class state.
     * @see "FILLER #94 record at MXWW01.CPY:394"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        outputLastCent = OUTPUT_LAST_CENT.getInt(bytes, offset);
        FILLER_95.getByteArray(bytes, offset);
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
