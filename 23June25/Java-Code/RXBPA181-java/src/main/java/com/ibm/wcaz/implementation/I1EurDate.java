package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class I1EurDate extends DateParmArea {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String i1EurDd = "";
    private String i1EurMm = "";
    private String i1EurCc = "";
    private String i1EurYy = "";
    
    /** Initialize fields to non-null default values */
    public I1EurDate() {}
    
    /** Initialize all fields to provided values */
    public I1EurDate(int reqDay, int manipulateReqType, int numberOfDays, int numberOfMonths, int inputDateType, int oIsoCc, int oIsoYy, int oIsoMm, int oIsoDd, int oUsaMm, int oUsaDd, int oUsaCc, int oUsaYy, int oEurDd, int oEurMm, int oEurCc, int oEurYy, int oJisCc, int oJisYy, int oJisMm, int oJisDd, int oMdySlshMm, int oMdySlshDd, int oMdySlshYy, int oMdyMm, int oMdyDd, int oMdyYy, int oMdcySlshMm, int oMdcySlshDd, int oMdcySlshCc, int oMdcySlshYy, int oMdcyMm, int oMdcyDd, int oMdcyCc, int oMdcyYy, int oYmdYy, int oYmdMm, int oYmdDd, int oCymdCc, int oCymdYy, int oCymdMm, int oCymdDd, int oJulYy, int oJulDdd, String oExpMm, int oExpDd, int oExpCc, int oExpYy, int differenceInDays, int oDayNumber, String reqCountry, OutputNextMoYr outputNextMoYr, OutputLastMoYr outputLastMoYr, String i1EurDd, String i1EurMm, String i1EurCc, String i1EurYy) {
        super(reqDay, manipulateReqType, numberOfDays, numberOfMonths, inputDateType, oIsoCc, oIsoYy, oIsoMm, oIsoDd, oUsaMm, oUsaDd, oUsaCc, oUsaYy, oEurDd, oEurMm, oEurCc, oEurYy, oJisCc, oJisYy, oJisMm, oJisDd, oMdySlshMm, oMdySlshDd, oMdySlshYy, oMdyMm, oMdyDd, oMdyYy, oMdcySlshMm, oMdcySlshDd, oMdcySlshCc, oMdcySlshYy, oMdcyMm, oMdcyDd, oMdcyCc, oMdcyYy, oYmdYy, oYmdMm, oYmdDd, oCymdCc, oCymdYy, oCymdMm, oCymdDd, oJulYy, oJulDdd, oExpMm, oExpDd, oExpCc, oExpYy, differenceInDays, oDayNumber, reqCountry, outputNextMoYr, outputLastMoYr);
        this.i1EurDd = i1EurDd;
        this.i1EurMm = i1EurMm;
        this.i1EurCc = i1EurCc;
        this.i1EurYy = i1EurYy;
    }
    
    @Override
    public I1EurDate clone() throws CloneNotSupportedException {
        I1EurDate cloned = (I1EurDate) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code I1EurDate} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected I1EurDate(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code I1EurDate} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected I1EurDate(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I1EurDate} object
     * @see #setBytes(byte[], int)
     */
    public static I1EurDate fromBytes(byte[] bytes, int offset) {
        return new I1EurDate(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I1EurDate} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static I1EurDate fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code I1EurDate} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static I1EurDate fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getI1EurDd() {
        return this.i1EurDd;
    }
    
    public void setI1EurDd(String i1EurDd) {
        this.i1EurDd = i1EurDd;
    }
    
    public String getI1EurMm() {
        return this.i1EurMm;
    }
    
    public void setI1EurMm(String i1EurMm) {
        this.i1EurMm = i1EurMm;
    }
    
    public String getI1EurCc() {
        return this.i1EurCc;
    }
    
    public void setI1EurCc(String i1EurCc) {
        this.i1EurCc = i1EurCc;
    }
    
    public String getI1EurYy() {
        return this.i1EurYy;
    }
    
    public void setI1EurYy(String i1EurYy) {
        this.i1EurYy = i1EurYy;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        i1EurDd = "";
        i1EurMm = "";
        i1EurCc = "";
        i1EurYy = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ i1EurDd=\"");
        s.append(getI1EurDd());
        s.append("\"");
        s.append(", filler30=\"");
        s.append(new String(filler30, encoding));
        s.append("\"");
        s.append(", i1EurMm=\"");
        s.append(getI1EurMm());
        s.append("\"");
        s.append(", filler31=\"");
        s.append(new String(filler31, encoding));
        s.append("\"");
        s.append(", i1EurCc=\"");
        s.append(getI1EurCc());
        s.append("\"");
        s.append(", i1EurYy=\"");
        s.append(getI1EurYy());
        s.append("\"");
        s.append(", filler32=\"");
        s.append(new String(filler32, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(I1EurDate that) {
        return super.equals(that) &&
            this.i1EurDd.equals(that.i1EurDd) &&
            Arrays.equals(this.filler30, that.filler30) &&
            this.i1EurMm.equals(that.i1EurMm) &&
            Arrays.equals(this.filler31, that.filler31) &&
            this.i1EurCc.equals(that.i1EurCc) &&
            this.i1EurYy.equals(that.i1EurYy) &&
            Arrays.equals(this.filler32, that.filler32);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof I1EurDate other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof I1EurDate;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(i1EurDd);
        result = 31 * result + Arrays.hashCode(filler30);
        result = 31 * result + Objects.hashCode(i1EurMm);
        result = 31 * result + Arrays.hashCode(filler31);
        result = 31 * result + Objects.hashCode(i1EurCc);
        result = 31 * result + Objects.hashCode(i1EurYy);
        result = 31 * result + Arrays.hashCode(filler32);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(I1EurDate that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.i1EurDd.compareTo(that.i1EurDd);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler30, that.filler30);
        if ( c != 0 ) return c;
        c = this.i1EurMm.compareTo(that.i1EurMm);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler31, that.filler31);
        if ( c != 0 ) return c;
        c = this.i1EurCc.compareTo(that.i1EurCc);
        if ( c != 0 ) return c;
        c = this.i1EurYy.compareTo(that.i1EurYy);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler32, that.filler32);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(DateParmArea that) {
        if (that instanceof I1EurDate other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(DateParmArea.SIZE);
    }
    
    private static final StringField I_1_EUR_DD = factory.getStringField(2);
    private static final ByteArrayField FILLER_30 = factory.getByteArrayField(1);
    private byte[] filler30 = new byte[1];
    private static final StringField I_1_EUR_MM = factory.getStringField(2);
    private static final ByteArrayField FILLER_31 = factory.getByteArrayField(1);
    private byte[] filler31 = new byte[1];
    private static final StringField I_1_EUR_CC = factory.getStringField(2);
    private static final StringField I_1_EUR_YY = factory.getStringField(2);
    private static final ByteArrayField FILLER_32 = factory.getByteArrayField(2);
    private byte[] filler32 = new byte[2];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "I1-EUR-DATE record at MXWW01.CPY:110"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        I_1_EUR_DD.putString(i1EurDd, bytes, offset);
        FILLER_30.putByteArray(filler30, bytes, offset);
        I_1_EUR_MM.putString(i1EurMm, bytes, offset);
        FILLER_31.putByteArray(filler31, bytes, offset);
        I_1_EUR_CC.putString(i1EurCc, bytes, offset);
        I_1_EUR_YY.putString(i1EurYy, bytes, offset);
        FILLER_32.putByteArray(filler32, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#setBytes(byte[], int)} to set parent-class state.
     * @see "I1-EUR-DATE record at MXWW01.CPY:110"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        i1EurDd = I_1_EUR_DD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_30.getByteArray(bytes, offset);
        i1EurMm = I_1_EUR_MM.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_31.getByteArray(bytes, offset);
        i1EurCc = I_1_EUR_CC.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        i1EurYy = I_1_EUR_YY.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_32.getByteArray(bytes, offset);
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
