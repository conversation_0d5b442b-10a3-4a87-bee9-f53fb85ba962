package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class CpuRegdtlData extends CpuRegdtlLayout {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private static final String CPU_REG_HDR_REC_VALUE = "URBH";
    private static final String CPU_REG_DTL_REC_VALUE = "URBD";
    private static final String CPU_REG_TRL_REC_VALUE = "URBZ";
    
    private String cpuRegdtlRecordType = "";
    private String cpuRegdtlCdfCustId = "";
    private String cpuRegdtlCdfCustCode = "";
    private String cpuRegdtlCreatDate = "";
    private String cpuRegdtlCreatTime = "";
    
    /** Initialize fields to non-null default values */
    public CpuRegdtlData() {}
    
    /** Initialize all fields to provided values */
    public CpuRegdtlData(String cpuRegdtlRecordType, String cpuRegdtlCdfCustId, String cpuRegdtlCdfCustCode, String cpuRegdtlCreatDate, String cpuRegdtlCreatTime) {
        this.cpuRegdtlRecordType = cpuRegdtlRecordType;
        this.cpuRegdtlCdfCustId = cpuRegdtlCdfCustId;
        this.cpuRegdtlCdfCustCode = cpuRegdtlCdfCustCode;
        this.cpuRegdtlCreatDate = cpuRegdtlCreatDate;
        this.cpuRegdtlCreatTime = cpuRegdtlCreatTime;
    }
    
    @Override
    public CpuRegdtlData clone() throws CloneNotSupportedException {
        CpuRegdtlData cloned = (CpuRegdtlData) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code CpuRegdtlData} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected CpuRegdtlData(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code CpuRegdtlData} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected CpuRegdtlData(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code CpuRegdtlData} object
     * @see #setBytes(byte[], int)
     */
    public static CpuRegdtlData fromBytes(byte[] bytes, int offset) {
        return new CpuRegdtlData(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code CpuRegdtlData} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static CpuRegdtlData fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code CpuRegdtlData} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static CpuRegdtlData fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getCpuRegdtlRecordType() {
        return this.cpuRegdtlRecordType;
    }
    
    public void setCpuRegdtlRecordType(String cpuRegdtlRecordType) {
        this.cpuRegdtlRecordType = cpuRegdtlRecordType;
    }
    
    public boolean isCpuRegHdrRec() {
        return cpuRegdtlRecordType.equals(CPU_REG_HDR_REC_VALUE);
    }
    
    public void setCpuRegHdrRec() {
        cpuRegdtlRecordType = CPU_REG_HDR_REC_VALUE;
    }
    
    public boolean isCpuRegDtlRec() {
        return cpuRegdtlRecordType.equals(CPU_REG_DTL_REC_VALUE);
    }
    
    public void setCpuRegDtlRec() {
        cpuRegdtlRecordType = CPU_REG_DTL_REC_VALUE;
    }
    
    public boolean isCpuRegTrlRec() {
        return cpuRegdtlRecordType.equals(CPU_REG_TRL_REC_VALUE);
    }
    
    public void setCpuRegTrlRec() {
        cpuRegdtlRecordType = CPU_REG_TRL_REC_VALUE;
    }
    
    public String getCpuRegdtlCdfCustId() {
        return this.cpuRegdtlCdfCustId;
    }
    
    public void setCpuRegdtlCdfCustId(String cpuRegdtlCdfCustId) {
        this.cpuRegdtlCdfCustId = cpuRegdtlCdfCustId;
    }
    
    public String getCpuRegdtlCdfCustCode() {
        return this.cpuRegdtlCdfCustCode;
    }
    
    public void setCpuRegdtlCdfCustCode(String cpuRegdtlCdfCustCode) {
        this.cpuRegdtlCdfCustCode = cpuRegdtlCdfCustCode;
    }
    
    public String getCpuRegdtlCreatDate() {
        return this.cpuRegdtlCreatDate;
    }
    
    public void setCpuRegdtlCreatDate(String cpuRegdtlCreatDate) {
        this.cpuRegdtlCreatDate = cpuRegdtlCreatDate;
    }
    
    public String getCpuRegdtlCreatTime() {
        return this.cpuRegdtlCreatTime;
    }
    
    public void setCpuRegdtlCreatTime(String cpuRegdtlCreatTime) {
        this.cpuRegdtlCreatTime = cpuRegdtlCreatTime;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        cpuRegdtlRecordType = "";
        cpuRegdtlCdfCustId = "";
        cpuRegdtlCdfCustCode = "";
        cpuRegdtlCreatDate = "";
        cpuRegdtlCreatTime = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ cpuRegdtlRecordType=\"");
        s.append(getCpuRegdtlRecordType());
        s.append("\"");
        s.append(", cpuRegdtlCdfCustId=\"");
        s.append(getCpuRegdtlCdfCustId());
        s.append("\"");
        s.append(", cpuRegdtlCdfCustCode=\"");
        s.append(getCpuRegdtlCdfCustCode());
        s.append("\"");
        s.append(", cpuRegdtlCreatDate=\"");
        s.append(getCpuRegdtlCreatDate());
        s.append("\"");
        s.append(", cpuRegdtlCreatTime=\"");
        s.append(getCpuRegdtlCreatTime());
        s.append("\"");
        s.append(", filler2=\"");
        s.append(new String(filler2, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(CpuRegdtlData that) {
        return super.equals(that) &&
            this.cpuRegdtlRecordType.equals(that.cpuRegdtlRecordType) &&
            this.cpuRegdtlCdfCustId.equals(that.cpuRegdtlCdfCustId) &&
            this.cpuRegdtlCdfCustCode.equals(that.cpuRegdtlCdfCustCode) &&
            this.cpuRegdtlCreatDate.equals(that.cpuRegdtlCreatDate) &&
            this.cpuRegdtlCreatTime.equals(that.cpuRegdtlCreatTime) &&
            Arrays.equals(this.filler2, that.filler2);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof CpuRegdtlData other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof CpuRegdtlData;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(cpuRegdtlRecordType);
        result = 31 * result + Objects.hashCode(cpuRegdtlCdfCustId);
        result = 31 * result + Objects.hashCode(cpuRegdtlCdfCustCode);
        result = 31 * result + Objects.hashCode(cpuRegdtlCreatDate);
        result = 31 * result + Objects.hashCode(cpuRegdtlCreatTime);
        result = 31 * result + Arrays.hashCode(filler2);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(CpuRegdtlData that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.cpuRegdtlRecordType.compareTo(that.cpuRegdtlRecordType);
        if ( c != 0 ) return c;
        c = this.cpuRegdtlCdfCustId.compareTo(that.cpuRegdtlCdfCustId);
        if ( c != 0 ) return c;
        c = this.cpuRegdtlCdfCustCode.compareTo(that.cpuRegdtlCdfCustCode);
        if ( c != 0 ) return c;
        c = this.cpuRegdtlCreatDate.compareTo(that.cpuRegdtlCreatDate);
        if ( c != 0 ) return c;
        c = this.cpuRegdtlCreatTime.compareTo(that.cpuRegdtlCreatTime);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler2, that.filler2);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(CpuRegdtlLayout that) {
        if (that instanceof CpuRegdtlData other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(CpuRegdtlLayout.SIZE);
    }
    
    private static final StringField CPU_REGDTL_RECORD_TYPE = factory.getStringField(4);
    private static final StringField CPU_REGDTL_CDF_CUST_ID = factory.getStringField(4);
    private static final StringField CPU_REGDTL_CDF_CUST_CODE = factory.getStringField(4);
    private static final StringField CPU_REGDTL_CREAT_DATE = factory.getStringField(10);
    private static final StringField CPU_REGDTL_CREAT_TIME = factory.getStringField(5);
    private static final ByteArrayField FILLER_2 = factory.getByteArrayField(173);
    private byte[] filler2 = new byte[173];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link CpuRegdtlLayout#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "CPU-REGDTL-DATA record at RXBPASVC.cbl:36"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        CPU_REGDTL_RECORD_TYPE.putString(cpuRegdtlRecordType, bytes, offset);
        CPU_REGDTL_CDF_CUST_ID.putString(cpuRegdtlCdfCustId, bytes, offset);
        CPU_REGDTL_CDF_CUST_CODE.putString(cpuRegdtlCdfCustCode, bytes, offset);
        CPU_REGDTL_CREAT_DATE.putString(cpuRegdtlCreatDate, bytes, offset);
        CPU_REGDTL_CREAT_TIME.putString(cpuRegdtlCreatTime, bytes, offset);
        FILLER_2.putByteArray(filler2, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link CpuRegdtlLayout#setBytes(byte[], int)} to set parent-class state.
     * @see "CPU-REGDTL-DATA record at RXBPASVC.cbl:36"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        cpuRegdtlRecordType = CPU_REGDTL_RECORD_TYPE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        cpuRegdtlCdfCustId = CPU_REGDTL_CDF_CUST_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        cpuRegdtlCdfCustCode = CPU_REGDTL_CDF_CUST_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        cpuRegdtlCreatDate = CPU_REGDTL_CREAT_DATE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        cpuRegdtlCreatTime = CPU_REGDTL_CREAT_TIME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_2.getByteArray(bytes, offset);
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
