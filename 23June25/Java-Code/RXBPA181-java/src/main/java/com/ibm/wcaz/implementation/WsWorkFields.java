package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.ExternalDecimalAsIntField;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class WsWorkFields implements Cloneable, Comparable<WsWorkFields> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private static final String VALID_REG_TYPES_VALUE_1 = "OTH";
    private static final String VALID_REG_TYPES_VALUE_2 = "DEM";
    private static final String VALID_REG_TYPES_VALUE_3 = "PRG";
    
    private String wsPgmName = "MXBPA181";
    private String wsDateRoutine = "MXBPW003";
    private String ws3ACpuid = "1133";
    private String wsCpuFileStatus = "";
    private String wsGoodStatus = "20";
    private WsCpuDate wsCpuDate = new WsCpuDate();
    private String wsProcDate = "";
    private String wsRegComplDate = "";
    private WsTime wsTime = new WsTime();
    private String wsRegType = "";
    private int wsDetailRecCnt = 0;
    private int wsTrailerRecCnt = 0;
    
    /** Initialize fields to non-null default values */
    public WsWorkFields() {}
    
    /** Initialize all fields to provided values */
    public WsWorkFields(String wsPgmName, String wsDateRoutine, String ws3ACpuid, String wsCpuFileStatus, String wsGoodStatus, WsCpuDate wsCpuDate, String wsProcDate, String wsRegComplDate, WsTime wsTime, String wsRegType, int wsDetailRecCnt, int wsTrailerRecCnt) {
        this.wsPgmName = wsPgmName;
        this.wsDateRoutine = wsDateRoutine;
        this.ws3ACpuid = ws3ACpuid;
        this.wsCpuFileStatus = wsCpuFileStatus;
        this.wsGoodStatus = wsGoodStatus;
        this.wsCpuDate = wsCpuDate;
        this.wsProcDate = wsProcDate;
        this.wsRegComplDate = wsRegComplDate;
        this.wsTime = wsTime;
        this.wsRegType = wsRegType;
        this.wsDetailRecCnt = wsDetailRecCnt;
        this.wsTrailerRecCnt = wsTrailerRecCnt;
    }
    
    @Override
    public WsWorkFields clone() throws CloneNotSupportedException {
        WsWorkFields cloned = (WsWorkFields) super.clone();
        cloned.wsCpuDate = this.wsCpuDate.clone();
        cloned.wsTime = this.wsTime.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code WsWorkFields} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected WsWorkFields(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code WsWorkFields} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected WsWorkFields(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsWorkFields} object
     * @see #setBytes(byte[], int)
     */
    public static WsWorkFields fromBytes(byte[] bytes, int offset) {
        return new WsWorkFields(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsWorkFields} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static WsWorkFields fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code WsWorkFields} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static WsWorkFields fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getWsPgmName() {
        return this.wsPgmName;
    }
    
    public void setWsPgmName(String wsPgmName) {
        this.wsPgmName = wsPgmName;
    }
    
    public String getWsDateRoutine() {
        return this.wsDateRoutine;
    }
    
    public void setWsDateRoutine(String wsDateRoutine) {
        this.wsDateRoutine = wsDateRoutine;
    }
    
    public String getWs3ACpuid() {
        return this.ws3ACpuid;
    }
    
    public void setWs3ACpuid(String ws3ACpuid) {
        this.ws3ACpuid = ws3ACpuid;
    }
    
    public String getWsCpuFileStatus() {
        return this.wsCpuFileStatus;
    }
    
    public void setWsCpuFileStatus(String wsCpuFileStatus) {
        this.wsCpuFileStatus = wsCpuFileStatus;
    }
    
    public String getWsGoodStatus() {
        return this.wsGoodStatus;
    }
    
    public void setWsGoodStatus(String wsGoodStatus) {
        this.wsGoodStatus = wsGoodStatus;
    }
    
    public WsCpuDate getWsCpuDate() {
        return this.wsCpuDate;
    }
    
    public void setWsCpuDate(WsCpuDate wsCpuDate) {
        this.wsCpuDate = wsCpuDate;
    }
    
    public String getWsProcDate() {
        return this.wsProcDate;
    }
    
    public void setWsProcDate(String wsProcDate) {
        this.wsProcDate = wsProcDate;
    }
    
    public String getWsRegComplDate() {
        return this.wsRegComplDate;
    }
    
    public void setWsRegComplDate(String wsRegComplDate) {
        this.wsRegComplDate = wsRegComplDate;
    }
    
    public WsTime getWsTime() {
        return this.wsTime;
    }
    
    public void setWsTime(WsTime wsTime) {
        this.wsTime = wsTime;
    }
    
    public String getWsRegType() {
        return this.wsRegType;
    }
    
    public void setWsRegType(String wsRegType) {
        this.wsRegType = wsRegType;
    }
    
    public boolean isValidRegTypes() {
        return
            (wsRegType.equals(VALID_REG_TYPES_VALUE_1))
            || (wsRegType.equals(VALID_REG_TYPES_VALUE_2))
            || (wsRegType.equals(VALID_REG_TYPES_VALUE_3));
    }
    
    public void setValidRegTypes() {
        wsRegType = VALID_REG_TYPES_VALUE_1;
    }
    
    public int getWsDetailRecCnt() {
        return this.wsDetailRecCnt;
    }
    
    public void setWsDetailRecCnt(int wsDetailRecCnt) {
        this.wsDetailRecCnt = wsDetailRecCnt;
    }
    
    public int getWsTrailerRecCnt() {
        return this.wsTrailerRecCnt;
    }
    
    public void setWsTrailerRecCnt(int wsTrailerRecCnt) {
        this.wsTrailerRecCnt = wsTrailerRecCnt;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        wsPgmName = "";
        wsDateRoutine = "";
        ws3ACpuid = "";
        wsCpuFileStatus = "";
        wsGoodStatus = "";
        wsCpuDate.reset();
        wsProcDate = "";
        wsRegComplDate = "";
        wsTime.reset();
        wsRegType = "";
        wsDetailRecCnt = 0;
        wsTrailerRecCnt = 0;
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ wsPgmName=\"");
        s.append(getWsPgmName());
        s.append("\"");
        s.append(", wsDateRoutine=\"");
        s.append(getWsDateRoutine());
        s.append("\"");
        s.append(", ws3ACpuid=\"");
        s.append(getWs3ACpuid());
        s.append("\"");
        s.append(", wsCpuFileStatus=\"");
        s.append(getWsCpuFileStatus());
        s.append("\"");
        s.append(", wsGoodStatus=\"");
        s.append(getWsGoodStatus());
        s.append("\"");
        s.append(", wsCpuDate=\"");
        s.append(getWsCpuDate());
        s.append("\"");
        s.append(", wsProcDate=\"");
        s.append(getWsProcDate());
        s.append("\"");
        s.append(", wsRegComplDate=\"");
        s.append(getWsRegComplDate());
        s.append("\"");
        s.append(", wsTime=\"");
        s.append(getWsTime());
        s.append("\"");
        s.append(", wsRegType=\"");
        s.append(getWsRegType());
        s.append("\"");
        s.append(", wsDetailRecCnt=\"");
        s.append(getWsDetailRecCnt());
        s.append("\"");
        s.append(", wsTrailerRecCnt=\"");
        s.append(getWsTrailerRecCnt());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(WsWorkFields that) {
        return this.wsPgmName.equals(that.wsPgmName) &&
            this.wsDateRoutine.equals(that.wsDateRoutine) &&
            this.ws3ACpuid.equals(that.ws3ACpuid) &&
            this.wsCpuFileStatus.equals(that.wsCpuFileStatus) &&
            this.wsGoodStatus.equals(that.wsGoodStatus) &&
            this.wsCpuDate.equals(that.wsCpuDate) &&
            this.wsProcDate.equals(that.wsProcDate) &&
            this.wsRegComplDate.equals(that.wsRegComplDate) &&
            this.wsTime.equals(that.wsTime) &&
            this.wsRegType.equals(that.wsRegType) &&
            this.wsDetailRecCnt == that.wsDetailRecCnt &&
            this.wsTrailerRecCnt == that.wsTrailerRecCnt;
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof WsWorkFields other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof WsWorkFields;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Objects.hashCode(wsPgmName);
        result = 31 * result + Objects.hashCode(wsDateRoutine);
        result = 31 * result + Objects.hashCode(ws3ACpuid);
        result = 31 * result + Objects.hashCode(wsCpuFileStatus);
        result = 31 * result + Objects.hashCode(wsGoodStatus);
        result = 31 * result + Objects.hashCode(wsCpuDate);
        result = 31 * result + Objects.hashCode(wsProcDate);
        result = 31 * result + Objects.hashCode(wsRegComplDate);
        result = 31 * result + Objects.hashCode(wsTime);
        result = 31 * result + Objects.hashCode(wsRegType);
        result = 31 * result + Integer.hashCode(wsDetailRecCnt);
        result = 31 * result + Integer.hashCode(wsTrailerRecCnt);
        return result;
    }
    
    @Override
    public int compareTo(WsWorkFields that) {
        int c = 0;
        c = this.wsPgmName.compareTo(that.wsPgmName);
        if ( c != 0 ) return c;
        c = this.wsDateRoutine.compareTo(that.wsDateRoutine);
        if ( c != 0 ) return c;
        c = this.ws3ACpuid.compareTo(that.ws3ACpuid);
        if ( c != 0 ) return c;
        c = this.wsCpuFileStatus.compareTo(that.wsCpuFileStatus);
        if ( c != 0 ) return c;
        c = this.wsGoodStatus.compareTo(that.wsGoodStatus);
        if ( c != 0 ) return c;
        c = this.wsCpuDate.compareTo(that.wsCpuDate);
        if ( c != 0 ) return c;
        c = this.wsProcDate.compareTo(that.wsProcDate);
        if ( c != 0 ) return c;
        c = this.wsRegComplDate.compareTo(that.wsRegComplDate);
        if ( c != 0 ) return c;
        c = this.wsTime.compareTo(that.wsTime);
        if ( c != 0 ) return c;
        c = this.wsRegType.compareTo(that.wsRegType);
        if ( c != 0 ) return c;
        c = Integer.compare(this.wsDetailRecCnt, that.wsDetailRecCnt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.wsTrailerRecCnt, that.wsTrailerRecCnt);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final StringField WS_PGM_NAME = factory.getStringField(8);
    private static final StringField WS_DATE_ROUTINE = factory.getStringField(8);
    private static final StringField WS_3_A_CPUID = factory.getStringField(4);
    private static final StringField WS_CPU_FILE_STATUS = factory.getStringField(2);
    private static final StringField WS_GOOD_STATUS = factory.getStringField(2);
    private static final ByteArrayField WS_CPU_DATE = factory.getByteArrayField(WsCpuDate.SIZE);
    private static final StringField WS_PROC_DATE = factory.getStringField(10);
    private static final StringField WS_REG_COMPL_DATE = factory.getStringField(10);
    private static final ByteArrayField WS_TIME = factory.getByteArrayField(WsTime.SIZE);
    private static final StringField WS_REG_TYPE = factory.getStringField(3);
    private static final ExternalDecimalAsIntField WS_DETAIL_REC_CNT = factory.getExternalDecimalAsIntField(6, true);
    private static final ExternalDecimalAsIntField WS_TRAILER_REC_CNT = factory.getExternalDecimalAsIntField(6, true);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsWorkFields} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code WS-WORK-FIELDS} record
     * @see "WS-WORK-FIELDS record at RXBPASVC.cbl:111"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        WS_PGM_NAME.putString(wsPgmName, bytes, offset);
        WS_DATE_ROUTINE.putString(wsDateRoutine, bytes, offset);
        WS_3_A_CPUID.putString(ws3ACpuid, bytes, offset);
        WS_CPU_FILE_STATUS.putString(wsCpuFileStatus, bytes, offset);
        WS_GOOD_STATUS.putString(wsGoodStatus, bytes, offset);
        wsCpuDate.getBytes(bytes, WS_CPU_DATE.getOffset() + offset);
        WS_PROC_DATE.putString(wsProcDate, bytes, offset);
        WS_REG_COMPL_DATE.putString(wsRegComplDate, bytes, offset);
        wsTime.getBytes(bytes, WS_TIME.getOffset() + offset);
        WS_REG_TYPE.putString(wsRegType, bytes, offset);
        WS_DETAIL_REC_CNT.putInt(wsDetailRecCnt, bytes, offset);
        WS_TRAILER_REC_CNT.putInt(wsTrailerRecCnt, bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsWorkFields} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsWorkFields} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code WsWorkFields} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code WS-WORK-FIELDS} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "WS-WORK-FIELDS record at RXBPASVC.cbl:111"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        wsPgmName = WS_PGM_NAME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        wsDateRoutine = WS_DATE_ROUTINE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        ws3ACpuid = WS_3_A_CPUID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        wsCpuFileStatus = WS_CPU_FILE_STATUS.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        wsGoodStatus = WS_GOOD_STATUS.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        wsCpuDate.setBytes(bytes, WS_CPU_DATE.getOffset() + offset);
        wsProcDate = WS_PROC_DATE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        wsRegComplDate = WS_REG_COMPL_DATE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        wsTime.setBytes(bytes, WS_TIME.getOffset() + offset);
        wsRegType = WS_REG_TYPE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        wsDetailRecCnt = WS_DETAIL_REC_CNT.getInt(bytes, offset);
        wsTrailerRecCnt = WS_TRAILER_REC_CNT.getInt(bytes, offset);
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
