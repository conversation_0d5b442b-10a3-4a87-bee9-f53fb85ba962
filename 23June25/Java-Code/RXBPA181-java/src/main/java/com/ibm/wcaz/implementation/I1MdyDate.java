package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class I1MdyDate extends DateParmArea {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String i1MdyMm = "";
    private String i1MdyDd = "";
    private String i1MdyYy = "";
    
    /** Initialize fields to non-null default values */
    public I1MdyDate() {}
    
    /** Initialize all fields to provided values */
    public I1MdyDate(int reqDay, int manipulateReqType, int numberOfDays, int numberOfMonths, int inputDateType, int oIsoCc, int oIsoYy, int oIsoMm, int oIsoDd, int oUsaMm, int oUsaDd, int oUsaCc, int oUsaYy, int oEurDd, int oEurMm, int oEurCc, int oEurYy, int oJisCc, int oJisYy, int oJisMm, int oJisDd, int oMdySlshMm, int oMdySlshDd, int oMdySlshYy, int oMdyMm, int oMdyDd, int oMdyYy, int oMdcySlshMm, int oMdcySlshDd, int oMdcySlshCc, int oMdcySlshYy, int oMdcyMm, int oMdcyDd, int oMdcyCc, int oMdcyYy, int oYmdYy, int oYmdMm, int oYmdDd, int oCymdCc, int oCymdYy, int oCymdMm, int oCymdDd, int oJulYy, int oJulDdd, String oExpMm, int oExpDd, int oExpCc, int oExpYy, int differenceInDays, int oDayNumber, String reqCountry, OutputNextMoYr outputNextMoYr, OutputLastMoYr outputLastMoYr, String i1MdyMm, String i1MdyDd, String i1MdyYy) {
        super(reqDay, manipulateReqType, numberOfDays, numberOfMonths, inputDateType, oIsoCc, oIsoYy, oIsoMm, oIsoDd, oUsaMm, oUsaDd, oUsaCc, oUsaYy, oEurDd, oEurMm, oEurCc, oEurYy, oJisCc, oJisYy, oJisMm, oJisDd, oMdySlshMm, oMdySlshDd, oMdySlshYy, oMdyMm, oMdyDd, oMdyYy, oMdcySlshMm, oMdcySlshDd, oMdcySlshCc, oMdcySlshYy, oMdcyMm, oMdcyDd, oMdcyCc, oMdcyYy, oYmdYy, oYmdMm, oYmdDd, oCymdCc, oCymdYy, oCymdMm, oCymdDd, oJulYy, oJulDdd, oExpMm, oExpDd, oExpCc, oExpYy, differenceInDays, oDayNumber, reqCountry, outputNextMoYr, outputLastMoYr);
        this.i1MdyMm = i1MdyMm;
        this.i1MdyDd = i1MdyDd;
        this.i1MdyYy = i1MdyYy;
    }
    
    @Override
    public I1MdyDate clone() throws CloneNotSupportedException {
        I1MdyDate cloned = (I1MdyDate) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code I1MdyDate} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected I1MdyDate(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code I1MdyDate} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected I1MdyDate(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I1MdyDate} object
     * @see #setBytes(byte[], int)
     */
    public static I1MdyDate fromBytes(byte[] bytes, int offset) {
        return new I1MdyDate(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I1MdyDate} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static I1MdyDate fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code I1MdyDate} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static I1MdyDate fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getI1MdyMm() {
        return this.i1MdyMm;
    }
    
    public void setI1MdyMm(String i1MdyMm) {
        this.i1MdyMm = i1MdyMm;
    }
    
    public String getI1MdyDd() {
        return this.i1MdyDd;
    }
    
    public void setI1MdyDd(String i1MdyDd) {
        this.i1MdyDd = i1MdyDd;
    }
    
    public String getI1MdyYy() {
        return this.i1MdyYy;
    }
    
    public void setI1MdyYy(String i1MdyYy) {
        this.i1MdyYy = i1MdyYy;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        i1MdyMm = "";
        i1MdyDd = "";
        i1MdyYy = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ i1MdyMm=\"");
        s.append(getI1MdyMm());
        s.append("\"");
        s.append(", i1MdyDd=\"");
        s.append(getI1MdyDd());
        s.append("\"");
        s.append(", i1MdyYy=\"");
        s.append(getI1MdyYy());
        s.append("\"");
        s.append(", filler39=\"");
        s.append(new String(filler39, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(I1MdyDate that) {
        return super.equals(that) &&
            this.i1MdyMm.equals(that.i1MdyMm) &&
            this.i1MdyDd.equals(that.i1MdyDd) &&
            this.i1MdyYy.equals(that.i1MdyYy) &&
            Arrays.equals(this.filler39, that.filler39);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof I1MdyDate other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof I1MdyDate;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(i1MdyMm);
        result = 31 * result + Objects.hashCode(i1MdyDd);
        result = 31 * result + Objects.hashCode(i1MdyYy);
        result = 31 * result + Arrays.hashCode(filler39);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(I1MdyDate that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.i1MdyMm.compareTo(that.i1MdyMm);
        if ( c != 0 ) return c;
        c = this.i1MdyDd.compareTo(that.i1MdyDd);
        if ( c != 0 ) return c;
        c = this.i1MdyYy.compareTo(that.i1MdyYy);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler39, that.filler39);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(DateParmArea that) {
        if (that instanceof I1MdyDate other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(DateParmArea.SIZE);
    }
    
    private static final StringField I_1_MDY_MM = factory.getStringField(2);
    private static final StringField I_1_MDY_DD = factory.getStringField(2);
    private static final StringField I_1_MDY_YY = factory.getStringField(2);
    private static final ByteArrayField FILLER_39 = factory.getByteArrayField(6);
    private byte[] filler39 = new byte[6];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "I1-MDY-DATE record at MXWW01.CPY:135"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        I_1_MDY_MM.putString(i1MdyMm, bytes, offset);
        I_1_MDY_DD.putString(i1MdyDd, bytes, offset);
        I_1_MDY_YY.putString(i1MdyYy, bytes, offset);
        FILLER_39.putByteArray(filler39, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#setBytes(byte[], int)} to set parent-class state.
     * @see "I1-MDY-DATE record at MXWW01.CPY:135"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        i1MdyMm = I_1_MDY_MM.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        i1MdyDd = I_1_MDY_DD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        i1MdyYy = I_1_MDY_YY.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_39.getByteArray(bytes, offset);
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
