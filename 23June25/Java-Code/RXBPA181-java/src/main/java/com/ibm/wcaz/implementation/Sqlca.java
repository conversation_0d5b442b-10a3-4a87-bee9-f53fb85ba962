package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.BinaryAsIntField;
import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class Sqlca implements Cloneable, Comparable<Sqlca> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String sqlcaid = "";
    private int sqlcabc;
    private int sqlcode;
    private int sqlerrml;
    private String sqlerrmc = "";
    private String sqlerrp = "";
    private ArrayList<Integer> sqlerrd = Stream.generate(() -> 0).limit(6).collect(Collectors.toCollection(ArrayList::new));
    private char sqlwarn0 = ' ';
    private char sqlwarn1 = ' ';
    private char sqlwarn2 = ' ';
    private char sqlwarn3 = ' ';
    private char sqlwarn4 = ' ';
    private char sqlwarn5 = ' ';
    private char sqlwarn6 = ' ';
    private char sqlwarn7 = ' ';
    private char sqlwarn8 = ' ';
    private char sqlwarn9 = ' ';
    private char sqlwarna = ' ';
    private String sqlstate = "";
    
    /** Initialize fields to non-null default values */
    public Sqlca() {}
    
    /** Initialize all fields to provided values */
    public Sqlca(String sqlcaid, int sqlcabc, int sqlcode, int sqlerrml, String sqlerrmc, String sqlerrp, ArrayList<Integer> sqlerrd, char sqlwarn0, char sqlwarn1, char sqlwarn2, char sqlwarn3, char sqlwarn4, char sqlwarn5, char sqlwarn6, char sqlwarn7, char sqlwarn8, char sqlwarn9, char sqlwarna, String sqlstate) {
        this.sqlcaid = sqlcaid;
        this.sqlcabc = sqlcabc;
        this.sqlcode = sqlcode;
        this.sqlerrml = sqlerrml;
        this.sqlerrmc = sqlerrmc;
        this.sqlerrp = sqlerrp;
        this.sqlerrd.clear();
        for (int i = 0; i < sqlerrd.size(); i++) {
            this.sqlerrd.add(sqlerrd.get(i));
        }
        for (int i = sqlerrd.size(); i < 6; i++) {
            this.sqlerrd.add(null);
        }
        this.sqlwarn0 = sqlwarn0;
        this.sqlwarn1 = sqlwarn1;
        this.sqlwarn2 = sqlwarn2;
        this.sqlwarn3 = sqlwarn3;
        this.sqlwarn4 = sqlwarn4;
        this.sqlwarn5 = sqlwarn5;
        this.sqlwarn6 = sqlwarn6;
        this.sqlwarn7 = sqlwarn7;
        this.sqlwarn8 = sqlwarn8;
        this.sqlwarn9 = sqlwarn9;
        this.sqlwarna = sqlwarna;
        this.sqlstate = sqlstate;
    }
    
    @Override
    public Sqlca clone() throws CloneNotSupportedException {
        Sqlca cloned = (Sqlca) super.clone();
        cloned.sqlerrd = new ArrayList<Integer>(this.sqlerrd);
        return cloned;
    }
    
    /**
     * Initialize {@code Sqlca} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected Sqlca(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code Sqlca} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected Sqlca(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code Sqlca} object
     * @see #setBytes(byte[], int)
     */
    public static Sqlca fromBytes(byte[] bytes, int offset) {
        return new Sqlca(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code Sqlca} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static Sqlca fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code Sqlca} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static Sqlca fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getSqlcaid() {
        return this.sqlcaid;
    }
    
    public void setSqlcaid(String sqlcaid) {
        this.sqlcaid = sqlcaid;
    }
    
    public int getSqlcabc() {
        return this.sqlcabc;
    }
    
    public void setSqlcabc(int sqlcabc) {
        this.sqlcabc = sqlcabc;
    }
    
    public int getSqlcode() {
        return this.sqlcode;
    }
    
    public void setSqlcode(int sqlcode) {
        this.sqlcode = sqlcode;
    }
    
    public int getSqlerrml() {
        return this.sqlerrml;
    }
    
    public void setSqlerrml(int sqlerrml) {
        this.sqlerrml = sqlerrml;
    }
    
    public String getSqlerrmc() {
        return this.sqlerrmc;
    }
    
    public void setSqlerrmc(String sqlerrmc) {
        this.sqlerrmc = sqlerrmc;
    }
    
    public String getSqlerrp() {
        return this.sqlerrp;
    }
    
    public void setSqlerrp(String sqlerrp) {
        this.sqlerrp = sqlerrp;
    }
    
    public ArrayList<Integer> getSqlerrd() {
        return this.sqlerrd;
    }
    public int getSqlerrd(int i) {
        return this.sqlerrd.get(i);
    }
    
    public void setSqlerrd(ArrayList<Integer> sqlerrd) {
        this.sqlerrd = sqlerrd;
    }
    public void setSqlerrd(int i, int sqlerrd) {
        this.sqlerrd.set(i, sqlerrd);
    }
    
    public char getSqlwarn0() {
        return this.sqlwarn0;
    }
    
    public void setSqlwarn0(char sqlwarn0) {
        this.sqlwarn0 = sqlwarn0;
    }
    
    public char getSqlwarn1() {
        return this.sqlwarn1;
    }
    
    public void setSqlwarn1(char sqlwarn1) {
        this.sqlwarn1 = sqlwarn1;
    }
    
    public char getSqlwarn2() {
        return this.sqlwarn2;
    }
    
    public void setSqlwarn2(char sqlwarn2) {
        this.sqlwarn2 = sqlwarn2;
    }
    
    public char getSqlwarn3() {
        return this.sqlwarn3;
    }
    
    public void setSqlwarn3(char sqlwarn3) {
        this.sqlwarn3 = sqlwarn3;
    }
    
    public char getSqlwarn4() {
        return this.sqlwarn4;
    }
    
    public void setSqlwarn4(char sqlwarn4) {
        this.sqlwarn4 = sqlwarn4;
    }
    
    public char getSqlwarn5() {
        return this.sqlwarn5;
    }
    
    public void setSqlwarn5(char sqlwarn5) {
        this.sqlwarn5 = sqlwarn5;
    }
    
    public char getSqlwarn6() {
        return this.sqlwarn6;
    }
    
    public void setSqlwarn6(char sqlwarn6) {
        this.sqlwarn6 = sqlwarn6;
    }
    
    public char getSqlwarn7() {
        return this.sqlwarn7;
    }
    
    public void setSqlwarn7(char sqlwarn7) {
        this.sqlwarn7 = sqlwarn7;
    }
    
    public char getSqlwarn8() {
        return this.sqlwarn8;
    }
    
    public void setSqlwarn8(char sqlwarn8) {
        this.sqlwarn8 = sqlwarn8;
    }
    
    public char getSqlwarn9() {
        return this.sqlwarn9;
    }
    
    public void setSqlwarn9(char sqlwarn9) {
        this.sqlwarn9 = sqlwarn9;
    }
    
    public char getSqlwarna() {
        return this.sqlwarna;
    }
    
    public void setSqlwarna(char sqlwarna) {
        this.sqlwarna = sqlwarna;
    }
    
    public String getSqlstate() {
        return this.sqlstate;
    }
    
    public void setSqlstate(String sqlstate) {
        this.sqlstate = sqlstate;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        sqlcaid = "";
        sqlcabc = 0;
        sqlcode = 0;
        sqlerrml = 0;
        sqlerrmc = "";
        sqlerrp = "";
        sqlerrd = Stream.generate(() -> 0).limit(6).collect(Collectors.toCollection(ArrayList::new));
        sqlwarn0 = ' ';
        sqlwarn1 = ' ';
        sqlwarn2 = ' ';
        sqlwarn3 = ' ';
        sqlwarn4 = ' ';
        sqlwarn5 = ' ';
        sqlwarn6 = ' ';
        sqlwarn7 = ' ';
        sqlwarn8 = ' ';
        sqlwarn9 = ' ';
        sqlwarna = ' ';
        sqlstate = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ sqlcaid=\"");
        s.append(getSqlcaid());
        s.append("\"");
        s.append(", sqlcabc=\"");
        s.append(getSqlcabc());
        s.append("\"");
        s.append(", sqlcode=\"");
        s.append(getSqlcode());
        s.append("\"");
        s.append(", sqlerrml=\"");
        s.append(getSqlerrml());
        s.append("\"");
        s.append(", sqlerrmc=\"");
        s.append(getSqlerrmc());
        s.append("\"");
        s.append(", sqlerrp=\"");
        s.append(getSqlerrp());
        s.append("\"");
        s.append(", sqlerrd=[\"");
        for (int i=0; i<this.sqlerrd.size(); i++) {
          s.append(this.sqlerrd.get(i));
          if (i < (this.sqlerrd.size()-1)) {
             s.append(", ");
          }
          else {
             s.append("\"]");
          }
        };
        s.append(", sqlwarn0=\"");
        s.append(getSqlwarn0());
        s.append("\"");
        s.append(", sqlwarn1=\"");
        s.append(getSqlwarn1());
        s.append("\"");
        s.append(", sqlwarn2=\"");
        s.append(getSqlwarn2());
        s.append("\"");
        s.append(", sqlwarn3=\"");
        s.append(getSqlwarn3());
        s.append("\"");
        s.append(", sqlwarn4=\"");
        s.append(getSqlwarn4());
        s.append("\"");
        s.append(", sqlwarn5=\"");
        s.append(getSqlwarn5());
        s.append("\"");
        s.append(", sqlwarn6=\"");
        s.append(getSqlwarn6());
        s.append("\"");
        s.append(", sqlwarn7=\"");
        s.append(getSqlwarn7());
        s.append("\"");
        s.append(", sqlwarn8=\"");
        s.append(getSqlwarn8());
        s.append("\"");
        s.append(", sqlwarn9=\"");
        s.append(getSqlwarn9());
        s.append("\"");
        s.append(", sqlwarna=\"");
        s.append(getSqlwarna());
        s.append("\"");
        s.append(", sqlstate=\"");
        s.append(getSqlstate());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(Sqlca that) {
        return this.sqlcaid.equals(that.sqlcaid) &&
            this.sqlcabc == that.sqlcabc &&
            this.sqlcode == that.sqlcode &&
            this.sqlerrml == that.sqlerrml &&
            this.sqlerrmc.equals(that.sqlerrmc) &&
            this.sqlerrp.equals(that.sqlerrp) &&
            this.sqlerrd.equals(that.sqlerrd) &&
            this.sqlwarn0 == that.sqlwarn0 &&
            this.sqlwarn1 == that.sqlwarn1 &&
            this.sqlwarn2 == that.sqlwarn2 &&
            this.sqlwarn3 == that.sqlwarn3 &&
            this.sqlwarn4 == that.sqlwarn4 &&
            this.sqlwarn5 == that.sqlwarn5 &&
            this.sqlwarn6 == that.sqlwarn6 &&
            this.sqlwarn7 == that.sqlwarn7 &&
            this.sqlwarn8 == that.sqlwarn8 &&
            this.sqlwarn9 == that.sqlwarn9 &&
            this.sqlwarna == that.sqlwarna &&
            this.sqlstate.equals(that.sqlstate);
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof Sqlca other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof Sqlca;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Objects.hashCode(sqlcaid);
        result = 31 * result + Integer.hashCode(sqlcabc);
        result = 31 * result + Integer.hashCode(sqlcode);
        result = 31 * result + Integer.hashCode(sqlerrml);
        result = 31 * result + Objects.hashCode(sqlerrmc);
        result = 31 * result + Objects.hashCode(sqlerrp);
        result = 31 * result + Objects.hashCode(sqlerrd);
        result = 31 * result + Character.hashCode(sqlwarn0);
        result = 31 * result + Character.hashCode(sqlwarn1);
        result = 31 * result + Character.hashCode(sqlwarn2);
        result = 31 * result + Character.hashCode(sqlwarn3);
        result = 31 * result + Character.hashCode(sqlwarn4);
        result = 31 * result + Character.hashCode(sqlwarn5);
        result = 31 * result + Character.hashCode(sqlwarn6);
        result = 31 * result + Character.hashCode(sqlwarn7);
        result = 31 * result + Character.hashCode(sqlwarn8);
        result = 31 * result + Character.hashCode(sqlwarn9);
        result = 31 * result + Character.hashCode(sqlwarna);
        result = 31 * result + Objects.hashCode(sqlstate);
        return result;
    }
    
    @Override
    public int compareTo(Sqlca that) {
        int c = 0;
        c = this.sqlcaid.compareTo(that.sqlcaid);
        if ( c != 0 ) return c;
        c = Integer.compare(this.sqlcabc, that.sqlcabc);
        if ( c != 0 ) return c;
        c = Integer.compare(this.sqlcode, that.sqlcode);
        if ( c != 0 ) return c;
        c = Integer.compare(this.sqlerrml, that.sqlerrml);
        if ( c != 0 ) return c;
        c = this.sqlerrmc.compareTo(that.sqlerrmc);
        if ( c != 0 ) return c;
        c = this.sqlerrp.compareTo(that.sqlerrp);
        if ( c != 0 ) return c;
        for (int i = 0; i < this.sqlerrd.size() && i < that.sqlerrd.size(); i++) {
            c = Integer.compare(this.sqlerrd.get(i), that.sqlerrd.get(i));
            if ( c != 0 ) return c;
        }
        c = Integer.compare(this.sqlerrd.size(), that.sqlerrd.size());
        if ( c != 0 ) return c;
        c = Character.compare(this.sqlwarn0, that.sqlwarn0);
        if ( c != 0 ) return c;
        c = Character.compare(this.sqlwarn1, that.sqlwarn1);
        if ( c != 0 ) return c;
        c = Character.compare(this.sqlwarn2, that.sqlwarn2);
        if ( c != 0 ) return c;
        c = Character.compare(this.sqlwarn3, that.sqlwarn3);
        if ( c != 0 ) return c;
        c = Character.compare(this.sqlwarn4, that.sqlwarn4);
        if ( c != 0 ) return c;
        c = Character.compare(this.sqlwarn5, that.sqlwarn5);
        if ( c != 0 ) return c;
        c = Character.compare(this.sqlwarn6, that.sqlwarn6);
        if ( c != 0 ) return c;
        c = Character.compare(this.sqlwarn7, that.sqlwarn7);
        if ( c != 0 ) return c;
        c = Character.compare(this.sqlwarn8, that.sqlwarn8);
        if ( c != 0 ) return c;
        c = Character.compare(this.sqlwarn9, that.sqlwarn9);
        if ( c != 0 ) return c;
        c = Character.compare(this.sqlwarna, that.sqlwarna);
        if ( c != 0 ) return c;
        c = this.sqlstate.compareTo(that.sqlstate);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final StringField SQLCAID = factory.getStringField(8);
    private static final BinaryAsIntField SQLCABC = factory.getBinaryAsIntField(9, true);
    private static final BinaryAsIntField SQLCODE = factory.getBinaryAsIntField(9, true);
    private static final BinaryAsIntField SQLERRML = factory.getBinaryAsIntField(4, true);
    private static final StringField SQLERRMC = factory.getStringField(70);
    private static final StringField SQLERRP = factory.getStringField(8);
    static { factory.pushOffset(); }
    private static final BinaryAsIntField SQLERRD = factory.getBinaryAsIntField(9, true);
    static { factory.popOffset(); }
    private static final StringField SQLWARN_0 = factory.getStringField(1);
    private static final StringField SQLWARN_1 = factory.getStringField(1);
    private static final StringField SQLWARN_2 = factory.getStringField(1);
    private static final StringField SQLWARN_3 = factory.getStringField(1);
    private static final StringField SQLWARN_4 = factory.getStringField(1);
    private static final StringField SQLWARN_5 = factory.getStringField(1);
    private static final StringField SQLWARN_6 = factory.getStringField(1);
    private static final StringField SQLWARN_7 = factory.getStringField(1);
    private static final StringField SQLWARN_8 = factory.getStringField(1);
    private static final StringField SQLWARN_9 = factory.getStringField(1);
    private static final StringField SQLWARNA = factory.getStringField(1);
    private static final StringField SQLSTATE = factory.getStringField(5);
    // include extra size from fields skipped in offset
    public static final int SIZE = factory.getOffset() +
        6 * SQLERRD.getByteLength();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code Sqlca} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code SQLCA} record
     * @see "SQLCA record at SQLCA.CPY:2"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        SQLCAID.putString(sqlcaid, bytes, offset);
        SQLCABC.putInt(sqlcabc, bytes, offset);
        SQLCODE.putInt(sqlcode, bytes, offset);
        SQLERRML.putInt(sqlerrml, bytes, offset);
        SQLERRMC.putString(sqlerrmc, bytes, offset);
        SQLERRP.putString(sqlerrp, bytes, offset);
        for (int i = 0; i < 6; i++) {
            SQLERRD.putInt(sqlerrd.get(i), bytes, offset);
            offset += SQLERRD.getByteLength();
        }
        SQLWARN_0.putString(Character.toString(sqlwarn0), bytes, offset);
        SQLWARN_1.putString(Character.toString(sqlwarn1), bytes, offset);
        SQLWARN_2.putString(Character.toString(sqlwarn2), bytes, offset);
        SQLWARN_3.putString(Character.toString(sqlwarn3), bytes, offset);
        SQLWARN_4.putString(Character.toString(sqlwarn4), bytes, offset);
        SQLWARN_5.putString(Character.toString(sqlwarn5), bytes, offset);
        SQLWARN_6.putString(Character.toString(sqlwarn6), bytes, offset);
        SQLWARN_7.putString(Character.toString(sqlwarn7), bytes, offset);
        SQLWARN_8.putString(Character.toString(sqlwarn8), bytes, offset);
        SQLWARN_9.putString(Character.toString(sqlwarn9), bytes, offset);
        SQLWARNA.putString(Character.toString(sqlwarna), bytes, offset);
        SQLSTATE.putString(sqlstate, bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code Sqlca} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code Sqlca} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code Sqlca} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code SQLCA} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "SQLCA record at SQLCA.CPY:2"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        sqlcaid = SQLCAID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        sqlcabc = SQLCABC.getInt(bytes, offset);
        sqlcode = SQLCODE.getInt(bytes, offset);
        sqlerrml = SQLERRML.getInt(bytes, offset);
        sqlerrmc = SQLERRMC.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        sqlerrp = SQLERRP.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        sqlerrd.clear();
        for (int i = 0; i < 6; i++) {
            sqlerrd.add(SQLERRD.getInt(bytes, offset));
            offset += SQLERRD.getByteLength();
        }
        sqlwarn0 = SQLWARN_0.getString(bytes, offset).charAt(0);
        sqlwarn1 = SQLWARN_1.getString(bytes, offset).charAt(0);
        sqlwarn2 = SQLWARN_2.getString(bytes, offset).charAt(0);
        sqlwarn3 = SQLWARN_3.getString(bytes, offset).charAt(0);
        sqlwarn4 = SQLWARN_4.getString(bytes, offset).charAt(0);
        sqlwarn5 = SQLWARN_5.getString(bytes, offset).charAt(0);
        sqlwarn6 = SQLWARN_6.getString(bytes, offset).charAt(0);
        sqlwarn7 = SQLWARN_7.getString(bytes, offset).charAt(0);
        sqlwarn8 = SQLWARN_8.getString(bytes, offset).charAt(0);
        sqlwarn9 = SQLWARN_9.getString(bytes, offset).charAt(0);
        sqlwarna = SQLWARNA.getString(bytes, offset).charAt(0);
        sqlstate = SQLSTATE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
