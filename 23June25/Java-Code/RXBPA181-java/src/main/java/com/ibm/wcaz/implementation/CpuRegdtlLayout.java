package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class CpuRegdtlLayout implements Cloneable, Comparable<CpuRegdtlLayout> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    /** Initialize fields to non-null default values */
    public CpuRegdtlLayout() {}
    
    @Override
    public CpuRegdtlLayout clone() throws CloneNotSupportedException {
        CpuRegdtlLayout cloned = (CpuRegdtlLayout) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code CpuRegdtlLayout} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected CpuRegdtlLayout(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code CpuRegdtlLayout} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected CpuRegdtlLayout(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code CpuRegdtlLayout} object
     * @see #setBytes(byte[], int)
     */
    public static CpuRegdtlLayout fromBytes(byte[] bytes, int offset) {
        return new CpuRegdtlLayout(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code CpuRegdtlLayout} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static CpuRegdtlLayout fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code CpuRegdtlLayout} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static CpuRegdtlLayout fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{}");
        return s.toString();
    }
    
    private boolean equals(CpuRegdtlLayout that) {
        return true;
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof CpuRegdtlLayout other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof CpuRegdtlLayout;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        return result;
    }
    
    @Override
    public int compareTo(CpuRegdtlLayout that) {
        int c = 0;
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code CpuRegdtlLayout} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code CPU-REGDTL-LAYOUT} record
     * @see "CPU-REGDTL-LAYOUT record at RXBPASVC.cbl:35"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code CpuRegdtlLayout} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code CpuRegdtlLayout} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code CpuRegdtlLayout} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code CPU-REGDTL-LAYOUT} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "CPU-REGDTL-LAYOUT record at RXBPASVC.cbl:35"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
