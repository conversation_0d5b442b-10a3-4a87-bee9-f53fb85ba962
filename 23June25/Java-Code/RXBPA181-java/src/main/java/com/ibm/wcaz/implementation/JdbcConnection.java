package com.ibm.wcaz.implementation;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

/**
 * Utility class for managing JDBC database connections
 */
public class JdbcConnection {
    
    public static Connection connection;
    private static String url = "jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1";
    private static String username = "sa";
    private static String password = "";
    
    static {
        try {
            // Load H2 driver
            Class.forName("org.h2.Driver");
            // Initialize connection
            connection = DriverManager.getConnection(url, username, password);
            
            // Create sample tables for testing
            createSampleTables();
            
        } catch (ClassNotFoundException | SQLException e) {
            System.err.println("Failed to initialize database connection: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Get the current database connection
     * @return the database connection
     */
    public static Connection getConnection() {
        try {
            if (connection == null || connection.isClosed()) {
                connection = DriverManager.getConnection(url, username, password);
            }
        } catch (SQLException e) {
            System.err.println("Failed to get database connection: " + e.getMessage());
            e.printStackTrace();
        }
        return connection;
    }
    
    /**
     * Close the database connection
     */
    public static void closeConnection() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
        } catch (SQLException e) {
            System.err.println("Failed to close database connection: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Get SQL error code from the connection
     * @return SQL error code
     */
    public static int getSqlCode() {
        // This is a placeholder implementation
        // In a real DB2 environment, this would return the actual SQLCODE
        return 0;
    }
    
    /**
     * Create sample tables for testing
     */
    private static void createSampleTables() {
        try {
            // Create VWMCUCP table
            connection.createStatement().execute(
                "CREATE TABLE IF NOT EXISTS VWMCUCP (" +
                "VEND_DLR_NO VARCHAR(10), " +
                "CDF_CUST_ID VARCHAR(10), " +
                "CPU_CODE VARCHAR(10), " +
                "CUST_NO VARCHAR(10)" +
                ")"
            );
            
            // Create VWMCTUPD table
            connection.createStatement().execute(
                "CREATE TABLE IF NOT EXISTS VWMCTUPD (" +
                "PROC_DATE VARCHAR(10), " +
                "SUBSYSTEM_ID_IND VARCHAR(1), " +
                "SUBFUNCTION_CODE VARCHAR(10)" +
                ")"
            );
            
            // Create VWMCPCD table
            connection.createStatement().execute(
                "CREATE TABLE IF NOT EXISTS VWMCPCD (" +
                "CPU_ID VARCHAR(10), " +
                "CPU_CODE VARCHAR(10), " +
                "DIST_NO INTEGER, " +
                "MFG_NO INTEGER, " +
                "CPU_STAT_CODE VARCHAR(1)" +
                ")"
            );
            
            // Create VWMBHRG table
            connection.createStatement().execute(
                "CREATE TABLE IF NOT EXISTS VWMBHRG (" +
                "ID INTEGER PRIMARY KEY AUTO_INCREMENT, " +
                "DATA VARCHAR(100)" +
                ")"
            );
            
            // Insert sample data
            connection.createStatement().execute(
                "INSERT INTO VWMCTUPD (PROC_DATE, SUBSYSTEM_ID_IND, SUBFUNCTION_CODE) " +
                "VALUES ('2023-06-25', ' ', '        ')"
            );
            
        } catch (SQLException e) {
            System.err.println("Failed to create sample tables: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
