package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class I2IsoDate extends DateParmArea {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String i2IsoCc = "";
    private String i2IsoYy = "";
    private String i2IsoMm = "";
    private String i2IsoDd = "";
    
    /** Initialize fields to non-null default values */
    public I2IsoDate() {}
    
    /** Initialize all fields to provided values */
    public I2IsoDate(int reqDay, int manipulateReqType, int numberOfDays, int numberOfMonths, int inputDateType, int oIsoCc, int oIsoYy, int oIsoMm, int oIsoDd, int oUsaMm, int oUsaDd, int oUsaCc, int oUsaYy, int oEurDd, int oEurMm, int oEurCc, int oEurYy, int oJisCc, int oJisYy, int oJisMm, int oJisDd, int oMdySlshMm, int oMdySlshDd, int oMdySlshYy, int oMdyMm, int oMdyDd, int oMdyYy, int oMdcySlshMm, int oMdcySlshDd, int oMdcySlshCc, int oMdcySlshYy, int oMdcyMm, int oMdcyDd, int oMdcyCc, int oMdcyYy, int oYmdYy, int oYmdMm, int oYmdDd, int oCymdCc, int oCymdYy, int oCymdMm, int oCymdDd, int oJulYy, int oJulDdd, String oExpMm, int oExpDd, int oExpCc, int oExpYy, int differenceInDays, int oDayNumber, String reqCountry, OutputNextMoYr outputNextMoYr, OutputLastMoYr outputLastMoYr, String i2IsoCc, String i2IsoYy, String i2IsoMm, String i2IsoDd) {
        super(reqDay, manipulateReqType, numberOfDays, numberOfMonths, inputDateType, oIsoCc, oIsoYy, oIsoMm, oIsoDd, oUsaMm, oUsaDd, oUsaCc, oUsaYy, oEurDd, oEurMm, oEurCc, oEurYy, oJisCc, oJisYy, oJisMm, oJisDd, oMdySlshMm, oMdySlshDd, oMdySlshYy, oMdyMm, oMdyDd, oMdyYy, oMdcySlshMm, oMdcySlshDd, oMdcySlshCc, oMdcySlshYy, oMdcyMm, oMdcyDd, oMdcyCc, oMdcyYy, oYmdYy, oYmdMm, oYmdDd, oCymdCc, oCymdYy, oCymdMm, oCymdDd, oJulYy, oJulDdd, oExpMm, oExpDd, oExpCc, oExpYy, differenceInDays, oDayNumber, reqCountry, outputNextMoYr, outputLastMoYr);
        this.i2IsoCc = i2IsoCc;
        this.i2IsoYy = i2IsoYy;
        this.i2IsoMm = i2IsoMm;
        this.i2IsoDd = i2IsoDd;
    }
    
    @Override
    public I2IsoDate clone() throws CloneNotSupportedException {
        I2IsoDate cloned = (I2IsoDate) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code I2IsoDate} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected I2IsoDate(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code I2IsoDate} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected I2IsoDate(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I2IsoDate} object
     * @see #setBytes(byte[], int)
     */
    public static I2IsoDate fromBytes(byte[] bytes, int offset) {
        return new I2IsoDate(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I2IsoDate} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static I2IsoDate fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code I2IsoDate} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static I2IsoDate fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getI2IsoCc() {
        return this.i2IsoCc;
    }
    
    public void setI2IsoCc(String i2IsoCc) {
        this.i2IsoCc = i2IsoCc;
    }
    
    public String getI2IsoYy() {
        return this.i2IsoYy;
    }
    
    public void setI2IsoYy(String i2IsoYy) {
        this.i2IsoYy = i2IsoYy;
    }
    
    public String getI2IsoMm() {
        return this.i2IsoMm;
    }
    
    public void setI2IsoMm(String i2IsoMm) {
        this.i2IsoMm = i2IsoMm;
    }
    
    public String getI2IsoDd() {
        return this.i2IsoDd;
    }
    
    public void setI2IsoDd(String i2IsoDd) {
        this.i2IsoDd = i2IsoDd;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        i2IsoCc = "";
        i2IsoYy = "";
        i2IsoMm = "";
        i2IsoDd = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ i2IsoCc=\"");
        s.append(getI2IsoCc());
        s.append("\"");
        s.append(", i2IsoYy=\"");
        s.append(getI2IsoYy());
        s.append("\"");
        s.append(", filler49=\"");
        s.append(new String(filler49, encoding));
        s.append("\"");
        s.append(", i2IsoMm=\"");
        s.append(getI2IsoMm());
        s.append("\"");
        s.append(", filler50=\"");
        s.append(new String(filler50, encoding));
        s.append("\"");
        s.append(", i2IsoDd=\"");
        s.append(getI2IsoDd());
        s.append("\"");
        s.append(", filler51=\"");
        s.append(new String(filler51, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(I2IsoDate that) {
        return super.equals(that) &&
            this.i2IsoCc.equals(that.i2IsoCc) &&
            this.i2IsoYy.equals(that.i2IsoYy) &&
            Arrays.equals(this.filler49, that.filler49) &&
            this.i2IsoMm.equals(that.i2IsoMm) &&
            Arrays.equals(this.filler50, that.filler50) &&
            this.i2IsoDd.equals(that.i2IsoDd) &&
            Arrays.equals(this.filler51, that.filler51);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof I2IsoDate other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof I2IsoDate;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(i2IsoCc);
        result = 31 * result + Objects.hashCode(i2IsoYy);
        result = 31 * result + Arrays.hashCode(filler49);
        result = 31 * result + Objects.hashCode(i2IsoMm);
        result = 31 * result + Arrays.hashCode(filler50);
        result = 31 * result + Objects.hashCode(i2IsoDd);
        result = 31 * result + Arrays.hashCode(filler51);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(I2IsoDate that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.i2IsoCc.compareTo(that.i2IsoCc);
        if ( c != 0 ) return c;
        c = this.i2IsoYy.compareTo(that.i2IsoYy);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler49, that.filler49);
        if ( c != 0 ) return c;
        c = this.i2IsoMm.compareTo(that.i2IsoMm);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler50, that.filler50);
        if ( c != 0 ) return c;
        c = this.i2IsoDd.compareTo(that.i2IsoDd);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler51, that.filler51);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(DateParmArea that) {
        if (that instanceof I2IsoDate other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(DateParmArea.SIZE);
    }
    
    private static final StringField I_2_ISO_CC = factory.getStringField(2);
    private static final StringField I_2_ISO_YY = factory.getStringField(2);
    private static final ByteArrayField FILLER_49 = factory.getByteArrayField(1);
    private byte[] filler49 = new byte[1];
    private static final StringField I_2_ISO_MM = factory.getStringField(2);
    private static final ByteArrayField FILLER_50 = factory.getByteArrayField(1);
    private byte[] filler50 = new byte[1];
    private static final StringField I_2_ISO_DD = factory.getStringField(2);
    private static final ByteArrayField FILLER_51 = factory.getByteArrayField(2);
    private byte[] filler51 = new byte[2];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "I2-ISO-DATE record at MXWW01.CPY:181"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        I_2_ISO_CC.putString(i2IsoCc, bytes, offset);
        I_2_ISO_YY.putString(i2IsoYy, bytes, offset);
        FILLER_49.putByteArray(filler49, bytes, offset);
        I_2_ISO_MM.putString(i2IsoMm, bytes, offset);
        FILLER_50.putByteArray(filler50, bytes, offset);
        I_2_ISO_DD.putString(i2IsoDd, bytes, offset);
        FILLER_51.putByteArray(filler51, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#setBytes(byte[], int)} to set parent-class state.
     * @see "I2-ISO-DATE record at MXWW01.CPY:181"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        i2IsoCc = I_2_ISO_CC.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        i2IsoYy = I_2_ISO_YY.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_49.getByteArray(bytes, offset);
        i2IsoMm = I_2_ISO_MM.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_50.getByteArray(bytes, offset);
        i2IsoDd = I_2_ISO_DD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_51.getByteArray(bytes, offset);
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
