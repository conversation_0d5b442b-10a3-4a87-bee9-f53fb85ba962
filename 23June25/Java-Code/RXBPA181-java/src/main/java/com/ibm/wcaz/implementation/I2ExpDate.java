package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class I2ExpDate extends DateParmArea {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String i2ExpMm = "";
    private String i2ExpDd = "";
    private String i2ExpCc = "";
    private String i2ExpYy = "";
    
    /** Initialize fields to non-null default values */
    public I2ExpDate() {}
    
    /** Initialize all fields to provided values */
    public I2ExpDate(int reqDay, int manipulateReqType, int numberOfDays, int numberOfMonths, int inputDateType, int oIsoCc, int oIsoYy, int oIsoMm, int oIsoDd, int oUsaMm, int oUsaDd, int oUsaCc, int oUsaYy, int oEurDd, int oEurMm, int oEurCc, int oEurYy, int oJisCc, int oJisYy, int oJisMm, int oJisDd, int oMdySlshMm, int oMdySlshDd, int oMdySlshYy, int oMdyMm, int oMdyDd, int oMdyYy, int oMdcySlshMm, int oMdcySlshDd, int oMdcySlshCc, int oMdcySlshYy, int oMdcyMm, int oMdcyDd, int oMdcyCc, int oMdcyYy, int oYmdYy, int oYmdMm, int oYmdDd, int oCymdCc, int oCymdYy, int oCymdMm, int oCymdDd, int oJulYy, int oJulDdd, String oExpMm, int oExpDd, int oExpCc, int oExpYy, int differenceInDays, int oDayNumber, String reqCountry, OutputNextMoYr outputNextMoYr, OutputLastMoYr outputLastMoYr, String i2ExpMm, String i2ExpDd, String i2ExpCc, String i2ExpYy) {
        super(reqDay, manipulateReqType, numberOfDays, numberOfMonths, inputDateType, oIsoCc, oIsoYy, oIsoMm, oIsoDd, oUsaMm, oUsaDd, oUsaCc, oUsaYy, oEurDd, oEurMm, oEurCc, oEurYy, oJisCc, oJisYy, oJisMm, oJisDd, oMdySlshMm, oMdySlshDd, oMdySlshYy, oMdyMm, oMdyDd, oMdyYy, oMdcySlshMm, oMdcySlshDd, oMdcySlshCc, oMdcySlshYy, oMdcyMm, oMdcyDd, oMdcyCc, oMdcyYy, oYmdYy, oYmdMm, oYmdDd, oCymdCc, oCymdYy, oCymdMm, oCymdDd, oJulYy, oJulDdd, oExpMm, oExpDd, oExpCc, oExpYy, differenceInDays, oDayNumber, reqCountry, outputNextMoYr, outputLastMoYr);
        this.i2ExpMm = i2ExpMm;
        this.i2ExpDd = i2ExpDd;
        this.i2ExpCc = i2ExpCc;
        this.i2ExpYy = i2ExpYy;
    }
    
    @Override
    public I2ExpDate clone() throws CloneNotSupportedException {
        I2ExpDate cloned = (I2ExpDate) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code I2ExpDate} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected I2ExpDate(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code I2ExpDate} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected I2ExpDate(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I2ExpDate} object
     * @see #setBytes(byte[], int)
     */
    public static I2ExpDate fromBytes(byte[] bytes, int offset) {
        return new I2ExpDate(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I2ExpDate} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static I2ExpDate fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code I2ExpDate} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static I2ExpDate fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getI2ExpMm() {
        return this.i2ExpMm;
    }
    
    public void setI2ExpMm(String i2ExpMm) {
        this.i2ExpMm = i2ExpMm;
    }
    
    public String getI2ExpDd() {
        return this.i2ExpDd;
    }
    
    public void setI2ExpDd(String i2ExpDd) {
        this.i2ExpDd = i2ExpDd;
    }
    
    public String getI2ExpCc() {
        return this.i2ExpCc;
    }
    
    public void setI2ExpCc(String i2ExpCc) {
        this.i2ExpCc = i2ExpCc;
    }
    
    public String getI2ExpYy() {
        return this.i2ExpYy;
    }
    
    public void setI2ExpYy(String i2ExpYy) {
        this.i2ExpYy = i2ExpYy;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        i2ExpMm = "";
        i2ExpDd = "";
        i2ExpCc = "";
        i2ExpYy = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ i2ExpMm=\"");
        s.append(getI2ExpMm());
        s.append("\"");
        s.append(", filler72=\"");
        s.append(new String(filler72, encoding));
        s.append("\"");
        s.append(", i2ExpDd=\"");
        s.append(getI2ExpDd());
        s.append("\"");
        s.append(", filler73=\"");
        s.append(new String(filler73, encoding));
        s.append("\"");
        s.append(", i2ExpCc=\"");
        s.append(getI2ExpCc());
        s.append("\"");
        s.append(", i2ExpYy=\"");
        s.append(getI2ExpYy());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(I2ExpDate that) {
        return super.equals(that) &&
            this.i2ExpMm.equals(that.i2ExpMm) &&
            Arrays.equals(this.filler72, that.filler72) &&
            this.i2ExpDd.equals(that.i2ExpDd) &&
            Arrays.equals(this.filler73, that.filler73) &&
            this.i2ExpCc.equals(that.i2ExpCc) &&
            this.i2ExpYy.equals(that.i2ExpYy);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof I2ExpDate other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof I2ExpDate;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(i2ExpMm);
        result = 31 * result + Arrays.hashCode(filler72);
        result = 31 * result + Objects.hashCode(i2ExpDd);
        result = 31 * result + Arrays.hashCode(filler73);
        result = 31 * result + Objects.hashCode(i2ExpCc);
        result = 31 * result + Objects.hashCode(i2ExpYy);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(I2ExpDate that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.i2ExpMm.compareTo(that.i2ExpMm);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler72, that.filler72);
        if ( c != 0 ) return c;
        c = this.i2ExpDd.compareTo(that.i2ExpDd);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler73, that.filler73);
        if ( c != 0 ) return c;
        c = this.i2ExpCc.compareTo(that.i2ExpCc);
        if ( c != 0 ) return c;
        c = this.i2ExpYy.compareTo(that.i2ExpYy);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(DateParmArea that) {
        if (that instanceof I2ExpDate other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(DateParmArea.SIZE);
    }
    
    private static final StringField I_2_EXP_MM = factory.getStringField(3);
    private static final ByteArrayField FILLER_72 = factory.getByteArrayField(1);
    private byte[] filler72 = new byte[1];
    private static final StringField I_2_EXP_DD = factory.getStringField(2);
    private static final ByteArrayField FILLER_73 = factory.getByteArrayField(2);
    private byte[] filler73 = new byte[2];
    private static final StringField I_2_EXP_CC = factory.getStringField(2);
    private static final StringField I_2_EXP_YY = factory.getStringField(2);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "I2-EXP-DATE record at MXWW01.CPY:264"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        I_2_EXP_MM.putString(i2ExpMm, bytes, offset);
        FILLER_72.putByteArray(filler72, bytes, offset);
        I_2_EXP_DD.putString(i2ExpDd, bytes, offset);
        FILLER_73.putByteArray(filler73, bytes, offset);
        I_2_EXP_CC.putString(i2ExpCc, bytes, offset);
        I_2_EXP_YY.putString(i2ExpYy, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#setBytes(byte[], int)} to set parent-class state.
     * @see "I2-EXP-DATE record at MXWW01.CPY:264"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        i2ExpMm = I_2_EXP_MM.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_72.getByteArray(bytes, offset);
        i2ExpDd = I_2_EXP_DD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_73.getByteArray(bytes, offset);
        i2ExpCc = I_2_EXP_CC.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        i2ExpYy = I_2_EXP_YY.getString(bytes, offset).replaceFirst("\\s+\\z", "");
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
