package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class I2MdcyDate extends DateParmArea {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String i2MdcyMm = "";
    private String i2MdcyDd = "";
    private String i2MdcyCc = "";
    private String i2MdcyYy = "";
    
    /** Initialize fields to non-null default values */
    public I2MdcyDate() {}
    
    /** Initialize all fields to provided values */
    public I2MdcyDate(int reqDay, int manipulateReqType, int numberOfDays, int numberOfMonths, int inputDateType, int oIsoCc, int oIsoYy, int oIsoMm, int oIsoDd, int oUsaMm, int oUsaDd, int oUsaCc, int oUsaYy, int oEurDd, int oEurMm, int oEurCc, int oEurYy, int oJisCc, int oJisYy, int oJisMm, int oJisDd, int oMdySlshMm, int oMdySlshDd, int oMdySlshYy, int oMdyMm, int oMdyDd, int oMdyYy, int oMdcySlshMm, int oMdcySlshDd, int oMdcySlshCc, int oMdcySlshYy, int oMdcyMm, int oMdcyDd, int oMdcyCc, int oMdcyYy, int oYmdYy, int oYmdMm, int oYmdDd, int oCymdCc, int oCymdYy, int oCymdMm, int oCymdDd, int oJulYy, int oJulDdd, String oExpMm, int oExpDd, int oExpCc, int oExpYy, int differenceInDays, int oDayNumber, String reqCountry, OutputNextMoYr outputNextMoYr, OutputLastMoYr outputLastMoYr, String i2MdcyMm, String i2MdcyDd, String i2MdcyCc, String i2MdcyYy) {
        super(reqDay, manipulateReqType, numberOfDays, numberOfMonths, inputDateType, oIsoCc, oIsoYy, oIsoMm, oIsoDd, oUsaMm, oUsaDd, oUsaCc, oUsaYy, oEurDd, oEurMm, oEurCc, oEurYy, oJisCc, oJisYy, oJisMm, oJisDd, oMdySlshMm, oMdySlshDd, oMdySlshYy, oMdyMm, oMdyDd, oMdyYy, oMdcySlshMm, oMdcySlshDd, oMdcySlshCc, oMdcySlshYy, oMdcyMm, oMdcyDd, oMdcyCc, oMdcyYy, oYmdYy, oYmdMm, oYmdDd, oCymdCc, oCymdYy, oCymdMm, oCymdDd, oJulYy, oJulDdd, oExpMm, oExpDd, oExpCc, oExpYy, differenceInDays, oDayNumber, reqCountry, outputNextMoYr, outputLastMoYr);
        this.i2MdcyMm = i2MdcyMm;
        this.i2MdcyDd = i2MdcyDd;
        this.i2MdcyCc = i2MdcyCc;
        this.i2MdcyYy = i2MdcyYy;
    }
    
    @Override
    public I2MdcyDate clone() throws CloneNotSupportedException {
        I2MdcyDate cloned = (I2MdcyDate) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code I2MdcyDate} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected I2MdcyDate(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code I2MdcyDate} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected I2MdcyDate(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I2MdcyDate} object
     * @see #setBytes(byte[], int)
     */
    public static I2MdcyDate fromBytes(byte[] bytes, int offset) {
        return new I2MdcyDate(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I2MdcyDate} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static I2MdcyDate fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code I2MdcyDate} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static I2MdcyDate fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getI2MdcyMm() {
        return this.i2MdcyMm;
    }
    
    public void setI2MdcyMm(String i2MdcyMm) {
        this.i2MdcyMm = i2MdcyMm;
    }
    
    public String getI2MdcyDd() {
        return this.i2MdcyDd;
    }
    
    public void setI2MdcyDd(String i2MdcyDd) {
        this.i2MdcyDd = i2MdcyDd;
    }
    
    public String getI2MdcyCc() {
        return this.i2MdcyCc;
    }
    
    public void setI2MdcyCc(String i2MdcyCc) {
        this.i2MdcyCc = i2MdcyCc;
    }
    
    public String getI2MdcyYy() {
        return this.i2MdcyYy;
    }
    
    public void setI2MdcyYy(String i2MdcyYy) {
        this.i2MdcyYy = i2MdcyYy;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        i2MdcyMm = "";
        i2MdcyDd = "";
        i2MdcyCc = "";
        i2MdcyYy = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ i2MdcyMm=\"");
        s.append(getI2MdcyMm());
        s.append("\"");
        s.append(", i2MdcyDd=\"");
        s.append(getI2MdcyDd());
        s.append("\"");
        s.append(", i2MdcyCc=\"");
        s.append(getI2MdcyCc());
        s.append("\"");
        s.append(", i2MdcyYy=\"");
        s.append(getI2MdcyYy());
        s.append("\"");
        s.append(", filler68=\"");
        s.append(new String(filler68, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(I2MdcyDate that) {
        return super.equals(that) &&
            this.i2MdcyMm.equals(that.i2MdcyMm) &&
            this.i2MdcyDd.equals(that.i2MdcyDd) &&
            this.i2MdcyCc.equals(that.i2MdcyCc) &&
            this.i2MdcyYy.equals(that.i2MdcyYy) &&
            Arrays.equals(this.filler68, that.filler68);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof I2MdcyDate other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof I2MdcyDate;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(i2MdcyMm);
        result = 31 * result + Objects.hashCode(i2MdcyDd);
        result = 31 * result + Objects.hashCode(i2MdcyCc);
        result = 31 * result + Objects.hashCode(i2MdcyYy);
        result = 31 * result + Arrays.hashCode(filler68);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(I2MdcyDate that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.i2MdcyMm.compareTo(that.i2MdcyMm);
        if ( c != 0 ) return c;
        c = this.i2MdcyDd.compareTo(that.i2MdcyDd);
        if ( c != 0 ) return c;
        c = this.i2MdcyCc.compareTo(that.i2MdcyCc);
        if ( c != 0 ) return c;
        c = this.i2MdcyYy.compareTo(that.i2MdcyYy);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler68, that.filler68);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(DateParmArea that) {
        if (that instanceof I2MdcyDate other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(DateParmArea.SIZE);
    }
    
    private static final StringField I_2_MDCY_MM = factory.getStringField(2);
    private static final StringField I_2_MDCY_DD = factory.getStringField(2);
    private static final StringField I_2_MDCY_CC = factory.getStringField(2);
    private static final StringField I_2_MDCY_YY = factory.getStringField(2);
    private static final ByteArrayField FILLER_68 = factory.getByteArrayField(4);
    private byte[] filler68 = new byte[4];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "I2-MDCY-DATE record at MXWW01.CPY:238"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        I_2_MDCY_MM.putString(i2MdcyMm, bytes, offset);
        I_2_MDCY_DD.putString(i2MdcyDd, bytes, offset);
        I_2_MDCY_CC.putString(i2MdcyCc, bytes, offset);
        I_2_MDCY_YY.putString(i2MdcyYy, bytes, offset);
        FILLER_68.putByteArray(filler68, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#setBytes(byte[], int)} to set parent-class state.
     * @see "I2-MDCY-DATE record at MXWW01.CPY:238"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        i2MdcyMm = I_2_MDCY_MM.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        i2MdcyDd = I_2_MDCY_DD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        i2MdcyCc = I_2_MDCY_CC.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        i2MdcyYy = I_2_MDCY_YY.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_68.getByteArray(bytes, offset);
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
