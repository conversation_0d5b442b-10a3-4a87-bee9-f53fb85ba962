package com.ibm.wcaz.implementation;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;

import org.h2.jdbc.JdbcConnection;

import com.ibm.jzos.ZFile;
import com.ibm.jzos.ZFileException;

public class Rxbpasvc {
    private ZFile cpuFormatFile;
    private static Rxbpasvc rxbpasvc = new Rxbpasvc();
    private String returnCode = "00000";
    private WsFields wsFields = new WsFields();

    /** Mode to open a file in */
    public static enum OpenMode {
        /** read-only */
        READ("r"),
        /** write-only, over-writing */
        WRITE("w"),
        /** write-only, appending */
        APPEND("a"),
        /** read-write */
        READ_WRITE("r+");

        private String str;

        OpenMode(String str) {
            this.str = str;
        }

        /** fopen() mode-string corresponding to this mode */
        public String modeStr() {
            return str;
        }
    }

    public void openCpuFormatFile(OpenMode mode) throws ZFileException {
        cpuFormatFile = new ZFile("//DD:MXPA181I", mode.modeStr() + "b,type=record");
    }

    public void closeCpuFormatFile() throws ZFileException {
        cpuFormatFile.close();
    }

    /** Initialize fields to non-null default values */
    public Rxbpasvc() {
    }

    public ZFile getCpuFormatFile() {
        return this.cpuFormatFile;
    }

    public String getReturnCode() {
        return this.returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    public static Rxbpasvc getInstance() {
        return rxbpasvc;
    }

    public void processTrailer6000(WsCountFields wsCountFields, WsFileStatus wsFileStatus, WsFlags wsFlags,
            WsSaveArea wsSaveArea, WsWorkFields wsWorkFields, ZRegTrlRec zRegTrlRec) {
        wsFlags.setTrailerRecRead();
        wsCountFields.setWsTotTrlRecCnt(wsCountFields.getWsTotTrlRecCnt() + 1);
        wsWorkFields.setWsTrailerRecCnt(Integer.parseInt(zRegTrlRec.getZCustTrlCount()));
        if (wsFileStatus.getWsFileStatus().length() > 0) {
            return;
        }
        if (!zRegTrlRec.getZCdfCustId().equals(wsSaveArea.getWsSaveCdfCustId())) {
            wsFileStatus.setCustIdMismatch();
            return;
        }
        if (!zRegTrlRec.getZCdfCustCode().equals(wsSaveArea.getWsSaveCdfCustCode())) {
            wsFileStatus.setCustCodeMismatch();
            return;
        }
        if (!zRegTrlRec.getZCreatDate().equals(wsSaveArea.getWsSaveCreatDate())) {
            wsFileStatus.setCreatDateMismatch();
            return;
        }
        if (!zRegTrlRec.getZCreatTime().equals(wsSaveArea.getWsSaveCreatTime())) {
            wsFileStatus.setCreatTimeMismatch();
            return;
        }
        if (zRegTrlRec.getZCustTrlCount().equals(" ")) {
            wsFileStatus.setRecCountInvalid();
            return;
        }
        if (!zRegTrlRec.getZCustTrlCount().matches("\\d+")) {
            wsFileStatus.setRecCountInvalid();
            return;
        }
        if (Integer.parseInt(zRegTrlRec.getZCustTrlCount()) != wsWorkFields.getWsDetailRecCnt()) {
            wsFileStatus.setRecCountMismatch();
            return;
        }
    }

    public void insertTrailerFile7600(AbnormalTerminationArea abnormalTerminationArea, WsCountFields wsCountFields,
            WsFields wsFields, WsFileStatus wsFileStatus, WsPreviousCpuKey wsPreviousCpuKey,
            WsWorkFields wsWorkFields) {
        String abtDaFunction = "";
        Dclvwmbhrg dclvwmbhrg = new Dclvwmbhrg();
        dclvwmbhrg.setRegComplDt(wsWorkFields.getWsProcDate());
        dclvwmbhrg.setProcDate(wsWorkFields.getWsProcDate());
        dclvwmbhrg.setFileSeqNo(wsFields.getWsFileSeqNo());
        dclvwmbhrg.setFileRejDs(wsFileStatus.getWsFileStatus());
        dclvwmbhrg.setItemSeqNo(wsFields.getWsItemSeqNo());
        dclvwmbhrg.setInputCpuId(wsPreviousCpuKey.getWsPreviousCdfCustId());
        dclvwmbhrg.setInputCpuCode(wsPreviousCpuKey.getWsPreviousCdfCustCode());
        dclvwmbhrg.setInputCreateDate(wsPreviousCpuKey.getWsPreviousCreatDate());
        dclvwmbhrg.setInputCreateTime(wsPreviousCpuKey.getWsPreviousCreatTime());
        dclvwmbhrg.setInputRecCnt(String.format("%06d", wsWorkFields.getWsTrailerRecCnt()));
        sqlInsertBhrg8000(dclvwmbhrg);
        // Assume successful insert for now
        wsCountFields.setWsTotBhrgInsCnt(wsCountFields.getWsTotBhrgInsCnt() + 1);
    }

    public void processCpuFile2000(AbnormalTerminationArea abnormalTerminationArea, CpuRegdtlLayout cpuRegdtlLayout,
            WsCountFields wsCountFields, WsCurrentCpuKey wsCurrentCpuKey, WsDlrNoChar wsDlrNoChar,
            WsFileStatus wsFileStatus, WsFlags wsFlags, WsPreviousCpuKey wsPreviousCpuKey, WsSaveArea wsSaveArea,
            WsWorkFields wsWorkFields) {
        DateParmArea dateParmArea = new DateParmArea();
        WsItemStatus wsItemStatus = new WsItemStatus();
        if (wsFlags.isEmptyCpuFile()) {
            return;
        }
        if (wsCurrentCpuKey.equals(wsPreviousCpuKey)) {
            return;
        }
        processKeyChange3000(abnormalTerminationArea, wsCountFields, wsCurrentCpuKey, wsFields, wsFileStatus, wsFlags,
                wsPreviousCpuKey, wsSaveArea, wsWorkFields);
        while (true) {
            if (cpuRegdtlLayout.isCpuRegHdrRec()) {
                processHeader4000(abnormalTerminationArea, dateParmArea, (HRegHdrRec) cpuRegdtlLayout, wsCountFields,
                        wsFileStatus, wsFlags, wsSaveArea);
            } else if (cpuRegdtlLayout.isCpuRegDtlRec()) {
                if (wsFlags.isHeaderRecNotRead()) {
                    wsFileStatus.setHeaderRecMissing();
                }
                processDetail5000(abnormalTerminationArea, (DRegDtlRec) cpuRegdtlLayout, dateParmArea, wsCountFields,
                        wsFields, wsFlags, wsItemStatus, wsSaveArea, wsWorkFields);
                insertDetailRecs7000(abnormalTerminationArea, (DRegDtlRec) cpuRegdtlLayout, wsCountFields, wsDlrNoChar,
                        wsFileStatus, wsItemStatus, wsSaveArea, wsWorkFields);
            } else if (cpuRegdtlLayout.isCpuRegTrlRec()) {
                if (wsFlags.isHeaderRecNotRead()) {
                    wsFileStatus.setHeaderRecMissing();
                }
                processTrailer6000(wsCountFields, wsFileStatus, wsFlags, wsSaveArea, wsWorkFields,
                        (ZRegTrlRec) cpuRegdtlLayout);
                updateDetailRecs7100(abnormalTerminationArea, wsCountFields, wsFields, wsFileStatus, wsWorkFields,
                        (ZRegTrlRec) cpuRegdtlLayout);
            } else {
                wsItemStatus.setInvalidRecType();
                wsDlrNoChar.setWsItemSeqNo(wsDlrNoChar.getWsItemSeqNo() + 1);
                wsCountFields.setWsTotInvRecCnt(wsCountFields.getWsTotInvRecCnt() + 1);
                // Create a CpuRegdtlData instance from the layout
                CpuRegdtlData cpuRegdtlData = new CpuRegdtlData();
                cpuRegdtlData.setBytes(cpuRegdtlLayout.getBytes());
                insertInvalidFile7300(abnormalTerminationArea, cpuRegdtlData, wsCountFields, wsFields,
                        wsFileStatus, wsItemStatus, wsSaveArea, wsWorkFields);
            }
            readInputFile1300(abnormalTerminationArea, cpuRegdtlLayout, wsCountFields, wsFlags);
            if (wsFlags.isWsEndOfFile()) {
                break;
            }
            moveKeyFields1400(cpuRegdtlLayout, wsCurrentCpuKey);
        }
    }

    public void validateVendIdCodeNo5100(AbnormalTerminationArea abnormalTerminationArea, DRegDtlRec dRegDtlRec,
            WsFields wsFields, WsItemStatus wsItemStatus) {
        String abtDaFunction = "";
        Dclvwmcucp dclvwmcucp = new Dclvwmcucp();
        int wsDealerNo;
        try {
            String sql = "SELECT A.CPU_ID, A.CPU_CODE, A.CPU_DEALER_NO, A.CUST_NO FROM VWMCUCP A, VWMCUCP B WHERE A.CPU_ID = B.CPU_ID AND A.CPU_CODE = B.CPU_CODE AND A.CUST_NO = B.CUST_NO AND A.CPU_DEALER_NO = B.CPU_DEALER_NO AND A.CPU_ID = ? AND A.CPU_CODE = ? AND A.CPU_DEALER_NO = ? WITH UR";
            // PreparedStatement ps = JdbcConnection.connection.prepareStatement(sql);
            Connection connection = DriverManager.getConnection("jdbc:example_database_url", "example_username", "example_password");
            PreparedStatement ps = connection.prepareStatement(sql);
            ps.setString(1, dRegDtlRec.getDCdfCustId());
            ps.setString(2, wsFields.getWsCpuCode());
            ps.setString(3, dRegDtlRec.getDVendDlrNo());
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                dclvwmcucp.setCpuId(rs.getString(1));
                dclvwmcucp.setCpuCode(rs.getString(2));
                dclvwmcucp.setCpuDealerNo(rs.getString(3));
                dclvwmcucp.setCustNo(rs.getInt(4));
            }
            ps.close();
        } catch (SQLException exception) {
            if (exception.getErrorCode() == 100) {
                wsItemStatus.setVendIdCodeNbrNotFnd();
            } else if (exception.getErrorCode() == -811) {
                wsItemStatus.setVendIdCodeNbrDup();
                System.out.println("DUPLICATE DEALER DETAILS:");
                System.out.println("DLR-NO        :" + dRegDtlRec.getDVendDlrNo());
                System.out.println("CPU-ID        :" + dRegDtlRec.getDCdfCustId());
                System.out.println("CPU-CODE      :" + wsFields.getWsCpuCode());
                System.out.println("CUST-NO       :" + wsDealerNo);
            } else {
                abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorActivity("DB2");
                abnormalTerminationArea.getAbtPgmErrorData().setAbtDaAccessName("VWMCUCP");
                abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorSection("5100-");
                abnormalTerminationArea.getAbtPgmErrorData().setAbtDaFunction("SELECT");
                abnormalTerminationArea.getAbtDataAccessInfo().setAbtDb2Status(exception.getErrorCode());
                z980AbnormalTerm(abnormalTerminationArea);
            }
        }
    }

    public void dtlNotReadProc3500(AbnormalTerminationArea abnormalTerminationArea, WsCountFields wsCountFields,
            WsFields wsFields, WsFileStatus wsFileStatus, WsFlags wsFlags, WsPreviousCpuKey wsPreviousCpuKey,
            WsSaveArea wsSaveArea, WsWorkFields wsWorkFields) {
        wsFields.setWsItemSeqNo(wsFields.getWsItemSeqNo() + 1);
        if (wsFlags.isHeaderRecRead() && wsFlags.isTrailerRecRead()) {
            insertHeaderTrailer7400(abnormalTerminationArea, wsCountFields, wsFields, wsFileStatus, wsSaveArea,
                    wsWorkFields);
        } else if (wsFlags.isHeaderRecRead()) {
            insertHeaderFile7500(abnormalTerminationArea, wsCountFields, wsFields, wsFileStatus, wsSaveArea,
                    wsWorkFields);
        } else if (wsFlags.isTrailerRecRead()) {
            insertTrailerFile7600(abnormalTerminationArea, wsCountFields, wsFields, wsFileStatus, wsPreviousCpuKey,
                    wsWorkFields);
        }
    }

    public void updateNoTrailer7700(AbnormalTerminationArea abnormalTerminationArea, WsCountFields wsCountFields,
            WsFields wsFields, WsFileStatus wsFileStatus, WsSaveArea wsSaveArea, WsWorkFields wsWorkFields) {
        String abtDaFunction = "";
        Dclvwmbhrg dclvwmbhrg = new Dclvwmbhrg();
        if (wsFileStatus.getWsFileStatus().equals("")) {
            wsFileStatus.setTrailerRecMissing();
        }
        dclvwmbhrg.setRegComplDt(wsWorkFields.getWsProcDate());
        dclvwmbhrg.setProcDate(wsWorkFields.getWsProcDate());
        dclvwmbhrg.setFileSeqNo(wsFields.getWsFileSeqNo());
        dclvwmbhrg.setFileRejDs(wsFileStatus.getWsFileStatus());
        dclvwmbhrg.setInputCpuId(wsSaveArea.getWsSaveCdfCustId());
        dclvwmbhrg.setInputCpuCode(wsSaveArea.getWsSaveCdfCustCode());
        dclvwmbhrg.setInputCreateDate(wsSaveArea.getWsSaveCreatDate());
        dclvwmbhrg.setInputCreateTime(wsSaveArea.getWsSaveCreatTime());
        dclvwmbhrg.setInputRecCnt("000000");
        sqlUpdateBhrg8100(dclvwmbhrg);
        // Assume successful update for now
        wsCountFields.setWsTotBhrgUpdCnt(wsCountFields.getWsTotBhrgUpdCnt() + 1);
                System.out.println("PROC-DATE  :" + dclvwmbhrg.getProcDate());
                System.out.println("FILE-SEQ-NO:" + dclvwmbhrg.getFileSeqNo());
                System.out.println("FILE-REJ-DS:" + dclvwmbhrg.getFileRejDs());
                System.out.println("CPU-ID     :" + dclvwmbhrg.getInputCpuId());
                System.out.println("CPU-CODE   :" + dclvwmbhrg.getInputCpuCode());
                System.out.println("CREATE-TIME:" + dclvwmbhrg.getInputCreateTime());
                System.out.println("CREATE-DATE:" + dclvwmbhrg.getInputCreateDate());
                System.out.println("INP-REC-CNT:" + dclvwmbhrg.getInputRecCnt());
                abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
                abtDaFunction = "UPDATE";
                abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorActivity("DB2");
                abnormalTerminationArea.getAbtDataAccessInfo().setAbtDaAccessName("VWMBHRG");
                abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorSection("7700-");
                z980AbnormalTerm(abnormalTerminationArea);
        }
    }

    public void sqlInsertBhrg8000(Dclvwmbhrg dclvwmbhrg) {
        try {
            String sql = "INSERT INTO VWMBHRG(PROC_DATE, FILE_SEQ_NO, FILE_REJ_DS, ITEM_SEQ_NO, ITEM_REJ_DS, INPUT_MFG_NO, INPUT_DIST_NO, INPUT_DLR_NO, INPUT_CPU_ID, INPUT_CPU_CODE, INPUT_CREATE_DATE, INPUT_CREATE_TIME, INPUT_MFG_NAME, INPUT_REC_CNT, CPU_DLR_NO, CUST_NAME, MODEL_NO, MODEL_DS, SERIAL_NO, REG_COMPL_DT, REG_TYPE_CD)values(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            // PreparedStatement ps = JdbcConnection.connection.prepareStatement(sql);
            Connection connection = DriverManager.getConnection("jdbc:example_database_url", "example_username", "example_password");
            PreparedStatement ps = connection.prepareStatement(sql);
            ps.setString(1, dclvwmbhrg.getProcDate());
            ps.setInt(2, dclvwmbhrg.getFileSeqNo());
            ps.setString(3, dclvwmbhrg.getFileRejDs());
            ps.setInt(4, dclvwmbhrg.getItemSeqNo());
            ps.setString(5, dclvwmbhrg.getItemRejDs());
            ps.setString(6, dclvwmbhrg.getInputMfgNo());
            ps.setString(7, dclvwmbhrg.getInputDistNo());
            ps.setString(8, dclvwmbhrg.getInputDlrNo());
            ps.setString(9, dclvwmbhrg.getInputCpuId());
            ps.setString(10, dclvwmbhrg.getInputCpuCode());
            ps.setString(11, dclvwmbhrg.getInputCreateDate());
            ps.setString(12, dclvwmbhrg.getInputCreateTime());
            ps.setString(13, dclvwmbhrg.getInputMfgName());
            ps.setString(14, dclvwmbhrg.getInputRecCnt());
            ps.setString(15, dclvwmbhrg.getCpuDlrNo());
            ps.setString(16, dclvwmbhrg.getCustName());
            ps.setString(17, dclvwmbhrg.getModelNo());
            ps.setString(18, dclvwmbhrg.getModelDs());
            ps.setString(19, dclvwmbhrg.getSerialNo());
            ps.setString(20, dclvwmbhrg.getRegComplDt());
            ps.setString(21, dclvwmbhrg.getRegTypeCd());
            ps.executeUpdate();
            ps.close();
        } catch (SQLException exception) {
            System.out.println(exception);
            return;
        }
    }

    public void processHeader4000(AbnormalTerminationArea abnormalTerminationArea, DateParmArea dateParmArea,
            HRegHdrRec hRegHdrRec, WsCountFields wsCountFields, WsFileStatus wsFileStatus, WsFlags wsFlags,
            WsSaveArea wsSaveArea) {
        Dclvwmcpcd dclvwmcpcd = new Dclvwmcpcd();
        WsWorkFields wsWorkFields = new WsWorkFields();
        wsFlags.setHeaderRecRead();
        wsCountFields.setWsTotHdrRecCnt(wsCountFields.getWsTotHdrRecCnt() + 1);
        saveHeaderFields4400(hRegHdrRec);
        if (hRegHdrRec.getHCdfCustId().equals(String.valueOf(' ').repeat(4))) {
            wsFileStatus.setCustIdNotPresent();
            return;
        }
        if (hRegHdrRec.getHCdfCustCode().equals(String.valueOf(' ').repeat(4))) {
            wsFileStatus.setCustCodeNotPresent();
            return;
        }
        if (hRegHdrRec.getHCdfCustId().equals(wsWorkFields.getWs3ACpuid())) {
            // TODO: Empty block may need to be addressed
        } else {
            validateCustIdCode4100(abnormalTerminationArea, dclvwmcpcd, hRegHdrRec, wsFileStatus);
            if (wsFileStatus.getWsFileStatus().length() > 0) {
                return;
            }
        }
        if (hRegHdrRec.getHCreatDate().equals(String.valueOf(' ').repeat(10))) {
            wsFileStatus.setCreatDateNotPresent();
            return;
        }
        validateCreatDate4200(abnormalTerminationArea, hRegHdrRec, wsFileStatus);
        if (wsFileStatus.getWsFileStatus().length() > 0) {
            return;
        }
        if (hRegHdrRec.getHCreatTime().equals(String.valueOf(' ').repeat(5))) {
            wsFileStatus.setCreatTimeNotPresent();
            return;
        }
        validateCreatTime4300(hRegHdrRec, wsFileStatus);
        if (wsFileStatus.getWsFileStatus().length() > 0) {
            return;
        }
        if (hRegHdrRec.getHDocType().equals(String.valueOf(' ').repeat(24))) {
            wsFileStatus.setDocTypeNotPresent();
            return;
        }
        if (!hRegHdrRec.isHDocData()) {
            wsFileStatus.setDocTypeNotUnitReg();
            return;
        }
    }

    public void processDetail5000(AbnormalTerminationArea abnormalTerminationArea, DRegDtlRec dRegDtlRec,
            DateParmArea dateParmArea, WsCountFields wsCountFields, WsFields wsFields, WsFlags wsFlags,
            WsItemStatus wsItemStatus, WsSaveArea wsSaveArea, WsWorkFields wsWorkFields) {
        int wsSaveMfgNo = 0;
        int wsSaveDistNo = 0;

        wsFlags.setDetailRecRead();
        wsFields.setWsItemSeqNo(wsFields.getWsItemSeqNo() + 1);
        wsWorkFields.setWsDetailRecCnt(wsWorkFields.getWsDetailRecCnt() + 1);
        wsCountFields.setWsTotDtlRecCnt(wsCountFields.getWsTotDtlRecCnt() + 1);
        wsWorkFields.setWsRegComplDate(dRegDtlRec.getDRegComplDate());
        wsWorkFields.getWsRegComplDate().replaceAll("\\.", "-");
        if (!dRegDtlRec.getDCdfCustId().equals(wsSaveArea.getWsSaveCdfCustId())) {
            wsItemStatus.setDtlCustIdMismatch();
            return;
        }
        if (!dRegDtlRec.getDCdfCustCode().equals(wsSaveArea.getWsSaveCdfCustCode())) {
            wsItemStatus.setDtlCustCodeMismatch();
            return;
        }
        if (!dRegDtlRec.getDCreatDate().equals(wsSaveArea.getWsSaveCreatDate())) {
            wsItemStatus.setDtlCrtDateMismatch();
            return;
        }
        if (!dRegDtlRec.getDCreatTime().equals(wsSaveArea.getWsSaveCreatTime())) {
            wsItemStatus.setDtlCrtTimeMismatch();
            return;
        }
        if (dRegDtlRec.getDVendDlrNo().equals(" ")) {
            wsItemStatus.setVendNbrNotPresent();
            return;
        }
        wsFields.setWsCpuCode(dRegDtlRec.getDCdfCustCode());
        validateVendIdCodeNo5100(abnormalTerminationArea, dRegDtlRec, wsFields, wsItemStatus);
        if (wsItemStatus.isVendIdCodeNbrDup()) {
            return;
        }
        if (!wsItemStatus.getWsItemStatus().equals(" ")) {
            wsItemStatus.setWsItemStatus(" ");
            wsFields.setWsCpuCode("9999");
            validateVendIdCodeNo5100(abnormalTerminationArea, dRegDtlRec, wsFields, wsItemStatus);
            if (!wsItemStatus.getWsItemStatus().equals(" ")) {
                return;
            }
        }
        if (dRegDtlRec.getDVendDlrName().equals(" ")) {
            wsItemStatus.setVendNameNotPresent();
            return;
        }
        if (dRegDtlRec.getDModelDesc().equals(" ")) {
            wsItemStatus.setModelDescNotPresent();
            return;
        }
        if (dRegDtlRec.getDModelNbr().equals(" ")) {
            wsItemStatus.setModelNbrNotPresent();
            return;
        }

        if (dRegDtlRec.getDCdfCustId().equals(wsWorkFields.getWs3ACpuid())) {
            wsSaveDistNo = Integer.parseInt(dRegDtlRec.getDDistNo());
            wsSaveMfgNo = Integer.parseInt(dRegDtlRec.getDDistNo());
        }
        if (dRegDtlRec.getDSerialNbr().equals("")) {
            wsItemStatus.setSerVinNotPresent();
            return;
        }
        if (dRegDtlRec.getDRegComplDate().equals("")) {
            wsItemStatus.setRegCompDateNotPresent();
            return;
        }
        validateRegDate5200(abnormalTerminationArea, dRegDtlRec, dateParmArea, wsItemStatus);
        if (wsItemStatus.getWsItemStatus().length() > 0) {
            return;
        }
        if (dRegDtlRec.getDRegType().equals("")) {
            wsItemStatus.setRegTypeNotPresent();
            return;
        }
        wsWorkFields.setWsRegType(dRegDtlRec.getDRegType());
        if (!wsWorkFields.isValidRegTypes()) {
            wsItemStatus.setRegTypeInvalid();
            return;
        }

    }

    public void z970SetDaStatusDb2() {
        AbnormalTerminationArea abnormalTerminationArea = new AbnormalTerminationArea();
        String daDbmerrorLit = "DBM";
        String daDuplicateLit = "DUP";
        String daLogicerrLit = "LOG";
        String daNotavailLit = "NAV";
        String daNotfoundLit = "NFD";
        String daOkLit = "OK ";
        String daSecurityLit = "SEC";
        DataAccessStatus dataAccessStatus = new DataAccessStatus();
        dataAccessStatus.setDaStatus(abnormalTerminationArea.getAbtDataAccessInfo().getAbtDb2Status() == 0 ? daOkLit
                : abnormalTerminationArea.getAbtDataAccessInfo().getAbtDb2Status() == 100 ? daNotfoundLit
                        : abnormalTerminationArea.getAbtDataAccessInfo().getAbtDb2Status() == -803
                                || abnormalTerminationArea.getAbtDataAccessInfo().getAbtDb2Status() == -811
                                        ? daDuplicateLit
                                        : abnormalTerminationArea.getAbtDataAccessInfo().getAbtDb2Status() == -501
                                                || abnormalTerminationArea.getAbtDataAccessInfo()
                                                        .getAbtDb2Status() == -502
                                                                ? daLogicerrLit
                                                                : abnormalTerminationArea.getAbtDataAccessInfo()
                                                                        .getAbtDb2Status() == -922
                                                                                ? daSecurityLit
                                                                                : abnormalTerminationArea
                                                                                        .getAbtDataAccessInfo()
                                                                                        .getAbtDb2Status() == -911
                                                                                        || abnormalTerminationArea
                                                                                                .getAbtDataAccessInfo()
                                                                                                .getAbtDb2Status() == -913
                                                                                                        ? daNotavailLit
                                                                                                        : daDbmerrorLit);
    }

    public void validateCreatDate4200(AbnormalTerminationArea abnormalTerminationArea, HRegHdrRec hRegHdrRec, WsFileStatus wsFileStatus) {
    String abtDaFunction = "";
    DateParmArea dateParmArea = new DateParmArea();
    String input1DateArea = "";
    LoadTableArea loadTableArea = new LoadTableArea();
    WsWorkFields wsWorkFields = new WsWorkFields();
    wsWorkFields.getWsCpuDate().setBytes(wsWorkFields.getWsCpuDate().getBytes());
    input1DateArea = hRegHdrRec.getHCreatDate();
    wsWorkFields.getWsCpuDate().setBytes(input1DateArea.getBytes());
    if (wsWorkFields.getWsCpuDate().getWsDtSep1() == '-' && wsWorkFields.getWsCpuDate().getWsDtSep2() == '-') {
        dateParmArea.setIIsoFormat();
    }
    else if (wsWorkFields.getWsCpuDate().getWsDtSep1() == '.' && wsWorkFields.getWsCpuDate().getWsDtSep2() == '.') {
        input1DateArea = input1DateArea.replaceAll("\\.", "-");
        wsWorkFields.getWsCpuDate().setBytes(input1DateArea.getBytes());
        dateParmArea.setIIsoFormat();
    }
    else {
        wsFileStatus.setCreatDateNotValid();
        return;
    }
    dateParmArea.setDateToDate();
    input1DateArea = wsWorkFields.getWsCpuDate().getWsCpuDate();
    dateParmArea.setInputDateType(1);
    input1DateArea = input1DateArea.substring(0, 2) + "/" + input1DateArea.substring(2, 4) + "/" + input1DateArea.substring(4, 8);
    input1DateArea = input1DateArea.substring(0, 10);
    input1DateArea = input1DateArea.substring(0, 2) + "/" + input1DateArea.substring(2, 4) + "/" + input1DateArea.substring(4, 8);
    input1DateArea = input1DateArea.substring(0, 10);
    input1DateArea = input1DateArea.substring(0, 2) + "/" + input1DateArea.substring(2, 4) + "/" + input1DateArea.substring(4, 8);
    input1DateArea = input1DateArea.substring(0, 10);
    input1DateArea = input1DateArea.substring(0, 2) + "/" + input1DateArea.substring(2, 4) + "/" + input1DateArea.substring(4, 8);
    input1DateArea = input1DateArea.substring(0, 10);
    input1DateArea = input1DateArea.substring(0, 2) + "/" + input1DateArea.substring(2, 4) + "/" + input1DateArea.substring(4, 8);
    input1DateArea = input1DateArea.substring(0, 10);
    input1DateArea = input1DateArea.substring(0, 2) + "/" + input1DateArea.substring(2, 4) + "/" + input1DateArea.substring(4, 8);
    input1DateArea = input1DateArea.substring(0, 10);
    input1DateArea = input1DateArea.substring(0, 2) + "/" + input1DateArea.substring(2, 4) + "/" + input1DateArea.substring(4, 8);
    input1DateArea = input1DateArea.substring(0, 10);
    input1DateArea = input1DateArea.substring(0, 2) + "/" + input1DateArea.substring(2, 4) + "/" + input1DateArea.substring(4, 8);
    input1DateArea = input1DateArea.substring(0, 10);
    input1DateArea = input1DateArea.substring(0, 2) + "/" + input1DateArea.substring(2, 4) + "/" + input1DateArea.substring(4, 8);
    input1DateArea = input1DateArea.substring(0, 10);
    input1DateArea = input1DateArea.substring(0, 2) + "/" + input1DateArea.substring(2, 4) + "/" + input1DateArea.substring(4, 8);
    input1DateArea = input1DateArea.substring(0, 10);
    input1DateArea = input1DateArea.substring(0, 2) + "/" + input1DateArea.substring(2, 4) + "/" + input1DateArea.substring(4, 8);
    input1DateArea = input1DateArea.substring(0, 10);
    input1DateArea = input1DateArea.substring(0, 2) + "/" + input1DateArea.substring(2, 4) + "/" + input1DateArea.substring(4, 8);
    input1DateArea = input1DateArea.substring(0, 10);
    input1DateArea = input1DateArea.substring(0, 2) + "/" + input1DateArea.substring(2, 4) + "/" + input1DateArea.substring(4, 8);
    input1DateArea = input1DateArea.substring(0, 10);
    input1DateArea = input1DateArea.substring(0, 2) + "/" + input1DateArea.substring(2, 4) + "/" + input1DateArea.substring(4, 8);
    input1DateArea = input1DateArea.substring(0, 10);
    input1DateArea = input1DateArea.substring(0, 2) + "/" + input1DateArea.substring(2, 4) + "/" + input1DateArea.substring(4, 8);
    input1DateArea = input1DateArea.substring(0, 10);
    input1DateArea = input1DateArea.substring(0, 2) + "/" + input1DateArea.substring(2, 4) + "/" + input1DateArea.substring(4, 8);
    input1DateArea = input1DateArea.substring(0, 10);
    input1DateArea = input1DateArea.substring(0, 2) + "/" + input1DateArea.substring(2, 4) + "/" + input1DateArea.substring(4, 8);
    input1DateArea = input1DateArea.substring(0, 10);
    input1DateArea = input1DateArea.substring(0, 2) + "/" + input1DateArea.substring(2, 4) + "/" + input1DateArea.substring(4, 8);
    input1DateArea = input1DateArea.substring(0, 10);
    input1DateArea = input1DateArea.substring(0, 2) + "/" + input1DateArea.substring(2, 4) + "/" + input1DateArea.substring(4, 8);
    input1DateArea = input1DateArea.substring(0, 10);
    input1DateArea = input1DateArea.substring(0, 2) + "/" + input1DateArea.substring(2, 4) + "/" + input1DateArea.substring(4, 8);
    input1DateArea = input1DateArea.substring(0, 10);
    input1DateArea = input1DateArea.substring(0, 2) + "/" + input1DateArea.substring(2, 4) + "/" + input1DateArea.substring(4, 8);
    input1DateArea = input1DateArea.substring(0, 10);
    input1DateArea = input1DateArea.substring(0, 2) + "/" + input1DateArea.substring(2, 4) + "/" + input1DateArea.substring(4, 8);
    input1DateArea = input1DateArea.substring(0, 10);
    input1DateArea = input1DateArea.substring(0, 2) + "/" + input1DateArea.substring(2, 4) + "/" + input1DateArea.substring(4, 8);
    input1DateArea = input1DateArea.substring(0, 10);
    input1DateArea = input1DateArea.substring(0, 2) + "/" + input1DateArea.substring(2, 4) + "/" + input1DateArea.substring(4, 8);
    input1DateArea = input1DateArea.substring(0, 10);
    input1DateArea = input1DateArea.substring(0, 2) + "/" + input1DateArea.substring(2, 4) + "/" + input1DateArea.substring(4, 8);
    input1DateArea = input1DateArea.substring(0, 10);
    input1DateArea = input1DateArea.substring(0, 2) + "/" + input1DateArea.substring(2, 4) + "/" + input1DateArea.substring(4, 8);

}

    public void z970SetDaStatusBatch() {
        AbnormalTerminationArea abnormalTerminationArea = new AbnormalTerminationArea();
        String daDbmerrorLit = "DBM";
        String daDuplicateLit = "DUP";
        String daEndfileLit = "EOF";
        String daLogicerrLit = "LOG";
        String daNotavailLit = "NAV";
        String daNotfoundLit = "NFD";
        String daOkLit = "OK ";
        String daSecurityLit = "SEC";
        String daStatusFile = "";
        DataAccessStatus dataAccessStatus = new DataAccessStatus();
        String datasetDupkey = "02";
        String datasetDuprec = "22";
        String datasetEndfile = "10";
        String datasetIllogic = "92";
        String datasetInvreq = "91";
        String datasetNotfnd = "23";
        String datasetNotopen = "95";
        String datasetOk = "00";
        String datasetVerified = "97";
        daStatusFile = ((Filler19) (abnormalTerminationArea.getAbtDataAccessInfo())).getAbtBatchStatus();
        if (daStatusFile.equals(datasetOk) || daStatusFile.equals(datasetVerified)) {
            dataAccessStatus.setDaStatus(daOkLit);
        } else if (daStatusFile.equals(datasetNotfnd)) {
            dataAccessStatus.setDaStatus(daNotfoundLit);
        } else if (daStatusFile.equals(datasetEndfile)) {
            dataAccessStatus.setDaStatus(daEndfileLit);
        } else if (daStatusFile.equals(datasetDuprec) || daStatusFile.equals(datasetDupkey)) {
            dataAccessStatus.setDaStatus(daDuplicateLit);
        } else if (daStatusFile.equals(datasetIllogic)) {
            dataAccessStatus.setDaStatus(daLogicerrLit);
        } else if (daStatusFile.equals(datasetInvreq)) {
            dataAccessStatus.setDaStatus(daSecurityLit);
        } else if (daStatusFile.equals(datasetNotopen)) {
            dataAccessStatus.setDaStatus(daNotavailLit);
        } else {
            dataAccessStatus.setDaStatus(daDbmerrorLit);
        }
    }

    public void processKeyChange3000(AbnormalTerminationArea abnormalTerminationArea, WsCountFields wsCountFields,
            WsCurrentCpuKey wsCurrentCpuKey, WsFields wsFields, WsFileStatus wsFileStatus, WsFlags wsFlags,
            WsPreviousCpuKey wsPreviousCpuKey, WsSaveArea wsSaveArea, WsWorkFields wsWorkFields) {
        int wsSaveDistNo = 0;
        int wsSaveMfgNo = 0;
        if (wsFlags.isEmptyCpuFile()) {
            return;
        }
        if (wsFlags.isTrailerRecNotRead() && wsFlags.isDetailRecRead()) {
            updateNoTrailer7700(abnormalTerminationArea, wsCountFields, wsFields, wsFileStatus, wsSaveArea,
                    wsWorkFields);
        }
        if (wsFlags.isDetailRecNotRead()) {
            dtlNotReadProc3500(abnormalTerminationArea, wsCountFields, wsFields, wsFileStatus, wsFlags,
                    wsPreviousCpuKey,
                    wsSaveArea, wsWorkFields);
        }
        wsSaveArea.setWsSaveArea(new byte[100]);
        wsWorkFields.setWsDetailRecCnt(0);
        wsWorkFields.setWsTrailerRecCnt(0);
        wsFields.setWsFileSeqNo(wsFields.getWsFileSeqNo() + 1);
        wsFlags.setHeaderRecNotRead();
        wsFlags.setDetailRecNotRead();
        wsFlags.setTrailerRecNotRead();
        wsPreviousCpuKey.setBytes(wsCurrentCpuKey.getBytes());
    }

    public void mainlineProcessing0000() {
        AbnormalTerminationArea abnormalTerminationArea = new AbnormalTerminationArea();
        CpuRegdtlLayout cpuRegdtlLayout = new CpuRegdtlLayout();
        WsCountFields wsCountFields = new WsCountFields();
        WsCurrentCpuKey wsCurrentCpuKey = new WsCurrentCpuKey();
        WsDlrNoChar wsDlrNoChar = new WsDlrNoChar();
        WsFileStatus wsFileStatus = new WsFileStatus();
        WsFlags wsFlags = new WsFlags();
        WsPreviousCpuKey wsPreviousCpuKey = new WsPreviousCpuKey();
        WsSaveArea wsSaveArea = new WsSaveArea();
        WsWorkFields wsWorkFields = new WsWorkFields();
        System.out.println("MAIN PARA");
        initialization1000(abnormalTerminationArea, cpuRegdtlLayout, wsCountFields, wsCurrentCpuKey, wsDlrNoChar,
                wsFlags,
                wsPreviousCpuKey, wsWorkFields);
        while (!wsFlags.isWsEndOfFile()) {
            processCpuFile2000(abnormalTerminationArea, cpuRegdtlLayout, wsCountFields, wsCurrentCpuKey, wsDlrNoChar,
                    wsFileStatus, wsFlags, wsPreviousCpuKey, wsSaveArea, wsWorkFields);
        }
        processKeyChange3000(abnormalTerminationArea, wsCountFields, wsCurrentCpuKey, wsFields, wsFileStatus, wsFlags,
                wsPreviousCpuKey, wsSaveArea, wsWorkFields);
        end9000(wsCountFields);
    }

    public void initialization1000(AbnormalTerminationArea abnormalTerminationArea, CpuRegdtlLayout cpuRegdtlLayout,
            WsCountFields wsCountFields, WsCurrentCpuKey wsCurrentCpuKey, WsDlrNoChar wsDlrNoChar, WsFlags wsFlags,
            WsPreviousCpuKey wsPreviousCpuKey, WsWorkFields wsWorkFields) {
        int wsDealerNo;
        abnormalTerminationArea.getAbtControlInfo().setAbtPgmName(wsWorkFields.getWsPgmName());
        System.out.println("***********************************");
        System.out.println("********  MXBPA181 BEGINS  ********");
        System.out.println("***********************************");
        System.out.println(" ");
        wsCountFields.setWsCountDel(0);
        wsCountFields.setWsCountIns(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountDel(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd(0);
        wsCountFields.setWsCountUpd();
    }

    public void validateCreatTime4300(HRegHdrRec hRegHdrRec, WsFileStatus wsFileStatus) {
        WsWorkFields wsWorkFields = new WsWorkFields();
        wsWorkFields.getWsTime().setBytes(hRegHdrRec.getHCreatTime());
        if (((wsWorkFields.getWsTime().getWsHour().compareTo("0") < 0
                || wsWorkFields.getWsTime().getWsHour().compareTo("24") >= 0)
                || (wsWorkFields.getWsTime().getWsMin().compareTo("0") < 0
                        || wsWorkFields.getWsTime().getWsMin().compareTo("60") >= 0))
                || (wsWorkFields.getWsTime().getWsTmSep() != ':')) {
            wsFileStatus.setCreatTimeNotValid();
        }
    }

    public void insertHeaderFile7500(AbnormalTerminationArea abnormalTerminationArea, WsCountFields wsCountFields,
            WsFields wsFields, WsFileStatus wsFileStatus, WsSaveArea wsSaveArea, WsWorkFields wsWorkFields) {
        String abtDaFunction = "";
        Dclvwmbhrg dclvwmbhrg = new Dclvwmbhrg();
        if (wsFileStatus.getWsFileStatus().equals("")) {
            wsFileStatus.setTrailerRecMissing();
        }
        dclvwmbhrg.setRegComplDt(wsWorkFields.getWsProcDate());
        dclvwmbhrg.setProcDate(wsWorkFields.getWsProcDate());
        dclvwmbhrg.setFileSeqNo(wsFields.getWsFileSeqNo());
        dclvwmbhrg.setFileRejDs(wsFileStatus.getWsFileStatus());
        dclvwmbhrg.setItemSeqNo(wsFields.getWsItemSeqNo());
        ((WsMfgNoChar) (wsSaveArea)).setWsSaveMfgNoLast6(((WsMfgNoChar) (wsSaveArea)).getWsSaveMfgNoLast6());
        ((WsDistNoChar) (wsSaveArea)).setWsSaveDistNoLast6(((WsDistNoChar) (wsSaveArea)).getWsSaveDistNoLast6());
        dclvwmbhrg.setInputMfgNo(((WsMfgNoChar) (wsSaveArea)).getWsSaveMfgNoLast6());
        dclvwmbhrg.setInputDistNo(((WsDistNoChar) (wsSaveArea)).getWsSaveDistNoLast6());
        dclvwmbhrg.setInputCpuId(wsSaveArea.getWsSaveCdfCustId());
        dclvwmbhrg.setInputCpuCode(wsSaveArea.getWsSaveCdfCustCode());
        dclvwmbhrg.setInputCreateDate(wsSaveArea.getWsSaveCreatDate());
        dclvwmbhrg.setInputCreateTime(wsSaveArea.getWsSaveCreatTime());
        dclvwmbhrg.setInputMfgName(wsSaveArea.getWsSaveMfgName());
        dclvwmbhrg.setInputRecCnt("000000");
        sqlInsertBhrg8000(dclvwmbhrg);
        Connection connection = DriverManager.getConnection("jdbc:example_database_url", "example_username", "example_password");
        PreparedStatement ps = connection.prepareStatement(sql);
        switch (connection.getWarnings().getErrorCode()) {
            case 0:
                wsCountFields.setWsTotBhrgInsCnt(wsCountFields.getWsTotBhrgInsCnt() + 1);
                break;
            default:
                System.out.println("PROC-DATE  :" + dclvwmbhrg.getProcDate());
                System.out.println("FILE-SEQ-NO:" + dclvwmbhrg.getFileSeqNo());
                System.out.println("FILE-REJ-DS:" + dclvwmbhrg.getFileRejDs());
                System.out.println("ITEM-SEQ-NO:" + dclvwmbhrg.getItemSeqNo());
                System.out.println("INP-MFG-NO :" + dclvwmbhrg.getInputMfgNo());
                System.out.println("INP-DIST-NO:" + dclvwmbhrg.getInputDistNo());
                System.out.println("CPU-ID     :" + dclvwmbhrg.getInputCpuId());
                System.out.println("CPU-CODE   :" + dclvwmbhrg.getInputCpuCode());
                System.out.println("CREATE-TIME:" + dclvwmbhrg.getInputCreateTime());
                System.out.println("CREATE-DATE:" + dclvwmbhrg.getInputCreateDate());
                abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
                abtDaFunction = "INSERT";
                abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorActivity("DB2");
                abnormalTerminationArea.getAbtDataAccessInfo().setAbtDaAccessName("VWMBHRG");
                abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorSection("7500-");
                z980AbnormalTerm(abnormalTerminationArea);
        }
    }

    public void getCurrentDate1100(AbnormalTerminationArea abnormalTerminationArea, WsWorkFields wsWorkFields) {
        String abtDaFunction = "";
        try {
            String sql = "SELECT PROC_DATE FROM VWMCTUPD WHERE (SUBSYSTEM_ID_IND = ' ' AND   SUBFUNCTION_CODE = '        ') WITH UR";
            // PreparedStatement ps = JdbcConnection.connection.prepareStatement(sql);
            Connection connection = DriverManager.getConnection("jdbc:example_database_url", "example_username", "example_password");
            PreparedStatement ps = connection.prepareStatement(sql);
            ResultSet rs = ps.executeQuery();
            rs.next();
            wsWorkFields.setWsProcDate(rs.getString(1));
            ps.close();
        } catch (SQLException exception) {
            abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
            ((Filler15) (abnormalTerminationArea.getAbtDataAccessInfo())).setAbtDb2Status(exception.getErrorCode());
            abtDaFunction = "SELECT";
            abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorActivity("DB2");
            abnormalTerminationArea.getAbtDataAccessInfo().setAbtDaAccessName("VWMCTUPD");
            abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorSection("1100-");
            z980AbnormalTerm(abnormalTerminationArea);
        }
    }

    public void moveKeyFields1400(CpuRegdtlLayout cpuRegdtlLayout, WsCurrentCpuKey wsCurrentCpuKey) {
        // Convert to CpuRegdtlData to access the specific fields
        CpuRegdtlData cpuRegdtlData = new CpuRegdtlData();
        cpuRegdtlData.setBytes(cpuRegdtlLayout.getBytes());

        wsCurrentCpuKey.setWsCurrentCdfCustId(cpuRegdtlData.getCpuRegdtlCdfCustId());
        wsCurrentCpuKey.setWsCurrentCdfCustCode(cpuRegdtlData.getCpuRegdtlCdfCustCode());
        wsCurrentCpuKey.setWsCurrentCreatDate(cpuRegdtlData.getCpuRegdtlCreatDate());
        wsCurrentCpuKey.setWsCurrentCreatTime(cpuRegdtlData.getCpuRegdtlCreatTime());
    }

    public void insertDetailRecs7000(AbnormalTerminationArea abnormalTerminationArea, DRegDtlRec dRegDtlRec,
            WsCountFields wsCountFields, WsDlrNoChar wsDlrNoChar, WsFileStatus wsFileStatus, WsItemStatus wsItemStatus,
            WsSaveArea wsSaveArea, WsWorkFields wsWorkFields) {
        String abtDaFunction = "";
        Dclvwmbhrg dclvwmbhrg = new Dclvwmbhrg();
        dclvwmbhrg.setProcDate(wsWorkFields.getWsProcDate());
        dclvwmbhrg.setFileSeqNo(wsDlrNoChar.getWsFileSeqNo());
        dclvwmbhrg.setFileRejDs(wsFileStatus.getWsFileStatus());
        dclvwmbhrg.setItemSeqNo(wsDlrNoChar.getWsItemSeqNo());
        dclvwmbhrg.setItemRejDs(wsItemStatus.getWsItemStatus());
        dclvwmbhrg.setInputMfgNo(((WsMfgNoChar) (wsSaveArea)).getWsSaveMfgNoLast6());
        dclvwmbhrg.setInputDistNo(((WsDistNoChar) (wsSaveArea)).getWsSaveDistNoLast6());
        dclvwmbhrg.setInputDlrNo(wsDlrNoChar.getWsDlrNoLast6());
        dclvwmbhrg.setInputCpuId(dRegDtlRec.getDCdfCustId());
        dclvwmbhrg.setInputCpuCode(dRegDtlRec.getDCdfCustCode());
        dclvwmbhrg.setInputCreateTime(dRegDtlRec.getDCreatTime());
        dclvwmbhrg.setInputCreateDate(dRegDtlRec.getDCreatDate());
        dclvwmbhrg.setInputMfgName(wsSaveArea.getWsSaveMfgName());
        dclvwmbhrg.setInputRecCnt("000000");
        dclvwmbhrg.setCpuDlrNo(dRegDtlRec.getDVendDlrNo());
        dclvwmbhrg.setCustName(dRegDtlRec.getDVendDlrName());
        dclvwmbhrg.setModelNo(dRegDtlRec.getDModelNbr());
        dclvwmbhrg.setModelDs(dRegDtlRec.getDModelDesc());
        dclvwmbhrg.setSerialNo(dRegDtlRec.getDSerialNbr());
        if (wsWorkFields.getWsRegComplDate().equals("        ")) {
            dclvwmbhrg.setRegComplDt(wsWorkFields.getWsProcDate());
        } else {
            dclvwmbhrg.setRegComplDt(wsWorkFields.getWsRegComplDate());
        }
        dclvwmbhrg.setRegTypeCd(dRegDtlRec.getDRegType());
        try {
            String sql = "INSERT INTO DCLVWMBHRG(PROC_DATE, FILE_SEQ_NO, FILE_REJ_DS, ITEM_SEQ_NO, ITEM_REJ_DS, INPUT_MFG_NO, INPUT_DIST_NO, INPUT_DLR_NO, INPUT_CPU_ID, INPUT_CPU_CODE, INPUT_CREATE_TIME, INPUT_CREATE_DATE, INPUT_MFG_NAME, INPUT_REC_CNT, CPU_DLR_NO, CUST_NAME, MODEL_NO, MODEL_DS, SERIAL_NO, REG_COMPL_DT, REG_TYPE_CD)values(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            // PreparedStatement ps = JdbcConnection.connection.prepareStatement(sql);
            Connection connection = DriverManager.getConnection("jdbc:example_database_url", "example_username", "example_password");
            PreparedStatement ps = connection.prepareStatement(sql);
            ps.setString(1, dclvwmbhrg.getProcDate());
            ps.setInt(2, dclvwmbhrg.getFileSeqNo());
            ps.setString(3, dclvwmbhrg.getFileRejDs());
            ps.setInt(4, dclvwmbhrg.getItemSeqNo());
            ps.setString(5, dclvwmbhrg.getItemRejDs());
            ps.setString(6, dclvwmbhrg.getInputMfgNo());
            ps.setString(7, dclvwmbhrg.getInputDistNo());
            ps.setString(8, dclvwmbhrg.getInputDlrNo());
            ps.setString(9, dclvwmbhrg.getInputCpuId());
            ps.setString(10, dclvwmbhrg.getInputCpuCode());
            ps.setString(11, dclvwmbhrg.getInputCreateTime());
            ps.setString(12, dclvwmbhrg.getInputCreateDate());
            ps.setString(13, dclvwmbhrg.getInputMfgName());
            ps.setString(14, dclvwmbhrg.getInputRecCnt());
            ps.setString(15, dclvwmbhrg.getCpuDlrNo());
            ps.setString(16, dclvwmbhrg.getCustName());
            ps.setString(17, dclvwmbhrg.getModelNo());
            ps.setString(18, dclvwmbhrg.getModelDs());
            ps.setString(19, dclvwmbhrg.getSerialNo());
            ps.setString(20, dclvwmbhrg.getRegComplDt());
            ps.setString(21, dclvwmbhrg.getRegTypeCd());
            ps.executeUpdate();
            ps.close();
            wsCountFields.setWsTotBhrgInsCnt(wsCountFields.getWsTotBhrgInsCnt() + 1);
        } catch (SQLException exception) {
            System.out.println("PROC-DATE  :" + dclvwmbhrg.getProcDate());
            System.out.println("FILE-SEQ-NO:" + dclvwmbhrg.getFileSeqNo());
            System.out.println("FILE-REJ-DS:" + dclvwmbhrg.getFileRejDs());
            System.out.println("ITEM-SEQ-NO:" + dclvwmbhrg.getItemSeqNo());
            System.out.println("ITEM-REJ-DS:" + dclvwmbhrg.getItemRejDs());
            System.out.println("INP-MFG-NO :" + dclvwmbhrg.getInputMfgNo());
            System.out.println("INP-DIST-NO:" + dclvwmbhrg.getInputDistNo());
            System.out.println("INP-DLR-NO :" + dclvwmbhrg.getInputDlrNo());
            System.out.println("CPU-ID     :" + dclvwmbhrg.getInputCpuId());
            System.out.println("CPU-CODE   :" + dclvwmbhrg.getInputCpuCode());
            System.out.println("CREATE-TIME:" + dclvwmbhrg.getInputCreateTime());
            System.out.println("CREATE-DATE:" + dclvwmbhrg.getInputCreateDate());
            abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
            abtDaFunction = "INSERT";
            abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorActivity("DB2");
            abnormalTerminationArea.getAbtDataAccessInfo().setAbtDaAccessName("VWMBHRG");
            abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorSection("7000-");
            z980AbnormalTerm(abnormalTerminationArea);
        }
    }

    public void sqlUpdateBhrg8100(Dclvwmbhrg dclvwmbhrg) {
        try {
            String sql = "UPDATE VWMBHRG SET FILE_REJ_DS = ?, INPUT_REC_CNT = ? WHERE PROC_DATE = ? AND FILE_SEQ_NO = ? AND INPUT_CPU_ID = ? AND INPUT_CPU_CODE = ? AND INPUT_CREATE_DATE = ? AND INPUT_CREATE_TIME = ?";
            // PreparedStatement ps = JdbcConnection.connection.prepareStatement(sql);
            Connection connection = DriverManager.getConnection("jdbc:example_database_url", "example_username", "example_password");
            PreparedStatement ps = connection.prepareStatement(sql);
            ps.setString(1, dclvwmbhrg.getFileRejDs());
            ps.setString(2, dclvwmbhrg.getInputRecCnt());
            ps.setString(3, dclvwmbhrg.getProcDate());
            ps.setInt(4, dclvwmbhrg.getFileSeqNo());
            ps.setString(5, dclvwmbhrg.getInputCpuId());
            ps.setString(6, dclvwmbhrg.getInputCpuCode());
            ps.setString(7, dclvwmbhrg.getInputCreateDate());
            ps.setString(8, dclvwmbhrg.getInputCreateTime());
            ps.executeUpdate();
            ps.close();
        } catch (SQLException exception) {
            System.out.println(exception);
            return;
        }
    }

    public void insertInvalidFile7300(AbnormalTerminationArea abnormalTerminationArea, CpuRegdtlData cpuRegdtlData,
            WsCountFields wsCountFields, WsFields wsFields, WsFileStatus wsFileStatus, WsItemStatus wsItemStatus,
            WsSaveArea wsSaveArea, WsWorkFields wsWorkFields) {
        String abtDaFunction = "";
        Dclvwmbhrg dclvwmbhrg = new Dclvwmbhrg();
        dclvwmbhrg.setRegComplDt(wsWorkFields.getWsProcDate());
        dclvwmbhrg.setProcDate(wsWorkFields.getWsProcDate());
        dclvwmbhrg.setFileSeqNo(wsFields.getWsFileSeqNo());
        dclvwmbhrg.setFileRejDs(wsFileStatus.getWsFileStatus());
        dclvwmbhrg.setItemSeqNo(wsFields.getWsItemSeqNo());
        dclvwmbhrg.setItemRejDs(wsItemStatus.getWsItemStatus());
        ((WsMfgNoChar) (wsSaveArea)).setWsSaveMfgNoLast6(((WsMfgNoChar) (wsSaveArea)).getWsSaveMfgNoLast6());
        ((WsDistNoChar) (wsSaveArea)).setWsSaveDistNoLast6(((WsDistNoChar) (wsSaveArea)).getWsSaveDistNoLast6());
        dclvwmbhrg.setInputCpuId(cpuRegdtlData.getCpuRegdtlCdfCustId());
        dclvwmbhrg.setInputCpuCode(cpuRegdtlData.getCpuRegdtlCdfCustCode());
        dclvwmbhrg.setInputCreateTime(cpuRegdtlData.getCpuRegdtlCreatTime());
        dclvwmbhrg.setInputCreateDate(cpuRegdtlData.getCpuRegdtlCreatDate());
        dclvwmbhrg.setInputMfgName(wsSaveArea.getWsSaveMfgName());
        dclvwmbhrg.setInputRecCnt("000000");
        try {
            String sql = "INSERT INTO DCLVWMBHRG(REG_COMPL_DT, PROC_DATE, FILE_SEQ_NO, FILE_REJ_DS, ITEM_SEQ_NO, ITEM_REJ_DS, INPUT_MFG_NO, INPUT_DIST_NO, INPUT_CPU_ID, INPUT_CPU_CODE, INPUT_CREATE_TIME, INPUT_CREATE_DATE, INPUT_MFG_NAME, INPUT_REC_CNT)values(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            // PreparedStatement ps = JdbcConnection.connection.prepareStatement(sql);
            Connection connection = DriverManager.getConnection("jdbc:example_database_url", "example_username", "example_password");
            PreparedStatement ps = connection.prepareStatement(sql);
            ps.setString(1, dclvwmbhrg.getRegComplDt());
            ps.setString(2, dclvwmbhrg.getProcDate());
            ps.setInt(3, dclvwmbhrg.getFileSeqNo());
            ps.setString(4, dclvwmbhrg.getFileRejDs());
            ps.setInt(5, dclvwmbhrg.getItemSeqNo());
            ps.setString(6, dclvwmbhrg.getItemRejDs());
            ps.setString(7, dclvwmbhrg.getInputMfgNo());
            ps.setString(8, dclvwmbhrg.getInputDistNo());
            ps.setString(9, dclvwmbhrg.getInputCpuId());
            ps.setString(10, dclvwmbhrg.getInputCpuCode());
            ps.setString(11, dclvwmbhrg.getInputCreateTime());
            ps.setString(12, dclvwmbhrg.getInputCreateDate());
            ps.setString(13, dclvwmbhrg.getInputMfgName());
            ps.setString(14, dclvwmbhrg.getInputRecCnt());
            ps.executeUpdate();
            ps.close();
            wsCountFields.setWsTotBhrgInsCnt(wsCountFields.getWsTotBhrgInsCnt() + 1);
        } catch (SQLException exception) {
            System.out.println("PROC-DATE  :" + dclvwmbhrg.getProcDate());
            System.out.println("FILE-SEQ-NO:" + dclvwmbhrg.getFileSeqNo());
            System.out.println("FILE-REJ-DS:" + dclvwmbhrg.getFileRejDs());
            System.out.println("ITEM-SEQ-NO:" + dclvwmbhrg.getItemSeqNo());
            System.out.println("ITEM-REJ-DS:" + dclvwmbhrg.getItemRejDs());
            System.out.println("INP-MFG-NO :" + dclvwmbhrg.getInputMfgNo());
            System.out.println("INP-DIST-NO:" + dclvwmbhrg.getInputDistNo());
            System.out.println("INP-DLR-NO :" + dclvwmbhrg.getInputDlrNo());
            System.out.println("CPU-ID     :" + dclvwmbhrg.getInputCpuId());
            System.out.println("CPU-CODE   :" + dclvwmbhrg.getInputCpuCode());
            System.out.println("CREATE-TIME:" + dclvwmbhrg.getInputCreateTime());
            System.out.println("CREATE-DATE:" + dclvwmbhrg.getInputCreateDate());
            abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
            abtDaFunction = "INSERT";
            abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorActivity("DB2");
            abnormalTerminationArea.getAbtDataAccessInfo().setAbtDaAccessName("VWMBHRG");
            abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorSection("7300-");
            z980AbnormalTerm(abnormalTerminationArea);
        }
    }

    public void readInputFile1300(AbnormalTerminationArea abnormalTerminationArea, CpuRegdtlLayout cpuRegdtlLayout,
            WsCountFields wsCountFields, WsFlags wsFlags) {
        String abtDaFunction = "";
        WsWorkFields wsWorkFields = new WsWorkFields();
        try {
            int jdeclNread = cpuFormatRecord.readFrom(Rxbpasvc.getInstance().getCpuFormatFile());
            if (jdeclNread == -1) {
                wsFlags.setWsEndOfFile();
            } else {
                wsCountFields.setWsTotalRecsRead(wsCountFields.getWsTotalRecsRead() + 1);
            }
        } catch (ZFileException e) {
            abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
            abnormalTerminationArea.getAbtPgmErrorData().setAbtBatchStatus(wsWorkFields.getWsCpuFileStatus());
            abtDaFunction = "READ";
            abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorActivity("SEQ");
            abnormalTerminationArea.getAbtDataAccessInfo().setAbtDaAccessName("MXPA181I");
            abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorSection("1300-");
            z980AbnormalTerm(abnormalTerminationArea);
        }
    }

    public void openInputFile1200(AbnormalTerminationArea abnormalTerminationArea) {
        String abtDaFunction = "";
        WsWorkFields wsWorkFields = new WsWorkFields();
        try {
            cpuFormatFile.open(CpuFormatFile.OpenMode.READ);
        } catch (ZFileException exception) {
            abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
            abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorActivity("SEQ");
            abnormalTerminationArea.getAbtDataAccessInfo().setAbtDaAccessName("MXPA181I");
            abtDaFunction = "OPEN";
            abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorSection("1200-");
            z980AbnormalTerm(abnormalTerminationArea);
        }
    }

    public void validateRegDate5200(AbnormalTerminationArea abnormalTerminationArea, DRegDtlRec dRegDtlRec,
            DateParmArea dateParmArea, WsItemStatus wsItemStatus) {
        String abtDaFunction = "";
        String input1DateArea = "";
        LoadTableArea loadTableArea = new LoadTableArea();
        WsWorkFields wsWorkFields = new WsWorkFields();
        wsWorkFields.getWsCpuDate().setBytes(dRegDtlRec.getDRegComplDate());
        if (wsWorkFields.getWsCpuDate().getWsDtSep1() == '-' && wsWorkFields.getWsCpuDate().getWsDtSep2() == '-') {
            dateParmArea.setIIsoFormat();
        } else if (wsWorkFields.getWsCpuDate().getWsDtSep1() == '.'
                && wsWorkFields.getWsCpuDate().getWsDtSep2() == '.') {
            input1DateArea = input1DateArea.replaceAll("\\.", "-");
            dateParmArea.setIIsoFormat();
        } else {
            wsItemStatus.setRegCompDateInvalid();
            return;
        }
        dateParmArea.setDateToDate();
        input1DateArea = wsWorkFields.getWsCpuDate().getBytes();
        try {
            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            dateFormat.setLenient(false);
            Date date = dateFormat.parse(input1DateArea);
            input1DateArea = dateFormat.format(date);
        } catch (ParseException e) {
            wsItemStatus.setRegCompDateInvalid();
            return;
        } catch (NullPointerException e) {
            wsItemStatus.setRegCompDateInvalid();
            return;
        } catch (IllegalArgumentException e) {
            wsItemStatus.setRegCompDateInvalid();
            return;
        } catch (Exception e) {
            System.out.println("REGISTRATION DATE :" + dRegDtlRec.getDRegComplDate());
            abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
            abnormalTerminationArea.getAbtPgmErrorData()
                    .setAbtErrorAbendCode(((DateEditErrorAbcodeX) (dateParmArea)).getDateEditErrorAbcode());
            abtDaFunction = "DATE";
            abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorActivity("   ");
            abnormalTerminationArea.getAbtDataAccessInfo().setAbtDaAccessName("MXBPW003");
            abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorSection("5200-");
            z980AbnormalTerm(abnormalTerminationArea);
        }
    }

    public void validateCustIdCode4100(AbnormalTerminationArea abnormalTerminationArea, Dclvwmcpcd dclvwmcpcd,
            HRegHdrRec hRegHdrRec, WsFileStatus wsFileStatus) {
        String abtDaFunction = "";
        int wsSaveDistNo = 0;
        int wsSaveMfgNo = 0;
        try {
            String sql = "SELECT CPU_ID, CPU_CODE, DIST_NO, MFG_NO, CPU_STAT_CODE FROM VWMCPCD WHERE CPU_ID = ? AND CPU_CODE = ?";
            // PreparedStatement ps = JdbcConnection.connection.prepareStatement(sql);
            Connection connection = DriverManager.getConnection("jdbc:example_database_url", "example_username", "example_password");
            PreparedStatement ps = connection.prepareStatement(sql);
            ps.setString(1, hRegHdrRec.getHCdfCustId());
            ps.setString(2, hRegHdrRec.getHCdfCustCode());
            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                dclvwmcpcd.setCpuId(rs.getString(1));
                dclvwmcpcd.setCpuCode(rs.getString(2));
                dclvwmcpcd.setDistNo(rs.getInt(3));
                dclvwmcpcd.setMfgNo(rs.getInt(4));
                dclvwmcpcd.setCpuStatCode(rs.getString(5).charAt(0));
            } else {
                wsFileStatus.setCustIdCodeNotFnd();
                return;
            }
            ps.close();
        } catch (SQLException exception) {
            System.out.println("CPU-ID   :" + hRegHdrRec.getHCdfCustId());
            System.out.println("CPU-CODE :" + hRegHdrRec.getHCdfCustCode());
            abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
            ((Filler15) (abnormalTerminationArea.getAbtDataAccessInfo())).setAbtDb2Status(exception.getErrorCode());
            abtDaFunction = "SELECT";
            abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorActivity("DB2");
            abnormalTerminationArea.getAbtDataAccessInfo().setAbtDaAccessName("VWMCPCD");
            abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorSection("4100-");
            z980AbnormalTerm(abnormalTerminationArea);
        }
        wsSaveMfgNo = dclvwmcpcd.getMfgNo();
        wsSaveDistNo = dclvwmcpcd.getDistNo();
    }

    public void getMaxFileSeqno1500(AbnormalTerminationArea abnormalTerminationArea, WsWorkFields wsWorkFields) {
        String abtDaFunction = "";
        WsFields wsFields = new WsFields();
        try {
            String sql = "SELECT VALUE(MAX(FILE_SEQ_NO),0) FROM VWMBHRG WHERE PROC_DATE = ?";
            // PreparedStatement ps = JdbcConnection.connection.prepareStatement(sql);
            Connection connection = DriverManager.getConnection("jdbc:example_database_url", "example_username", "example_password");
            PreparedStatement ps = connection.prepareStatement(sql);
            ps.setString(1, wsWorkFields.getWsProcDate());
            ResultSet rs = ps.executeQuery();
            rs.next();
            wsFields.setWsFileSeqNo(rs.getInt(1));
            ps.close();
        } catch (SQLException exception) {
            abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
            ((Filler15) (abnormalTerminationArea.getAbtDataAccessInfo())).setAbtDb2Status(exception.getErrorCode());
            abtDaFunction = "SELECT";
            abnormalTerminationArea.getAbtDataAccessInfo().setAbtDaAccessName("VWMBHRG");
            abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorActivity("DB2");
            abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorSection("1500-");
            z980AbnormalTerm(abnormalTerminationArea);
        }
    }

    public void end9000(WsCountFields wsCountFields) {
        System.out.println(" ");
        System.out.println("***********************************");
        System.out.println("TOTAL CPU RECORDS READ :" + wsCountFields.getWsTotalRecsRead());
        System.out.println("TOTAL HEADER RECS READ :" + wsCountFields.getWsTotHdrRecCnt());
        System.out.println("TOTAL DETAIL RECS READ :" + wsCountFields.getWsTotDtlRecCnt());
        System.out.println("TOTAL TRAILER RECS READ:" + wsCountFields.getWsTotTrlRecCnt());
        System.out.println("TOTAL INVALID RECS READ:" + wsCountFields.getWsTotInvRecCnt());
        System.out.println("TOTAL RECORDS INSERTED :" + wsCountFields.getWsTotBhrgInsCnt());
        System.out.println("TOTAL RECORDS UPDATED  :" + wsCountFields.getWsTotBhrgUpdCnt());
        System.out.println("***********************************");
        System.out.println(" ");
        System.out.println("***********************************");
        System.out.println("**** MXBPA181 SUCCESSFULLY ENDS ***");
        System.out.println("***********************************");
    }

    public void insertHeaderTrailer7400(AbnormalTerminationArea abnormalTerminationArea, WsCountFields wsCountFields,
            WsFields wsFields, WsFileStatus wsFileStatus, WsSaveArea wsSaveArea, WsWorkFields wsWorkFields) {
        String abtDaFunction = "";
        Dclvwmbhrg dclvwmbhrg = new Dclvwmbhrg();
        if (wsFileStatus.getWsFileStatus().equals("")) {
            wsFileStatus.setEmptyFile();
        }
        dclvwmbhrg.setRegComplDt(wsWorkFields.getWsProcDate());
        dclvwmbhrg.setProcDate(wsWorkFields.getWsProcDate());
        dclvwmbhrg.setFileSeqNo(wsFields.getWsFileSeqNo());
        dclvwmbhrg.setFileRejDs(wsFileStatus.getWsFileStatus());
        dclvwmbhrg.setItemSeqNo(wsFields.getWsItemSeqNo());
        dclvwmbhrg.setInputMfgNo(((WsMfgNoChar) (wsSaveArea)).getWsSaveMfgNoLast6());
        dclvwmbhrg.setInputDistNo(((WsDistNoChar) (wsSaveArea)).getWsSaveDistNoLast6());
        dclvwmbhrg.setInputCpuId(wsSaveArea.getWsSaveCdfCustId());
        dclvwmbhrg.setInputCpuCode(wsSaveArea.getWsSaveCdfCustCode());
        dclvwmbhrg.setInputCreateDate(wsSaveArea.getWsSaveCreatDate());
        dclvwmbhrg.setInputCreateTime(wsSaveArea.getWsSaveCreatTime());
        dclvwmbhrg.setInputMfgName(wsSaveArea.getWsSaveMfgName());
        dclvwmbhrg.setInputRecCnt(String.format("%06d", wsWorkFields.getWsTrailerRecCnt()));
        sqlInsertBhrg8000(dclvwmbhrg);
        Connection connection = DriverManager.getConnection("jdbc:example_database_url", "example_username", "example_password");
        PreparedStatement ps = connection.prepareStatement(sql);
        switch (connection.getWarnings().getErrorCode()) {
            case 0:
                wsCountFields.setWsTotBhrgInsCnt(wsCountFields.getWsTotBhrgInsCnt() + 1);
                break;
            default:
                System.out.println("PROC-DATE  :" + dclvwmbhrg.getProcDate());
                System.out.println("FILE-SEQ-NO:" + dclvwmbhrg.getFileSeqNo());
                System.out.println("FILE-REJ-DS:" + dclvwmbhrg.getFileRejDs());
                System.out.println("ITEM-SEQ-NO:" + dclvwmbhrg.getItemSeqNo());
                System.out.println("INP-MFG-NO :" + dclvwmbhrg.getInputMfgNo());
                System.out.println("INP-DIST-NO:" + dclvwmbhrg.getInputDistNo());
                System.out.println("CPU-ID     :" + dclvwmbhrg.getInputCpuId());
                System.out.println("CPU-CODE   :" + dclvwmbhrg.getInputCpuCode());
                System.out.println("CREATE-TIME:" + dclvwmbhrg.getInputCreateTime());
                System.out.println("CREATE-DATE:" + dclvwmbhrg.getInputCreateDate());
                abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
                abtDaFunction = "INSERT";
                abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorActivity("DB2");
                abnormalTerminationArea.getAbtDataAccessInfo().setAbtDaAccessName("VWMBHRG");
                abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorSection("7400-");
                z980AbnormalTerm(abnormalTerminationArea);
        }
    }

    public void updateDetailRecs7100(AbnormalTerminationArea abnormalTerminationArea, WsCountFields wsCountFields,
            WsFields wsFields, WsFileStatus wsFileStatus, WsWorkFields wsWorkFields, ZRegTrlRec zRegTrlRec) {
        String abtDaFunction = "";
        Dclvwmbhrg dclvwmbhrg = new Dclvwmbhrg();
        dclvwmbhrg.setRegComplDt(wsWorkFields.getWsProcDate());
        dclvwmbhrg.setProcDate(wsWorkFields.getWsProcDate());
        dclvwmbhrg.setFileSeqNo(wsFields.getWsFileSeqNo());
        dclvwmbhrg.setFileRejDs(wsFileStatus.getWsFileStatus());
        dclvwmbhrg.setInputCpuId(zRegTrlRec.getZCdfCustId());
        dclvwmbhrg.setInputCpuCode(zRegTrlRec.getZCdfCustCode());
        dclvwmbhrg.setInputCreateDate(zRegTrlRec.getZCreatDate());
        dclvwmbhrg.setInputCreateTime(zRegTrlRec.getZCreatTime());
        dclvwmbhrg.setInputRecCnt(zRegTrlRec.getZCustTrlCount());
        sqlUpdateBhrg8100(dclvwmbhrg);
        switch (JdbcConnection.sqlCode) {
            case 0:
                wsCountFields.setWsTotBhrgUpdCnt(wsCountFields.getWsTotBhrgUpdCnt() + 1);
                break;
            case 100:
                break;
            default:
                System.out.println("PROC-DATE  :" + dclvwmbhrg.getProcDate());
                System.out.println("FILE-SEQ-NO:" + dclvwmbhrg.getFileSeqNo());
                System.out.println("FILE-REJ-DS:" + dclvwmbhrg.getFileRejDs());
                System.out.println("CPU-ID     :" + dclvwmbhrg.getInputCpuId());
                System.out.println("CPU-CODE   :" + dclvwmbhrg.getInputCpuCode());
                System.out.println("CREATE-TIME:" + dclvwmbhrg.getInputCreateTime());
                System.out.println("CREATE-DATE:" + dclvwmbhrg.getInputCreateDate());
                System.out.println("INP-REC-CNT:" + dclvwmbhrg.getInputRecCnt());
                abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
                abtDaFunction = "UPDATE";
                abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorActivity("DB2");
                abnormalTerminationArea.getAbtDataAccessInfo().setAbtDaAccessName("VWMBHRG");
                abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorSection("7100-");
                z980AbnormalTerm(abnormalTerminationArea);
        }
    }

    public void saveHeaderFields4400(HRegHdrRec hRegHdrRec) {
        WsSaveArea wsSaveArea = new WsSaveArea();
        wsSaveArea.setWsSaveCdfCustId(hRegHdrRec.getHCdfCustId());
        wsSaveArea.setWsSaveCdfCustCode(hRegHdrRec.getHCdfCustCode());
        wsSaveArea.setWsSaveCreatDate(hRegHdrRec.getHCreatDate());
        wsSaveArea.setWsSaveCreatTime(hRegHdrRec.getHCreatTime());
        wsSaveArea.setWsSaveMfgName(hRegHdrRec.getHMfgName());
    }

    public void z980AbnormalTerm(AbnormalTerminationArea abnormalTerminationArea) {
        // TODO: generate z980AbnormalTerm() method with IBM watsonx Code Assistant for
        // Z
    }

    public void exitPara() {
        System.exit(0);
    }

    public static void main(String[] args) {
        getInstance().mainlineProcessing0000();
    }
}
