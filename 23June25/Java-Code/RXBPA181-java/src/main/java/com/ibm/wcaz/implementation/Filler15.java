package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.BinaryAsIntField;
import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class Filler15 extends AbtDataAccessInfo {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private int abtDb2Status;
    
    /** Initialize fields to non-null default values */
    public Filler15() {}
    
    /** Initialize all fields to provided values */
    public Filler15(String abtU100Sub, String abtDaAccessName, String abtDaGenericStatus, int abtDb2Status) {
        super(abtU100Sub, abtDaAccessName, abtDaGenericStatus);
        this.abtDb2Status = abtDb2Status;
    }
    
    @Override
    public Filler15 clone() throws CloneNotSupportedException {
        Filler15 cloned = (Filler15) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code Filler15} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected Filler15(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code Filler15} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected Filler15(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code Filler15} object
     * @see #setBytes(byte[], int)
     */
    public static Filler15 fromBytes(byte[] bytes, int offset) {
        return new Filler15(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code Filler15} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static Filler15 fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code Filler15} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static Filler15 fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public int getAbtDb2Status() {
        return this.abtDb2Status;
    }
    
    public void setAbtDb2Status(int abtDb2Status) {
        this.abtDb2Status = abtDb2Status;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        abtDb2Status = 0;
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ abtDb2Status=\"");
        s.append(getAbtDb2Status());
        s.append("\"");
        s.append(", filler16=\"");
        s.append(new String(filler16, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(Filler15 that) {
        return super.equals(that) &&
            this.abtDb2Status == that.abtDb2Status &&
            Arrays.equals(this.filler16, that.filler16);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof Filler15 other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof Filler15;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Integer.hashCode(abtDb2Status);
        result = 31 * result + Arrays.hashCode(filler16);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(Filler15 that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = Integer.compare(this.abtDb2Status, that.abtDb2Status);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler16, that.filler16);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(AbtDataAccessInfo that) {
        if (that instanceof Filler15 other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(AbtDataAccessInfo.SIZE);
    }
    
    private static final BinaryAsIntField ABT_DB_2_STATUS = factory.getBinaryAsIntField(9, true);
    private static final ByteArrayField FILLER_16 = factory.getByteArrayField(2);
    private byte[] filler16 = new byte[2];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link AbtDataAccessInfo#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "FILLER #15 record at MXWW03.CPY:98"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        ABT_DB_2_STATUS.putInt(abtDb2Status, bytes, offset);
        FILLER_16.putByteArray(filler16, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link AbtDataAccessInfo#setBytes(byte[], int)} to set parent-class state.
     * @see "FILLER #15 record at MXWW03.CPY:98"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        abtDb2Status = ABT_DB_2_STATUS.getInt(bytes, offset);
        FILLER_16.getByteArray(bytes, offset);
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
