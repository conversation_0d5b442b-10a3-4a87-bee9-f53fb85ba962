package com.ibm.wcaz.implementation;

import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

import com.ibm.jzos.fields.BinaryAsIntField;
import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;

public class AbtPgmErrorData implements Cloneable, Comparable<AbtPgmErrorData> {
    private static final Charset encoding = Charset.forName("IBM-1047");

    private static final String ABT_ERROR_IS_TP_IMS_VALUE = "IMS ";
    private static final String ABT_ERROR_IS_TP_CICS_VALUE = "CICS";
    private static final String ABT_ERROR_IS_TP_TSO_VALUE = "TSO ";
    private static final String ABT_ERROR_IS_SEQ_VALUE = "SEQ ";
    private static final String ABT_ERROR_IS_VSAM_VALUE = "VSAM";
    private static final String ABT_ERROR_IS_DLI_VALUE = "DLI ";
    private static final String ABT_ERROR_IS_EXECDLI_VALUE = "XDLI";
    private static final String ABT_ERROR_IS_DB_2_VALUE = "DB2 ";
    private static final String ABT_ERROR_IS_CQUEUE_VALUE = "CQUE";
    private static final String ABT_ERROR_IS_CJOURNAL_VALUE = "CJRL";

    private String abtErrorActivity = "";
    private int abtErrorAbendCode = 0;
    private String abtErrorSection = "";
    private String abtDaAccessName = "";
    private String abtDaFunction = "";
    private String abtBatchStatus = "";

    /** Initialize fields to non-null default values */
    public AbtPgmErrorData() {
        initFiller();
    }

    /** Initialize all fields to provided values */
    public AbtPgmErrorData(String abtErrorActivity, int abtErrorAbendCode) {
        this.abtErrorActivity = abtErrorActivity;
        this.abtErrorAbendCode = abtErrorAbendCode;
        initFiller();
    }

    @Override
    public AbtPgmErrorData clone() throws CloneNotSupportedException {
        AbtPgmErrorData cloned = (AbtPgmErrorData) super.clone();
        return cloned;
    }

    /**
     * Initialize {@code AbtPgmErrorData} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected AbtPgmErrorData(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }

    /**
     * Initialize {@code AbtPgmErrorData} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected AbtPgmErrorData(byte[] bytes) {
        this(bytes, 0);
    }

    /**
     * Deserialize the COBOL-formatted byte array into a new {@code AbtPgmErrorData} object
     * @see #setBytes(byte[], int)
     */
    public static AbtPgmErrorData fromBytes(byte[] bytes, int offset) {
        return new AbtPgmErrorData(bytes, offset);
    }

    /**
     * Deserialize the COBOL-formatted byte array into a new {@code AbtPgmErrorData} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static AbtPgmErrorData fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }

    /**
     * Deserialize the COBOL-formatted string into a new {@code AbtPgmErrorData} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static AbtPgmErrorData fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }

    public String getAbtErrorActivity() {
        return this.abtErrorActivity;
    }

    public void setAbtErrorActivity(String abtErrorActivity) {
        this.abtErrorActivity = abtErrorActivity;
    }

    public boolean isAbtErrorIsTpIms() {
        return abtErrorActivity.equals(ABT_ERROR_IS_TP_IMS_VALUE);
    }

    public void setAbtErrorIsTpIms() {
        abtErrorActivity = ABT_ERROR_IS_TP_IMS_VALUE;
    }

    public boolean isAbtErrorIsTpCics() {
        return abtErrorActivity.equals(ABT_ERROR_IS_TP_CICS_VALUE);
    }

    public void setAbtErrorIsTpCics() {
        abtErrorActivity = ABT_ERROR_IS_TP_CICS_VALUE;
    }

    public boolean isAbtErrorIsTpTso() {
        return abtErrorActivity.equals(ABT_ERROR_IS_TP_TSO_VALUE);
    }

    public void setAbtErrorIsTpTso() {
        abtErrorActivity = ABT_ERROR_IS_TP_TSO_VALUE;
    }

    public boolean isAbtErrorIsSeq() {
        return abtErrorActivity.equals(ABT_ERROR_IS_SEQ_VALUE);
    }

    public void setAbtErrorIsSeq() {
        abtErrorActivity = ABT_ERROR_IS_SEQ_VALUE;
    }

    public boolean isAbtErrorIsVsam() {
        return abtErrorActivity.equals(ABT_ERROR_IS_VSAM_VALUE);
    }

    public void setAbtErrorIsVsam() {
        abtErrorActivity = ABT_ERROR_IS_VSAM_VALUE;
    }

    public boolean isAbtErrorIsDli() {
        return abtErrorActivity.equals(ABT_ERROR_IS_DLI_VALUE);
    }

    public void setAbtErrorIsDli() {
        abtErrorActivity = ABT_ERROR_IS_DLI_VALUE;
    }

    public boolean isAbtErrorIsExecdli() {
        return abtErrorActivity.equals(ABT_ERROR_IS_EXECDLI_VALUE);
    }

    public void setAbtErrorIsExecdli() {
        abtErrorActivity = ABT_ERROR_IS_EXECDLI_VALUE;
    }

    public boolean isAbtErrorIsDb2() {
        return abtErrorActivity.equals(ABT_ERROR_IS_DB_2_VALUE);
    }

    public void setAbtErrorIsDb2() {
        abtErrorActivity = ABT_ERROR_IS_DB_2_VALUE;
    }

    public boolean isAbtErrorIsCqueue() {
        return abtErrorActivity.equals(ABT_ERROR_IS_CQUEUE_VALUE);
    }

    public void setAbtErrorIsCqueue() {
        abtErrorActivity = ABT_ERROR_IS_CQUEUE_VALUE;
    }

    public boolean isAbtErrorIsCjournal() {
        return abtErrorActivity.equals(ABT_ERROR_IS_CJOURNAL_VALUE);
    }

    public void setAbtErrorIsCjournal() {
        abtErrorActivity = ABT_ERROR_IS_CJOURNAL_VALUE;
    }

    public int getAbtErrorAbendCode() {
        return this.abtErrorAbendCode;
    }

    public void setAbtErrorAbendCode(int abtErrorAbendCode) {
        this.abtErrorAbendCode = abtErrorAbendCode;
    }

    public String getAbtErrorSection() {
        return this.abtErrorSection;
    }

    public void setAbtErrorSection(String abtErrorSection) {
        this.abtErrorSection = abtErrorSection;
    }

    public String getAbtDaAccessName() {
        return this.abtDaAccessName;
    }

    public void setAbtDaAccessName(String abtDaAccessName) {
        this.abtDaAccessName = abtDaAccessName;
    }

    public String getAbtDaFunction() {
        return this.abtDaFunction;
    }

    public void setAbtDaFunction(String abtDaFunction) {
        this.abtDaFunction = abtDaFunction;
    }

    public String getAbtBatchStatus() {
        return this.abtBatchStatus;
    }

    public void setAbtBatchStatus(String abtBatchStatus) {
        this.abtBatchStatus = abtBatchStatus;
    }

    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        abtErrorActivity = "";
        abtErrorAbendCode = 0;
        abtErrorSection = "";
        abtDaAccessName = "";
        abtDaFunction = "";
        abtBatchStatus = "";
    }

    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ abtErrorActivity=\"");
        s.append(getAbtErrorActivity());
        s.append("\"");
        s.append(", abtErrorAbendCode=\"");
        s.append(getAbtErrorAbendCode());
        s.append("\"");
        s.append(", filler10=\"");
        s.append(new String(filler10, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }

    private boolean equals(AbtPgmErrorData that) {
        return this.abtErrorActivity.equals(that.abtErrorActivity) &&
            this.abtErrorAbendCode == that.abtErrorAbendCode &&
            Arrays.equals(this.filler10, that.filler10);
    }

    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof AbtPgmErrorData other) && other.canEqual(this) && this.equals(other);
    }

    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof AbtPgmErrorData;
    }

    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Objects.hashCode(abtErrorActivity);
        result = 31 * result + Integer.hashCode(abtErrorAbendCode);
        result = 31 * result + Arrays.hashCode(filler10);
        return result;
    }

    @Override
    public int compareTo(AbtPgmErrorData that) {
        int c = 0;
        c = this.abtErrorActivity.compareTo(that.abtErrorActivity);
        if ( c != 0 ) return c;
        c = Integer.compare(this.abtErrorAbendCode, that.abtErrorAbendCode);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler10, that.filler10);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }

    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }

    private static final StringField ABT_ERROR_ACTIVITY = factory.getStringField(4);
    private static final BinaryAsIntField ABT_ERROR_ABEND_CODE = factory.getBinaryAsIntField(4, true);
    private static final ByteArrayField FILLER_10 = factory.getByteArrayField(16);
    private byte[] filler10 = new byte[16];
    private void initFiller() {
        new StringField(0, 16).putString("\0".repeat(16), filler10);
    }
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata

    /**
     * Retrieves a COBOL-format byte array representation of the {@code AbtPgmErrorData} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code ABT-PGM-ERROR-DATA} record
     * @see "ABT-PGM-ERROR-DATA record at MXWW03.CPY:52"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        ABT_ERROR_ACTIVITY.putString(abtErrorActivity, bytes, offset);
        ABT_ERROR_ABEND_CODE.putInt(abtErrorAbendCode, bytes, offset);
        FILLER_10.putByteArray(filler10, bytes, offset);
        return bytes;
    }

    /**
     * Retrieves a COBOL-format byte array representation of the {@code AbtPgmErrorData} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }

    /**
     * Retrieves a COBOL-format byte array representation of the {@code AbtPgmErrorData} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }

    /**
     * Retrieves a COBOL-format string representation of the {@code AbtPgmErrorData} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }

    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code ABT-PGM-ERROR-DATA} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "ABT-PGM-ERROR-DATA record at MXWW03.CPY:52"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        abtErrorActivity = ABT_ERROR_ACTIVITY.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        abtErrorAbendCode = ABT_ERROR_ABEND_CODE.getInt(bytes, offset);
        FILLER_10.getByteArray(bytes, offset);
    }


    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }

    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }

    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }

}
