package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class I1IsoDate extends DateParmArea {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String i1IsoCc = "";
    private String i1IsoYy = "";
    private String i1IsoMm = "";
    private String i1IsoDd = "";
    
    /** Initialize fields to non-null default values */
    public I1IsoDate() {}
    
    /** Initialize all fields to provided values */
    public I1IsoDate(int reqDay, int manipulateReqType, int numberOfDays, int numberOfMonths, int inputDateType, int oIsoCc, int oIsoYy, int oIsoMm, int oIsoDd, int oUsaMm, int oUsaDd, int oUsaCc, int oUsaYy, int oEurDd, int oEurMm, int oEurCc, int oEurYy, int oJisCc, int oJisYy, int oJisMm, int oJisDd, int oMdySlshMm, int oMdySlshDd, int oMdySlshYy, int oMdyMm, int oMdyDd, int oMdyYy, int oMdcySlshMm, int oMdcySlshDd, int oMdcySlshCc, int oMdcySlshYy, int oMdcyMm, int oMdcyDd, int oMdcyCc, int oMdcyYy, int oYmdYy, int oYmdMm, int oYmdDd, int oCymdCc, int oCymdYy, int oCymdMm, int oCymdDd, int oJulYy, int oJulDdd, String oExpMm, int oExpDd, int oExpCc, int oExpYy, int differenceInDays, int oDayNumber, String reqCountry, OutputNextMoYr outputNextMoYr, OutputLastMoYr outputLastMoYr, String i1IsoCc, String i1IsoYy, String i1IsoMm, String i1IsoDd) {
        super(reqDay, manipulateReqType, numberOfDays, numberOfMonths, inputDateType, oIsoCc, oIsoYy, oIsoMm, oIsoDd, oUsaMm, oUsaDd, oUsaCc, oUsaYy, oEurDd, oEurMm, oEurCc, oEurYy, oJisCc, oJisYy, oJisMm, oJisDd, oMdySlshMm, oMdySlshDd, oMdySlshYy, oMdyMm, oMdyDd, oMdyYy, oMdcySlshMm, oMdcySlshDd, oMdcySlshCc, oMdcySlshYy, oMdcyMm, oMdcyDd, oMdcyCc, oMdcyYy, oYmdYy, oYmdMm, oYmdDd, oCymdCc, oCymdYy, oCymdMm, oCymdDd, oJulYy, oJulDdd, oExpMm, oExpDd, oExpCc, oExpYy, differenceInDays, oDayNumber, reqCountry, outputNextMoYr, outputLastMoYr);
        this.i1IsoCc = i1IsoCc;
        this.i1IsoYy = i1IsoYy;
        this.i1IsoMm = i1IsoMm;
        this.i1IsoDd = i1IsoDd;
    }
    
    @Override
    public I1IsoDate clone() throws CloneNotSupportedException {
        I1IsoDate cloned = (I1IsoDate) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code I1IsoDate} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected I1IsoDate(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code I1IsoDate} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected I1IsoDate(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I1IsoDate} object
     * @see #setBytes(byte[], int)
     */
    public static I1IsoDate fromBytes(byte[] bytes, int offset) {
        return new I1IsoDate(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I1IsoDate} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static I1IsoDate fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code I1IsoDate} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static I1IsoDate fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getI1IsoCc() {
        return this.i1IsoCc;
    }
    
    public void setI1IsoCc(String i1IsoCc) {
        this.i1IsoCc = i1IsoCc;
    }
    
    public String getI1IsoYy() {
        return this.i1IsoYy;
    }
    
    public void setI1IsoYy(String i1IsoYy) {
        this.i1IsoYy = i1IsoYy;
    }
    
    public String getI1IsoMm() {
        return this.i1IsoMm;
    }
    
    public void setI1IsoMm(String i1IsoMm) {
        this.i1IsoMm = i1IsoMm;
    }
    
    public String getI1IsoDd() {
        return this.i1IsoDd;
    }
    
    public void setI1IsoDd(String i1IsoDd) {
        this.i1IsoDd = i1IsoDd;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        i1IsoCc = "";
        i1IsoYy = "";
        i1IsoMm = "";
        i1IsoDd = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ i1IsoCc=\"");
        s.append(getI1IsoCc());
        s.append("\"");
        s.append(", i1IsoYy=\"");
        s.append(getI1IsoYy());
        s.append("\"");
        s.append(", filler24=\"");
        s.append(new String(filler24, encoding));
        s.append("\"");
        s.append(", i1IsoMm=\"");
        s.append(getI1IsoMm());
        s.append("\"");
        s.append(", filler25=\"");
        s.append(new String(filler25, encoding));
        s.append("\"");
        s.append(", i1IsoDd=\"");
        s.append(getI1IsoDd());
        s.append("\"");
        s.append(", filler26=\"");
        s.append(new String(filler26, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(I1IsoDate that) {
        return super.equals(that) &&
            this.i1IsoCc.equals(that.i1IsoCc) &&
            this.i1IsoYy.equals(that.i1IsoYy) &&
            Arrays.equals(this.filler24, that.filler24) &&
            this.i1IsoMm.equals(that.i1IsoMm) &&
            Arrays.equals(this.filler25, that.filler25) &&
            this.i1IsoDd.equals(that.i1IsoDd) &&
            Arrays.equals(this.filler26, that.filler26);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof I1IsoDate other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof I1IsoDate;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(i1IsoCc);
        result = 31 * result + Objects.hashCode(i1IsoYy);
        result = 31 * result + Arrays.hashCode(filler24);
        result = 31 * result + Objects.hashCode(i1IsoMm);
        result = 31 * result + Arrays.hashCode(filler25);
        result = 31 * result + Objects.hashCode(i1IsoDd);
        result = 31 * result + Arrays.hashCode(filler26);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(I1IsoDate that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.i1IsoCc.compareTo(that.i1IsoCc);
        if ( c != 0 ) return c;
        c = this.i1IsoYy.compareTo(that.i1IsoYy);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler24, that.filler24);
        if ( c != 0 ) return c;
        c = this.i1IsoMm.compareTo(that.i1IsoMm);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler25, that.filler25);
        if ( c != 0 ) return c;
        c = this.i1IsoDd.compareTo(that.i1IsoDd);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler26, that.filler26);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(DateParmArea that) {
        if (that instanceof I1IsoDate other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(DateParmArea.SIZE);
    }
    
    private static final StringField I_1_ISO_CC = factory.getStringField(2);
    private static final StringField I_1_ISO_YY = factory.getStringField(2);
    private static final ByteArrayField FILLER_24 = factory.getByteArrayField(1);
    private byte[] filler24 = new byte[1];
    private static final StringField I_1_ISO_MM = factory.getStringField(2);
    private static final ByteArrayField FILLER_25 = factory.getByteArrayField(1);
    private byte[] filler25 = new byte[1];
    private static final StringField I_1_ISO_DD = factory.getStringField(2);
    private static final ByteArrayField FILLER_26 = factory.getByteArrayField(2);
    private byte[] filler26 = new byte[2];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "I1-ISO-DATE record at MXWW01.CPY:92"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        I_1_ISO_CC.putString(i1IsoCc, bytes, offset);
        I_1_ISO_YY.putString(i1IsoYy, bytes, offset);
        FILLER_24.putByteArray(filler24, bytes, offset);
        I_1_ISO_MM.putString(i1IsoMm, bytes, offset);
        FILLER_25.putByteArray(filler25, bytes, offset);
        I_1_ISO_DD.putString(i1IsoDd, bytes, offset);
        FILLER_26.putByteArray(filler26, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#setBytes(byte[], int)} to set parent-class state.
     * @see "I1-ISO-DATE record at MXWW01.CPY:92"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        i1IsoCc = I_1_ISO_CC.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        i1IsoYy = I_1_ISO_YY.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_24.getByteArray(bytes, offset);
        i1IsoMm = I_1_ISO_MM.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_25.getByteArray(bytes, offset);
        i1IsoDd = I_1_ISO_DD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_26.getByteArray(bytes, offset);
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
