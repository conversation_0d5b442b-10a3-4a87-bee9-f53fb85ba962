package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class I1JulDate extends DateParmArea {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String i1JulYy = "";
    private String i1JulDdd = "";
    
    /** Initialize fields to non-null default values */
    public I1JulDate() {}
    
    /** Initialize all fields to provided values */
    public I1JulDate(int reqDay, int manipulateReqType, int numberOfDays, int numberOfMonths, int inputDateType, int oIsoCc, int oIsoYy, int oIsoMm, int oIsoDd, int oUsaMm, int oUsaDd, int oUsaCc, int oUsaYy, int oEurDd, int oEurMm, int oEurCc, int oEurYy, int oJisCc, int oJisYy, int oJisMm, int oJisDd, int oMdySlshMm, int oMdySlshDd, int oMdySlshYy, int oMdyMm, int oMdyDd, int oMdyYy, int oMdcySlshMm, int oMdcySlshDd, int oMdcySlshCc, int oMdcySlshYy, int oMdcyMm, int oMdcyDd, int oMdcyCc, int oMdcyYy, int oYmdYy, int oYmdMm, int oYmdDd, int oCymdCc, int oCymdYy, int oCymdMm, int oCymdDd, int oJulYy, int oJulDdd, String oExpMm, int oExpDd, int oExpCc, int oExpYy, int differenceInDays, int oDayNumber, String reqCountry, OutputNextMoYr outputNextMoYr, OutputLastMoYr outputLastMoYr, String i1JulYy, String i1JulDdd) {
        super(reqDay, manipulateReqType, numberOfDays, numberOfMonths, inputDateType, oIsoCc, oIsoYy, oIsoMm, oIsoDd, oUsaMm, oUsaDd, oUsaCc, oUsaYy, oEurDd, oEurMm, oEurCc, oEurYy, oJisCc, oJisYy, oJisMm, oJisDd, oMdySlshMm, oMdySlshDd, oMdySlshYy, oMdyMm, oMdyDd, oMdyYy, oMdcySlshMm, oMdcySlshDd, oMdcySlshCc, oMdcySlshYy, oMdcyMm, oMdcyDd, oMdcyCc, oMdcyYy, oYmdYy, oYmdMm, oYmdDd, oCymdCc, oCymdYy, oCymdMm, oCymdDd, oJulYy, oJulDdd, oExpMm, oExpDd, oExpCc, oExpYy, differenceInDays, oDayNumber, reqCountry, outputNextMoYr, outputLastMoYr);
        this.i1JulYy = i1JulYy;
        this.i1JulDdd = i1JulDdd;
    }
    
    @Override
    public I1JulDate clone() throws CloneNotSupportedException {
        I1JulDate cloned = (I1JulDate) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code I1JulDate} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected I1JulDate(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code I1JulDate} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected I1JulDate(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I1JulDate} object
     * @see #setBytes(byte[], int)
     */
    public static I1JulDate fromBytes(byte[] bytes, int offset) {
        return new I1JulDate(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I1JulDate} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static I1JulDate fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code I1JulDate} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static I1JulDate fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getI1JulYy() {
        return this.i1JulYy;
    }
    
    public void setI1JulYy(String i1JulYy) {
        this.i1JulYy = i1JulYy;
    }
    
    public String getI1JulDdd() {
        return this.i1JulDdd;
    }
    
    public void setI1JulDdd(String i1JulDdd) {
        this.i1JulDdd = i1JulDdd;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        i1JulYy = "";
        i1JulDdd = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ i1JulYy=\"");
        s.append(getI1JulYy());
        s.append("\"");
        s.append(", i1JulDdd=\"");
        s.append(getI1JulDdd());
        s.append("\"");
        s.append(", filler46=\"");
        s.append(new String(filler46, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(I1JulDate that) {
        return super.equals(that) &&
            this.i1JulYy.equals(that.i1JulYy) &&
            this.i1JulDdd.equals(that.i1JulDdd) &&
            Arrays.equals(this.filler46, that.filler46);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof I1JulDate other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof I1JulDate;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(i1JulYy);
        result = 31 * result + Objects.hashCode(i1JulDdd);
        result = 31 * result + Arrays.hashCode(filler46);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(I1JulDate that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.i1JulYy.compareTo(that.i1JulYy);
        if ( c != 0 ) return c;
        c = this.i1JulDdd.compareTo(that.i1JulDdd);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler46, that.filler46);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(DateParmArea that) {
        if (that instanceof I1JulDate other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(DateParmArea.SIZE);
    }
    
    private static final StringField I_1_JUL_YY = factory.getStringField(2);
    private static final StringField I_1_JUL_DDD = factory.getStringField(3);
    private static final ByteArrayField FILLER_46 = factory.getByteArrayField(7);
    private byte[] filler46 = new byte[7];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "I1-JUL-DATE record at MXWW01.CPY:168"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        I_1_JUL_YY.putString(i1JulYy, bytes, offset);
        I_1_JUL_DDD.putString(i1JulDdd, bytes, offset);
        FILLER_46.putByteArray(filler46, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#setBytes(byte[], int)} to set parent-class state.
     * @see "I1-JUL-DATE record at MXWW01.CPY:168"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        i1JulYy = I_1_JUL_YY.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        i1JulDdd = I_1_JUL_DDD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_46.getByteArray(bytes, offset);
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
