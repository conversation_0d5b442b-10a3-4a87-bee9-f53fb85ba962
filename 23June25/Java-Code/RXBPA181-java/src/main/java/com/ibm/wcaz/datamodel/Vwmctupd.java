package com.ibm.wcaz.datamodel;

import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.sql.Date;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Objects;

public class Vwmctupd implements Cloneable, Comparable<Vwmctupd> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String subsystemIdInd = "";
    private String subfunctionCode = "";
    private Date procDate = new Date(0);
    private String monthEndFlag = "";
    private String yearEndFlag = "";
    
    /** Initialize fields to non-null default values */
    public Vwmctupd() {}
    
    /** Initialize all fields to provided values */
    public Vwmctupd(String subsystemIdInd, String subfunctionCode, Date procDate, String monthEndFlag, String yearEndFlag) {
        this.subsystemIdInd = subsystemIdInd;
        this.subfunctionCode = subfunctionCode;
        this.procDate = procDate;
        this.monthEndFlag = monthEndFlag;
        this.yearEndFlag = yearEndFlag;
    }
    
    @Override
    public Vwmctupd clone() throws CloneNotSupportedException {
        Vwmctupd cloned = (Vwmctupd) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code Vwmctupd} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected Vwmctupd(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code Vwmctupd} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected Vwmctupd(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code Vwmctupd} object
     * @see #setBytes(byte[], int)
     */
    public static Vwmctupd fromBytes(byte[] bytes, int offset) {
        return new Vwmctupd(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code Vwmctupd} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static Vwmctupd fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code Vwmctupd} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static Vwmctupd fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getSubsystemIdInd() {
        return this.subsystemIdInd;
    }
    
    public void setSubsystemIdInd(String subsystemIdInd) {
        this.subsystemIdInd = subsystemIdInd;
    }
    
    public String getSubfunctionCode() {
        return this.subfunctionCode;
    }
    
    public void setSubfunctionCode(String subfunctionCode) {
        this.subfunctionCode = subfunctionCode;
    }
    
    public Date getProcDate() {
        return this.procDate;
    }
    
    public void setProcDate(Date procDate) {
        this.procDate = procDate;
    }
    
    public String getMonthEndFlag() {
        return this.monthEndFlag;
    }
    
    public void setMonthEndFlag(String monthEndFlag) {
        this.monthEndFlag = monthEndFlag;
    }
    
    public String getYearEndFlag() {
        return this.yearEndFlag;
    }
    
    public void setYearEndFlag(String yearEndFlag) {
        this.yearEndFlag = yearEndFlag;
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ subsystemIdInd=\"");
        s.append(getSubsystemIdInd());
        s.append("\"");
        s.append(", subfunctionCode=\"");
        s.append(getSubfunctionCode());
        s.append("\"");
        s.append(", procDate=\"");
        s.append(getProcDate());
        s.append("\"");
        s.append(", monthEndFlag=\"");
        s.append(getMonthEndFlag());
        s.append("\"");
        s.append(", yearEndFlag=\"");
        s.append(getYearEndFlag());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(Vwmctupd that) {
        return this.subsystemIdInd.equals(that.subsystemIdInd) &&
            this.subfunctionCode.equals(that.subfunctionCode) &&
            this.procDate.equals(that.procDate) &&
            this.monthEndFlag.equals(that.monthEndFlag) &&
            this.yearEndFlag.equals(that.yearEndFlag);
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof Vwmctupd other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof Vwmctupd;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Objects.hashCode(subsystemIdInd);
        result = 31 * result + Objects.hashCode(subfunctionCode);
        result = 31 * result + Objects.hashCode(procDate);
        result = 31 * result + Objects.hashCode(monthEndFlag);
        result = 31 * result + Objects.hashCode(yearEndFlag);
        return result;
    }
    
    @Override
    public int compareTo(Vwmctupd that) {
        int c = 0;
        c = this.subsystemIdInd.compareTo(that.subsystemIdInd);
        if ( c != 0 ) return c;
        c = this.subfunctionCode.compareTo(that.subfunctionCode);
        if ( c != 0 ) return c;
        c = this.procDate.compareTo(that.procDate);
        if ( c != 0 ) return c;
        c = this.monthEndFlag.compareTo(that.monthEndFlag);
        if ( c != 0 ) return c;
        c = this.yearEndFlag.compareTo(that.yearEndFlag);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final StringField SUBSYSTEM_ID_IND = factory.getStringField(2);
    private static final StringField SUBFUNCTION_CODE = factory.getStringField(9);
    private static final StringField PROC_DATE = factory.getStringField(8);
    private static final DateTimeFormatter PROC_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField MONTH_END_FLAG = factory.getStringField(2);
    private static final StringField YEAR_END_FLAG = factory.getStringField(2);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code Vwmctupd} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code VWMCTUPD} record
     * @see "VWMCTUPD record at VWMCTUPD.CPY:10"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        SUBSYSTEM_ID_IND.putString(subsystemIdInd, bytes, offset);
        SUBFUNCTION_CODE.putString(subfunctionCode, bytes, offset);
        PROC_DATE.putString(procDate.toLocalDate().format(PROC_DATE_FMT), bytes, offset);
        MONTH_END_FLAG.putString(monthEndFlag, bytes, offset);
        YEAR_END_FLAG.putString(yearEndFlag, bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code Vwmctupd} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code Vwmctupd} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code Vwmctupd} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code VWMCTUPD} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "VWMCTUPD record at VWMCTUPD.CPY:10"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        subsystemIdInd = SUBSYSTEM_ID_IND.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        subfunctionCode = SUBFUNCTION_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        procDate = Date.valueOf(LocalDate.parse(PROC_DATE.getString(bytes, offset), PROC_DATE_FMT));
        monthEndFlag = MONTH_END_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        yearEndFlag = YEAR_END_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
