package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class WsFileStatus implements Cloneable, Comparable<WsFileStatus> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private static final String HEADER_REC_MISSING_VALUE = "HEADER MISSING";
    private static final String CUST_ID_NOT_PRESENT_VALUE = "CPU ID MISSING";
    private static final String CUST_CODE_NOT_PRESENT_VALUE = "CPU CODE MISSING";
    private static final String CUST_ID_CODE_NOT_FND_VALUE = "PARTNER INVALID";
    private static final String CUST_STATUS_INACTIVE_VALUE = "PARTNER INACTIVE";
    private static final String CREAT_DATE_NOT_PRESENT_VALUE = "CREATE DATE MISSING";
    private static final String CREAT_DATE_NOT_VALID_VALUE = "CREATE DATE INVALID";
    private static final String CREAT_TIME_NOT_PRESENT_VALUE = "CREATE TIME MISSING";
    private static final String CREAT_TIME_NOT_VALID_VALUE = "CREATE TIME INVALID";
    private static final String DOC_TYPE_NOT_PRESENT_VALUE = "DOC TYPE MISSING";
    private static final String DOC_TYPE_NOT_UNIT_REG_VALUE = "DOC TYPE INVALID";
    private static final String TRAILER_REC_MISSING_VALUE = "TRAILER MISSING";
    private static final String CUST_ID_MISMATCH_VALUE = "CPU ID INVALID";
    private static final String CUST_CODE_MISMATCH_VALUE = "CPU CODE INVALID";
    private static final String CREAT_DATE_MISMATCH_VALUE = "CREATE DATE INVALID";
    private static final String CREAT_TIME_MISMATCH_VALUE = "CREATE TIME INVALID";
    private static final String REC_COUNT_INVALID_VALUE = "REC COUNT MISSING";
    private static final String REC_COUNT_MISMATCH_VALUE = "OUT OF BALANCE";
    private static final String EMPTY_FILE_VALUE = "NO DATA TO REPORT";
    
    private String wsFileStatus = "";
    
    /** Initialize fields to non-null default values */
    public WsFileStatus() {}
    
    /** Initialize all fields to provided values */
    public WsFileStatus(String wsFileStatus) {
        this.wsFileStatus = wsFileStatus;
    }
    
    @Override
    public WsFileStatus clone() throws CloneNotSupportedException {
        WsFileStatus cloned = (WsFileStatus) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code WsFileStatus} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected WsFileStatus(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code WsFileStatus} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected WsFileStatus(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsFileStatus} object
     * @see #setBytes(byte[], int)
     */
    public static WsFileStatus fromBytes(byte[] bytes, int offset) {
        return new WsFileStatus(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsFileStatus} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static WsFileStatus fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code WsFileStatus} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static WsFileStatus fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getWsFileStatus() {
        return this.wsFileStatus;
    }
    
    public void setWsFileStatus(String wsFileStatus) {
        this.wsFileStatus = wsFileStatus;
    }
    
    public boolean isHeaderRecMissing() {
        return wsFileStatus.equals(HEADER_REC_MISSING_VALUE);
    }
    
    public void setHeaderRecMissing() {
        wsFileStatus = HEADER_REC_MISSING_VALUE;
    }
    
    public boolean isCustIdNotPresent() {
        return wsFileStatus.equals(CUST_ID_NOT_PRESENT_VALUE);
    }
    
    public void setCustIdNotPresent() {
        wsFileStatus = CUST_ID_NOT_PRESENT_VALUE;
    }
    
    public boolean isCustCodeNotPresent() {
        return wsFileStatus.equals(CUST_CODE_NOT_PRESENT_VALUE);
    }
    
    public void setCustCodeNotPresent() {
        wsFileStatus = CUST_CODE_NOT_PRESENT_VALUE;
    }
    
    public boolean isCustIdCodeNotFnd() {
        return wsFileStatus.equals(CUST_ID_CODE_NOT_FND_VALUE);
    }
    
    public void setCustIdCodeNotFnd() {
        wsFileStatus = CUST_ID_CODE_NOT_FND_VALUE;
    }
    
    public boolean isCustStatusInactive() {
        return wsFileStatus.equals(CUST_STATUS_INACTIVE_VALUE);
    }
    
    public void setCustStatusInactive() {
        wsFileStatus = CUST_STATUS_INACTIVE_VALUE;
    }
    
    public boolean isCreatDateNotPresent() {
        return wsFileStatus.equals(CREAT_DATE_NOT_PRESENT_VALUE);
    }
    
    public void setCreatDateNotPresent() {
        wsFileStatus = CREAT_DATE_NOT_PRESENT_VALUE;
    }
    
    public boolean isCreatDateNotValid() {
        return wsFileStatus.equals(CREAT_DATE_NOT_VALID_VALUE);
    }
    
    public void setCreatDateNotValid() {
        wsFileStatus = CREAT_DATE_NOT_VALID_VALUE;
    }
    
    public boolean isCreatTimeNotPresent() {
        return wsFileStatus.equals(CREAT_TIME_NOT_PRESENT_VALUE);
    }
    
    public void setCreatTimeNotPresent() {
        wsFileStatus = CREAT_TIME_NOT_PRESENT_VALUE;
    }
    
    public boolean isCreatTimeNotValid() {
        return wsFileStatus.equals(CREAT_TIME_NOT_VALID_VALUE);
    }
    
    public void setCreatTimeNotValid() {
        wsFileStatus = CREAT_TIME_NOT_VALID_VALUE;
    }
    
    public boolean isDocTypeNotPresent() {
        return wsFileStatus.equals(DOC_TYPE_NOT_PRESENT_VALUE);
    }
    
    public void setDocTypeNotPresent() {
        wsFileStatus = DOC_TYPE_NOT_PRESENT_VALUE;
    }
    
    public boolean isDocTypeNotUnitReg() {
        return wsFileStatus.equals(DOC_TYPE_NOT_UNIT_REG_VALUE);
    }
    
    public void setDocTypeNotUnitReg() {
        wsFileStatus = DOC_TYPE_NOT_UNIT_REG_VALUE;
    }
    
    public boolean isTrailerRecMissing() {
        return wsFileStatus.equals(TRAILER_REC_MISSING_VALUE);
    }
    
    public void setTrailerRecMissing() {
        wsFileStatus = TRAILER_REC_MISSING_VALUE;
    }
    
    public boolean isCustIdMismatch() {
        return wsFileStatus.equals(CUST_ID_MISMATCH_VALUE);
    }
    
    public void setCustIdMismatch() {
        wsFileStatus = CUST_ID_MISMATCH_VALUE;
    }
    
    public boolean isCustCodeMismatch() {
        return wsFileStatus.equals(CUST_CODE_MISMATCH_VALUE);
    }
    
    public void setCustCodeMismatch() {
        wsFileStatus = CUST_CODE_MISMATCH_VALUE;
    }
    
    public boolean isCreatDateMismatch() {
        return wsFileStatus.equals(CREAT_DATE_MISMATCH_VALUE);
    }
    
    public void setCreatDateMismatch() {
        wsFileStatus = CREAT_DATE_MISMATCH_VALUE;
    }
    
    public boolean isCreatTimeMismatch() {
        return wsFileStatus.equals(CREAT_TIME_MISMATCH_VALUE);
    }
    
    public void setCreatTimeMismatch() {
        wsFileStatus = CREAT_TIME_MISMATCH_VALUE;
    }
    
    public boolean isRecCountInvalid() {
        return wsFileStatus.equals(REC_COUNT_INVALID_VALUE);
    }
    
    public void setRecCountInvalid() {
        wsFileStatus = REC_COUNT_INVALID_VALUE;
    }
    
    public boolean isRecCountMismatch() {
        return wsFileStatus.equals(REC_COUNT_MISMATCH_VALUE);
    }
    
    public void setRecCountMismatch() {
        wsFileStatus = REC_COUNT_MISMATCH_VALUE;
    }
    
    public boolean isEmptyFile() {
        return wsFileStatus.equals(EMPTY_FILE_VALUE);
    }
    
    public void setEmptyFile() {
        wsFileStatus = EMPTY_FILE_VALUE;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        wsFileStatus = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ wsFileStatus=\"");
        s.append(getWsFileStatus());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(WsFileStatus that) {
        return this.wsFileStatus.equals(that.wsFileStatus);
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof WsFileStatus other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof WsFileStatus;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Objects.hashCode(wsFileStatus);
        return result;
    }
    
    @Override
    public int compareTo(WsFileStatus that) {
        int c = 0;
        c = this.wsFileStatus.compareTo(that.wsFileStatus);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final StringField WS_FILE_STATUS = factory.getStringField(20);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsFileStatus} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code WS-FILE-STATUS} record
     * @see "WS-FILE-STATUS record at RXBPASVC.cbl:165"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        WS_FILE_STATUS.putString(wsFileStatus, bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsFileStatus} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsFileStatus} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code WsFileStatus} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code WS-FILE-STATUS} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "WS-FILE-STATUS record at RXBPASVC.cbl:165"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        wsFileStatus = WS_FILE_STATUS.getString(bytes, offset).replaceFirst("\\s+\\z", "");
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
