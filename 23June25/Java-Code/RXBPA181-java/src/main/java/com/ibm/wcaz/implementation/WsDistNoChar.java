package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class WsDistNoChar extends WsSaveArea {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String wsSaveDistNo1St3 = "";
    private String wsSaveDistNoLast6 = "";
    
    /** Initialize fields to non-null default values */
    public WsDistNoChar() {}
    
    /** Initialize all fields to provided values */
    public WsDistNoChar(String wsSaveCdfCustId, String wsSaveCdfCustCode, String wsSaveCreatDate, String wsSaveCreatTime, String wsSaveMfgName, String wsSaveDistNo1St3, String wsSaveDistNoLast6) {
        super(wsSaveCdfCustId, wsSaveCdfCustCode, wsSaveCreatDate, wsSaveCreatTime, wsSaveMfgName);
        this.wsSaveDistNo1St3 = wsSaveDistNo1St3;
        this.wsSaveDistNoLast6 = wsSaveDistNoLast6;
    }
    
    @Override
    public WsDistNoChar clone() throws CloneNotSupportedException {
        WsDistNoChar cloned = (WsDistNoChar) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code WsDistNoChar} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected WsDistNoChar(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code WsDistNoChar} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected WsDistNoChar(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsDistNoChar} object
     * @see #setBytes(byte[], int)
     */
    public static WsDistNoChar fromBytes(byte[] bytes, int offset) {
        return new WsDistNoChar(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsDistNoChar} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static WsDistNoChar fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code WsDistNoChar} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static WsDistNoChar fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getWsSaveDistNo1St3() {
        return this.wsSaveDistNo1St3;
    }
    
    public void setWsSaveDistNo1St3(String wsSaveDistNo1St3) {
        this.wsSaveDistNo1St3 = wsSaveDistNo1St3;
    }
    
    public String getWsSaveDistNoLast6() {
        return this.wsSaveDistNoLast6;
    }
    
    public void setWsSaveDistNoLast6(String wsSaveDistNoLast6) {
        this.wsSaveDistNoLast6 = wsSaveDistNoLast6;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        wsSaveDistNo1St3 = "";
        wsSaveDistNoLast6 = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ wsSaveDistNo1St3=\"");
        s.append(getWsSaveDistNo1St3());
        s.append("\"");
        s.append(", wsSaveDistNoLast6=\"");
        s.append(getWsSaveDistNoLast6());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(WsDistNoChar that) {
        return super.equals(that) &&
            this.wsSaveDistNo1St3.equals(that.wsSaveDistNo1St3) &&
            this.wsSaveDistNoLast6.equals(that.wsSaveDistNoLast6);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof WsDistNoChar other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof WsDistNoChar;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(wsSaveDistNo1St3);
        result = 31 * result + Objects.hashCode(wsSaveDistNoLast6);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(WsDistNoChar that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.wsSaveDistNo1St3.compareTo(that.wsSaveDistNo1St3);
        if ( c != 0 ) return c;
        c = this.wsSaveDistNoLast6.compareTo(that.wsSaveDistNoLast6);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(WsSaveArea that) {
        if (that instanceof WsDistNoChar other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(WsSaveArea.SIZE);
    }
    
    private static final StringField WS_SAVE_DIST_NO_1_ST_3 = factory.getStringField(3);
    private static final StringField WS_SAVE_DIST_NO_LAST_6 = factory.getStringField(6);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link WsSaveArea#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "WS-DIST-NO-CHAR record at RXBPASVC.cbl:108"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        WS_SAVE_DIST_NO_1_ST_3.putString(wsSaveDistNo1St3, bytes, offset);
        WS_SAVE_DIST_NO_LAST_6.putString(wsSaveDistNoLast6, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link WsSaveArea#setBytes(byte[], int)} to set parent-class state.
     * @see "WS-DIST-NO-CHAR record at RXBPASVC.cbl:108"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        wsSaveDistNo1St3 = WS_SAVE_DIST_NO_1_ST_3.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        wsSaveDistNoLast6 = WS_SAVE_DIST_NO_LAST_6.getString(bytes, offset).replaceFirst("\\s+\\z", "");
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
