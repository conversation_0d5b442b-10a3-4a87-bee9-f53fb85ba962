package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class DateEditError extends DateParmArea {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private static final String DATE_EDIT_SUCCESS_VALUE = "";
    private static final String INVALID_DATE_TYPE_VALUE = "W00201";
    private static final String INVALID_REQ_COUNTRY_VALUE = "W00202";
    private static final String INVALID_REQ_DAY_VALUE = "W00203";
    private static final String INVALID_MANIPULATE_REQ_VALUE = "W00204";
    private static final String DATE_2_LESS_THAN_DATE_1_VALUE = "W00205";
    private static final String INVALID_INPUT_DATE_VALUE = "W00206";
    private static final String DATE_NOT_IN_BUSS_TAB_VALUE = "W00207";
    private static final String INVALID_CASH_DAY_VALUE = "W00208";
    private static final String INVALID_BUSS_DAY_VALUE = "W00209";
    private static final String NO_CICS_CWA_VALUE = "W00210";
    private static final String NO_CICS_DATE_TABLE_VALUE = "W00211";
    
    private String dateEditError = "";
    
    /** Initialize fields to non-null default values */
    public DateEditError() {}
    
    /** Initialize all fields to provided values */
    public DateEditError(int reqDay, int manipulateReqType, int numberOfDays, int numberOfMonths, int inputDateType, int oIsoCc, int oIsoYy, int oIsoMm, int oIsoDd, int oUsaMm, int oUsaDd, int oUsaCc, int oUsaYy, int oEurDd, int oEurMm, int oEurCc, int oEurYy, int oJisCc, int oJisYy, int oJisMm, int oJisDd, int oMdySlshMm, int oMdySlshDd, int oMdySlshYy, int oMdyMm, int oMdyDd, int oMdyYy, int oMdcySlshMm, int oMdcySlshDd, int oMdcySlshCc, int oMdcySlshYy, int oMdcyMm, int oMdcyDd, int oMdcyCc, int oMdcyYy, int oYmdYy, int oYmdMm, int oYmdDd, int oCymdCc, int oCymdYy, int oCymdMm, int oCymdDd, int oJulYy, int oJulDdd, String oExpMm, int oExpDd, int oExpCc, int oExpYy, int differenceInDays, int oDayNumber, String reqCountry, OutputNextMoYr outputNextMoYr, OutputLastMoYr outputLastMoYr, String dateEditError) {
        super(reqDay, manipulateReqType, numberOfDays, numberOfMonths, inputDateType, oIsoCc, oIsoYy, oIsoMm, oIsoDd, oUsaMm, oUsaDd, oUsaCc, oUsaYy, oEurDd, oEurMm, oEurCc, oEurYy, oJisCc, oJisYy, oJisMm, oJisDd, oMdySlshMm, oMdySlshDd, oMdySlshYy, oMdyMm, oMdyDd, oMdyYy, oMdcySlshMm, oMdcySlshDd, oMdcySlshCc, oMdcySlshYy, oMdcyMm, oMdcyDd, oMdcyCc, oMdcyYy, oYmdYy, oYmdMm, oYmdDd, oCymdCc, oCymdYy, oCymdMm, oCymdDd, oJulYy, oJulDdd, oExpMm, oExpDd, oExpCc, oExpYy, differenceInDays, oDayNumber, reqCountry, outputNextMoYr, outputLastMoYr);
        this.dateEditError = dateEditError;
    }
    
    @Override
    public DateEditError clone() throws CloneNotSupportedException {
        DateEditError cloned = (DateEditError) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code DateEditError} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected DateEditError(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code DateEditError} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected DateEditError(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code DateEditError} object
     * @see #setBytes(byte[], int)
     */
    public static DateEditError fromBytes(byte[] bytes, int offset) {
        return new DateEditError(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code DateEditError} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static DateEditError fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code DateEditError} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static DateEditError fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getDateEditError() {
        return this.dateEditError;
    }
    
    public void setDateEditError(String dateEditError) {
        this.dateEditError = dateEditError;
    }
    
    public boolean isDateEditSuccess() {
        return dateEditError.equals(DATE_EDIT_SUCCESS_VALUE);
    }
    
    public void setDateEditSuccess() {
        dateEditError = DATE_EDIT_SUCCESS_VALUE;
    }
    
    public boolean isInvalidDateType() {
        return dateEditError.equals(INVALID_DATE_TYPE_VALUE);
    }
    
    public void setInvalidDateType() {
        dateEditError = INVALID_DATE_TYPE_VALUE;
    }
    
    public boolean isInvalidReqCountry() {
        return dateEditError.equals(INVALID_REQ_COUNTRY_VALUE);
    }
    
    public void setInvalidReqCountry() {
        dateEditError = INVALID_REQ_COUNTRY_VALUE;
    }
    
    public boolean isInvalidReqDay() {
        return dateEditError.equals(INVALID_REQ_DAY_VALUE);
    }
    
    public void setInvalidReqDay() {
        dateEditError = INVALID_REQ_DAY_VALUE;
    }
    
    public boolean isInvalidManipulateReq() {
        return dateEditError.equals(INVALID_MANIPULATE_REQ_VALUE);
    }
    
    public void setInvalidManipulateReq() {
        dateEditError = INVALID_MANIPULATE_REQ_VALUE;
    }
    
    public boolean isDate2LessThanDate1() {
        return dateEditError.equals(DATE_2_LESS_THAN_DATE_1_VALUE);
    }
    
    public void setDate2LessThanDate1() {
        dateEditError = DATE_2_LESS_THAN_DATE_1_VALUE;
    }
    
    public boolean isInvalidInputDate() {
        return dateEditError.equals(INVALID_INPUT_DATE_VALUE);
    }
    
    public void setInvalidInputDate() {
        dateEditError = INVALID_INPUT_DATE_VALUE;
    }
    
    public boolean isDateNotInBussTab() {
        return dateEditError.equals(DATE_NOT_IN_BUSS_TAB_VALUE);
    }
    
    public void setDateNotInBussTab() {
        dateEditError = DATE_NOT_IN_BUSS_TAB_VALUE;
    }
    
    public boolean isInvalidCashDay() {
        return dateEditError.equals(INVALID_CASH_DAY_VALUE);
    }
    
    public void setInvalidCashDay() {
        dateEditError = INVALID_CASH_DAY_VALUE;
    }
    
    public boolean isInvalidBussDay() {
        return dateEditError.equals(INVALID_BUSS_DAY_VALUE);
    }
    
    public void setInvalidBussDay() {
        dateEditError = INVALID_BUSS_DAY_VALUE;
    }
    
    public boolean isNoCicsCwa() {
        return dateEditError.equals(NO_CICS_CWA_VALUE);
    }
    
    public void setNoCicsCwa() {
        dateEditError = NO_CICS_CWA_VALUE;
    }
    
    public boolean isNoCicsDateTable() {
        return dateEditError.equals(NO_CICS_DATE_TABLE_VALUE);
    }
    
    public void setNoCicsDateTable() {
        dateEditError = NO_CICS_DATE_TABLE_VALUE;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        dateEditError = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ dateEditError=\"");
        s.append(getDateEditError());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(DateEditError that) {
        return super.equals(that) &&
            this.dateEditError.equals(that.dateEditError);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof DateEditError other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof DateEditError;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(dateEditError);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(DateEditError that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.dateEditError.compareTo(that.dateEditError);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(DateParmArea that) {
        if (that instanceof DateEditError other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(DateParmArea.SIZE);
    }
    
    private static final StringField DATE_EDIT_ERROR = factory.getStringField(6);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "DATE-EDIT-ERROR record at MXWW01.CPY:360"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        DATE_EDIT_ERROR.putString(dateEditError, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#setBytes(byte[], int)} to set parent-class state.
     * @see "DATE-EDIT-ERROR record at MXWW01.CPY:360"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        dateEditError = DATE_EDIT_ERROR.getString(bytes, offset).replaceFirst("\\s+\\z", "");
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
