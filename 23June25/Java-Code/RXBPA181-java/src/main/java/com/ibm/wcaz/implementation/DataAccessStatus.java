package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class DataAccessStatus implements Cloneable, Comparable<DataAccessStatus> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private static final String DA_OK_VALUE = "OK ";
    private static final String DA_DUPLICATE_VALUE = "DUP";
    private static final String DA_NOTAVAIL_VALUE = "NAV";
    private static final String DA_NOTFOUND_VALUE = "NFD";
    private static final String DA_ENDFILE_VALUE_1 = "EOF";
    private static final String DA_ENDFILE_VALUE_2 = "NFD";
    private static final String DA_LOGICERR_VALUE = "LOG";
    private static final String DA_SECURITY_VALUE = "SEC";
    private static final String DA_DBMERROR_VALUE = "DBM";
    private static final String DA_ANYERROR_VALUE_1 = "DUP";
    private static final String DA_ANYERROR_VALUE_2 = "NAV";
    private static final String DA_ANYERROR_VALUE_3 = "NFD";
    private static final String DA_ANYERROR_VALUE_4 = "EOF";
    private static final String DA_ANYERROR_VALUE_5 = "LOG";
    private static final String DA_ANYERROR_VALUE_6 = "SEC";
    
    private String daStatus = "";
    
    /** Initialize fields to non-null default values */
    public DataAccessStatus() {}
    
    /** Initialize all fields to provided values */
    public DataAccessStatus(String daStatus) {
        this.daStatus = daStatus;
    }
    
    @Override
    public DataAccessStatus clone() throws CloneNotSupportedException {
        DataAccessStatus cloned = (DataAccessStatus) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code DataAccessStatus} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected DataAccessStatus(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code DataAccessStatus} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected DataAccessStatus(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code DataAccessStatus} object
     * @see #setBytes(byte[], int)
     */
    public static DataAccessStatus fromBytes(byte[] bytes, int offset) {
        return new DataAccessStatus(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code DataAccessStatus} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static DataAccessStatus fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code DataAccessStatus} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static DataAccessStatus fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getDaStatus() {
        return this.daStatus;
    }
    
    public void setDaStatus(String daStatus) {
        this.daStatus = daStatus;
    }
    
    public boolean isDaOk() {
        return daStatus.equals(DA_OK_VALUE);
    }
    
    public void setDaOk() {
        daStatus = DA_OK_VALUE;
    }
    
    public boolean isDaDuplicate() {
        return daStatus.equals(DA_DUPLICATE_VALUE);
    }
    
    public void setDaDuplicate() {
        daStatus = DA_DUPLICATE_VALUE;
    }
    
    public boolean isDaNotavail() {
        return daStatus.equals(DA_NOTAVAIL_VALUE);
    }
    
    public void setDaNotavail() {
        daStatus = DA_NOTAVAIL_VALUE;
    }
    
    public boolean isDaNotfound() {
        return daStatus.equals(DA_NOTFOUND_VALUE);
    }
    
    public void setDaNotfound() {
        daStatus = DA_NOTFOUND_VALUE;
    }
    
    public boolean isDaEndfile() {
        return
            (daStatus.equals(DA_ENDFILE_VALUE_1))
            || (daStatus.equals(DA_ENDFILE_VALUE_2));
    }
    
    public void setDaEndfile() {
        daStatus = DA_ENDFILE_VALUE_1;
    }
    
    public boolean isDaLogicerr() {
        return daStatus.equals(DA_LOGICERR_VALUE);
    }
    
    public void setDaLogicerr() {
        daStatus = DA_LOGICERR_VALUE;
    }
    
    public boolean isDaSecurity() {
        return daStatus.equals(DA_SECURITY_VALUE);
    }
    
    public void setDaSecurity() {
        daStatus = DA_SECURITY_VALUE;
    }
    
    public boolean isDaDbmerror() {
        return daStatus.equals(DA_DBMERROR_VALUE);
    }
    
    public void setDaDbmerror() {
        daStatus = DA_DBMERROR_VALUE;
    }
    
    public boolean isDaAnyerror() {
        return
            (daStatus.equals(DA_ANYERROR_VALUE_1))
            || (daStatus.equals(DA_ANYERROR_VALUE_2))
            || (daStatus.equals(DA_ANYERROR_VALUE_3))
            || (daStatus.equals(DA_ANYERROR_VALUE_4))
            || (daStatus.equals(DA_ANYERROR_VALUE_5))
            || (daStatus.equals(DA_ANYERROR_VALUE_6));
    }
    
    public void setDaAnyerror() {
        daStatus = DA_ANYERROR_VALUE_1;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        daStatus = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ daStatus=\"");
        s.append(getDaStatus());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(DataAccessStatus that) {
        return this.daStatus.equals(that.daStatus);
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof DataAccessStatus other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof DataAccessStatus;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Objects.hashCode(daStatus);
        return result;
    }
    
    @Override
    public int compareTo(DataAccessStatus that) {
        int c = 0;
        c = this.daStatus.compareTo(that.daStatus);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final StringField DA_STATUS = factory.getStringField(3);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code DataAccessStatus} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code DATA-ACCESS-STATUS} record
     * @see "DATA-ACCESS-STATUS record at MXWW03.CPY:141"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        DA_STATUS.putString(daStatus, bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code DataAccessStatus} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code DataAccessStatus} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code DataAccessStatus} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code DATA-ACCESS-STATUS} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "DATA-ACCESS-STATUS record at MXWW03.CPY:141"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        daStatus = DA_STATUS.getString(bytes, offset).replaceFirst("\\s+\\z", "");
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
