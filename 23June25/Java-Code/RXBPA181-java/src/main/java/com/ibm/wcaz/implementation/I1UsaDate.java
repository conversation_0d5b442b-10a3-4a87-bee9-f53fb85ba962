package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class I1UsaDate extends DateParmArea {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String i1UsaMm = "";
    private String i1UsaDd = "";
    private String i1UsaCc = "";
    private String i1UsaYy = "";
    
    /** Initialize fields to non-null default values */
    public I1UsaDate() {}
    
    /** Initialize all fields to provided values */
    public I1UsaDate(int reqDay, int manipulateReqType, int numberOfDays, int numberOfMonths, int inputDateType, int oIsoCc, int oIsoYy, int oIsoMm, int oIsoDd, int oUsaMm, int oUsaDd, int oUsaCc, int oUsaYy, int oEurDd, int oEurMm, int oEurCc, int oEurYy, int oJisCc, int oJisYy, int oJisMm, int oJisDd, int oMdySlshMm, int oMdySlshDd, int oMdySlshYy, int oMdyMm, int oMdyDd, int oMdyYy, int oMdcySlshMm, int oMdcySlshDd, int oMdcySlshCc, int oMdcySlshYy, int oMdcyMm, int oMdcyDd, int oMdcyCc, int oMdcyYy, int oYmdYy, int oYmdMm, int oYmdDd, int oCymdCc, int oCymdYy, int oCymdMm, int oCymdDd, int oJulYy, int oJulDdd, String oExpMm, int oExpDd, int oExpCc, int oExpYy, int differenceInDays, int oDayNumber, String reqCountry, OutputNextMoYr outputNextMoYr, OutputLastMoYr outputLastMoYr, String i1UsaMm, String i1UsaDd, String i1UsaCc, String i1UsaYy) {
        super(reqDay, manipulateReqType, numberOfDays, numberOfMonths, inputDateType, oIsoCc, oIsoYy, oIsoMm, oIsoDd, oUsaMm, oUsaDd, oUsaCc, oUsaYy, oEurDd, oEurMm, oEurCc, oEurYy, oJisCc, oJisYy, oJisMm, oJisDd, oMdySlshMm, oMdySlshDd, oMdySlshYy, oMdyMm, oMdyDd, oMdyYy, oMdcySlshMm, oMdcySlshDd, oMdcySlshCc, oMdcySlshYy, oMdcyMm, oMdcyDd, oMdcyCc, oMdcyYy, oYmdYy, oYmdMm, oYmdDd, oCymdCc, oCymdYy, oCymdMm, oCymdDd, oJulYy, oJulDdd, oExpMm, oExpDd, oExpCc, oExpYy, differenceInDays, oDayNumber, reqCountry, outputNextMoYr, outputLastMoYr);
        this.i1UsaMm = i1UsaMm;
        this.i1UsaDd = i1UsaDd;
        this.i1UsaCc = i1UsaCc;
        this.i1UsaYy = i1UsaYy;
    }
    
    @Override
    public I1UsaDate clone() throws CloneNotSupportedException {
        I1UsaDate cloned = (I1UsaDate) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code I1UsaDate} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected I1UsaDate(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code I1UsaDate} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected I1UsaDate(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I1UsaDate} object
     * @see #setBytes(byte[], int)
     */
    public static I1UsaDate fromBytes(byte[] bytes, int offset) {
        return new I1UsaDate(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I1UsaDate} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static I1UsaDate fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code I1UsaDate} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static I1UsaDate fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getI1UsaMm() {
        return this.i1UsaMm;
    }
    
    public void setI1UsaMm(String i1UsaMm) {
        this.i1UsaMm = i1UsaMm;
    }
    
    public String getI1UsaDd() {
        return this.i1UsaDd;
    }
    
    public void setI1UsaDd(String i1UsaDd) {
        this.i1UsaDd = i1UsaDd;
    }
    
    public String getI1UsaCc() {
        return this.i1UsaCc;
    }
    
    public void setI1UsaCc(String i1UsaCc) {
        this.i1UsaCc = i1UsaCc;
    }
    
    public String getI1UsaYy() {
        return this.i1UsaYy;
    }
    
    public void setI1UsaYy(String i1UsaYy) {
        this.i1UsaYy = i1UsaYy;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        i1UsaMm = "";
        i1UsaDd = "";
        i1UsaCc = "";
        i1UsaYy = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ i1UsaMm=\"");
        s.append(getI1UsaMm());
        s.append("\"");
        s.append(", filler27=\"");
        s.append(new String(filler27, encoding));
        s.append("\"");
        s.append(", i1UsaDd=\"");
        s.append(getI1UsaDd());
        s.append("\"");
        s.append(", filler28=\"");
        s.append(new String(filler28, encoding));
        s.append("\"");
        s.append(", i1UsaCc=\"");
        s.append(getI1UsaCc());
        s.append("\"");
        s.append(", i1UsaYy=\"");
        s.append(getI1UsaYy());
        s.append("\"");
        s.append(", filler29=\"");
        s.append(new String(filler29, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(I1UsaDate that) {
        return super.equals(that) &&
            this.i1UsaMm.equals(that.i1UsaMm) &&
            Arrays.equals(this.filler27, that.filler27) &&
            this.i1UsaDd.equals(that.i1UsaDd) &&
            Arrays.equals(this.filler28, that.filler28) &&
            this.i1UsaCc.equals(that.i1UsaCc) &&
            this.i1UsaYy.equals(that.i1UsaYy) &&
            Arrays.equals(this.filler29, that.filler29);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof I1UsaDate other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof I1UsaDate;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(i1UsaMm);
        result = 31 * result + Arrays.hashCode(filler27);
        result = 31 * result + Objects.hashCode(i1UsaDd);
        result = 31 * result + Arrays.hashCode(filler28);
        result = 31 * result + Objects.hashCode(i1UsaCc);
        result = 31 * result + Objects.hashCode(i1UsaYy);
        result = 31 * result + Arrays.hashCode(filler29);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(I1UsaDate that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.i1UsaMm.compareTo(that.i1UsaMm);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler27, that.filler27);
        if ( c != 0 ) return c;
        c = this.i1UsaDd.compareTo(that.i1UsaDd);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler28, that.filler28);
        if ( c != 0 ) return c;
        c = this.i1UsaCc.compareTo(that.i1UsaCc);
        if ( c != 0 ) return c;
        c = this.i1UsaYy.compareTo(that.i1UsaYy);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler29, that.filler29);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(DateParmArea that) {
        if (that instanceof I1UsaDate other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(DateParmArea.SIZE);
    }
    
    private static final StringField I_1_USA_MM = factory.getStringField(2);
    private static final ByteArrayField FILLER_27 = factory.getByteArrayField(1);
    private byte[] filler27 = new byte[1];
    private static final StringField I_1_USA_DD = factory.getStringField(2);
    private static final ByteArrayField FILLER_28 = factory.getByteArrayField(1);
    private byte[] filler28 = new byte[1];
    private static final StringField I_1_USA_CC = factory.getStringField(2);
    private static final StringField I_1_USA_YY = factory.getStringField(2);
    private static final ByteArrayField FILLER_29 = factory.getByteArrayField(2);
    private byte[] filler29 = new byte[2];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "I1-USA-DATE record at MXWW01.CPY:101"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        I_1_USA_MM.putString(i1UsaMm, bytes, offset);
        FILLER_27.putByteArray(filler27, bytes, offset);
        I_1_USA_DD.putString(i1UsaDd, bytes, offset);
        FILLER_28.putByteArray(filler28, bytes, offset);
        I_1_USA_CC.putString(i1UsaCc, bytes, offset);
        I_1_USA_YY.putString(i1UsaYy, bytes, offset);
        FILLER_29.putByteArray(filler29, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#setBytes(byte[], int)} to set parent-class state.
     * @see "I1-USA-DATE record at MXWW01.CPY:101"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        i1UsaMm = I_1_USA_MM.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_27.getByteArray(bytes, offset);
        i1UsaDd = I_1_USA_DD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_28.getByteArray(bytes, offset);
        i1UsaCc = I_1_USA_CC.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        i1UsaYy = I_1_USA_YY.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_29.getByteArray(bytes, offset);
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
