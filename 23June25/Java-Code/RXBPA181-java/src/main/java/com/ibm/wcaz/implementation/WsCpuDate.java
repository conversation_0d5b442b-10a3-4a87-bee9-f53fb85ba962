package com.ibm.wcaz.implementation;

import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;

public class WsCpuDate implements Cloneable, Comparable<WsCpuDate> {
    private static final Charset encoding = Charset.forName("IBM-1047");

    private String wsCpuCcyy = "";
    private char wsDtSep1 = ' ';
    private String wsCpuMm = "";
    private char wsDtSep2 = ' ';
    private String wsCpuDd = "";

    /** Initialize fields to non-null default values */
    public WsCpuDate() {}

    /** Initialize all fields to provided values */
    public WsCpuDate(String wsCpuCcyy, char wsDtSep1, String wsCpuMm, char wsDtSep2, String wsCpuDd) {
        this.wsCpuCcyy = wsCpuCcyy;
        this.wsDtSep1 = wsDtSep1;
        this.wsCpuMm = wsCpuMm;
        this.wsDtSep2 = wsDtSep2;
        this.wsCpuDd = wsCpuDd;
    }

    @Override
    public WsCpuDate clone() throws CloneNotSupportedException {
        WsCpuDate cloned = (WsCpuDate) super.clone();
        return cloned;
    }

    /**
     * Initialize {@code WsCpuDate} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected WsCpuDate(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }

    /**
     * Initialize {@code WsCpuDate} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected WsCpuDate(byte[] bytes) {
        this(bytes, 0);
    }

    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsCpuDate} object
     * @see #setBytes(byte[], int)
     */
    public static WsCpuDate fromBytes(byte[] bytes, int offset) {
        return new WsCpuDate(bytes, offset);
    }

    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsCpuDate} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static WsCpuDate fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }

    /**
     * Deserialize the COBOL-formatted string into a new {@code WsCpuDate} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static WsCpuDate fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }

    public String getWsCpuCcyy() {
        return this.wsCpuCcyy;
    }

    public void setWsCpuCcyy(String wsCpuCcyy) {
        this.wsCpuCcyy = wsCpuCcyy;
    }

    public char getWsDtSep1() {
        return this.wsDtSep1;
    }

    public void setWsDtSep1(char wsDtSep1) {
        this.wsDtSep1 = wsDtSep1;
    }

    public String getWsCpuMm() {
        return this.wsCpuMm;
    }

    public void setWsCpuMm(String wsCpuMm) {
        this.wsCpuMm = wsCpuMm;
    }

    public char getWsDtSep2() {
        return this.wsDtSep2;
    }

    public void setWsDtSep2(char wsDtSep2) {
        this.wsDtSep2 = wsDtSep2;
    }

    public String getWsCpuDd() {
        return this.wsCpuDd;
    }

    public void setWsCpuDd(String wsCpuDd) {
        this.wsCpuDd = wsCpuDd;
    }

    /**
     * Get the complete CPU date as a formatted string
     * @return the complete date in CCYY-MM-DD format
     */
    public String getWsCpuDate() {
        return wsCpuCcyy + wsDtSep1 + wsCpuMm + wsDtSep2 + wsCpuDd;
    }

    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        wsCpuCcyy = "";
        wsDtSep1 = ' ';
        wsCpuMm = "";
        wsDtSep2 = ' ';
        wsCpuDd = "";
    }

    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ wsCpuCcyy=\"");
        s.append(getWsCpuCcyy());
        s.append("\"");
        s.append(", wsDtSep1=\"");
        s.append(getWsDtSep1());
        s.append("\"");
        s.append(", wsCpuMm=\"");
        s.append(getWsCpuMm());
        s.append("\"");
        s.append(", wsDtSep2=\"");
        s.append(getWsDtSep2());
        s.append("\"");
        s.append(", wsCpuDd=\"");
        s.append(getWsCpuDd());
        s.append("\"");
        s.append("}");
        return s.toString();
    }

    private boolean equals(WsCpuDate that) {
        return this.wsCpuCcyy.equals(that.wsCpuCcyy) &&
            this.wsDtSep1 == that.wsDtSep1 &&
            this.wsCpuMm.equals(that.wsCpuMm) &&
            this.wsDtSep2 == that.wsDtSep2 &&
            this.wsCpuDd.equals(that.wsCpuDd);
    }

    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof WsCpuDate other) && other.canEqual(this) && this.equals(other);
    }

    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof WsCpuDate;
    }

    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Objects.hashCode(wsCpuCcyy);
        result = 31 * result + Character.hashCode(wsDtSep1);
        result = 31 * result + Objects.hashCode(wsCpuMm);
        result = 31 * result + Character.hashCode(wsDtSep2);
        result = 31 * result + Objects.hashCode(wsCpuDd);
        return result;
    }

    @Override
    public int compareTo(WsCpuDate that) {
        int c = 0;
        c = this.wsCpuCcyy.compareTo(that.wsCpuCcyy);
        if ( c != 0 ) return c;
        c = Character.compare(this.wsDtSep1, that.wsDtSep1);
        if ( c != 0 ) return c;
        c = this.wsCpuMm.compareTo(that.wsCpuMm);
        if ( c != 0 ) return c;
        c = Character.compare(this.wsDtSep2, that.wsDtSep2);
        if ( c != 0 ) return c;
        c = this.wsCpuDd.compareTo(that.wsCpuDd);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }

    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }

    private static final StringField WS_CPU_CCYY = factory.getStringField(4);
    private static final StringField WS_DT_SEP_1 = factory.getStringField(1);
    private static final StringField WS_CPU_MM = factory.getStringField(2);
    private static final StringField WS_DT_SEP_2 = factory.getStringField(1);
    private static final StringField WS_CPU_DD = factory.getStringField(2);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata

    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsCpuDate} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code WS-CPU-DATE} record
     * @see "WS-CPU-DATE record at RXBPASVC.cbl:119"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        WS_CPU_CCYY.putString(wsCpuCcyy, bytes, offset);
        WS_DT_SEP_1.putString(Character.toString(wsDtSep1), bytes, offset);
        WS_CPU_MM.putString(wsCpuMm, bytes, offset);
        WS_DT_SEP_2.putString(Character.toString(wsDtSep2), bytes, offset);
        WS_CPU_DD.putString(wsCpuDd, bytes, offset);
        return bytes;
    }

    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsCpuDate} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }

    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsCpuDate} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }

    /**
     * Retrieves a COBOL-format string representation of the {@code WsCpuDate} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }

    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code WS-CPU-DATE} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "WS-CPU-DATE record at RXBPASVC.cbl:119"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        wsCpuCcyy = WS_CPU_CCYY.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        wsDtSep1 = WS_DT_SEP_1.getString(bytes, offset).charAt(0);
        wsCpuMm = WS_CPU_MM.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        wsDtSep2 = WS_DT_SEP_2.getString(bytes, offset).charAt(0);
        wsCpuDd = WS_CPU_DD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
    }


    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }

    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }

    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }

}
