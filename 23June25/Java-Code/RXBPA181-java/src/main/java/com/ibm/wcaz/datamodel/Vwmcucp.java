package com.ibm.wcaz.datamodel;

import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.ExternalDecimalAsIntField;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.sql.Date;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Objects;

public class Vwmcucp implements Cloneable, Comparable<Vwmcucp> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private int custNo;
    private String cpuId = "";
    private String cpuCode = "";
    private String cpuDealerNo = "";
    private int branchNo;
    private int buyGrpNo;
    private int distLocNo;
    private int dlrLocNo;
    private int mfgLocNo;
    private String explodeInd = "";
    private Date holdCredDate = new Date(0);
    private String prodCode = "";
    private String specInvoiceFlag = "";
    private String titleCode = "";
    private int modtermCode;
    private int ovrdModtermCode;
    private String serviceFeeType = "";
    private int serviceFeeRate;
    private int serviceFeeAmt;
    private String masterDlrFlag = "";
    private String lastUpdLogonId = "";
    private Date lastUpdDate = new Date(0);
    private Date auditTs = new Date(0);
    private String auditLogonId = "";
    private String auditProcessTx = "";
    private String auditDeleteFl = "";
    private String otbAuctionFl = "";
    
    /** Initialize fields to non-null default values */
    public Vwmcucp() {}
    
    /** Initialize all fields to provided values */
    public Vwmcucp(int custNo, String cpuId, String cpuCode, String cpuDealerNo, int branchNo, int buyGrpNo, int distLocNo, int dlrLocNo, int mfgLocNo, String explodeInd, Date holdCredDate, String prodCode, String specInvoiceFlag, String titleCode, int modtermCode, int ovrdModtermCode, String serviceFeeType, int serviceFeeRate, int serviceFeeAmt, String masterDlrFlag, String lastUpdLogonId, Date lastUpdDate, Date auditTs, String auditLogonId, String auditProcessTx, String auditDeleteFl, String otbAuctionFl) {
        this.custNo = custNo;
        this.cpuId = cpuId;
        this.cpuCode = cpuCode;
        this.cpuDealerNo = cpuDealerNo;
        this.branchNo = branchNo;
        this.buyGrpNo = buyGrpNo;
        this.distLocNo = distLocNo;
        this.dlrLocNo = dlrLocNo;
        this.mfgLocNo = mfgLocNo;
        this.explodeInd = explodeInd;
        this.holdCredDate = holdCredDate;
        this.prodCode = prodCode;
        this.specInvoiceFlag = specInvoiceFlag;
        this.titleCode = titleCode;
        this.modtermCode = modtermCode;
        this.ovrdModtermCode = ovrdModtermCode;
        this.serviceFeeType = serviceFeeType;
        this.serviceFeeRate = serviceFeeRate;
        this.serviceFeeAmt = serviceFeeAmt;
        this.masterDlrFlag = masterDlrFlag;
        this.lastUpdLogonId = lastUpdLogonId;
        this.lastUpdDate = lastUpdDate;
        this.auditTs = auditTs;
        this.auditLogonId = auditLogonId;
        this.auditProcessTx = auditProcessTx;
        this.auditDeleteFl = auditDeleteFl;
        this.otbAuctionFl = otbAuctionFl;
    }
    
    @Override
    public Vwmcucp clone() throws CloneNotSupportedException {
        Vwmcucp cloned = (Vwmcucp) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code Vwmcucp} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected Vwmcucp(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code Vwmcucp} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected Vwmcucp(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code Vwmcucp} object
     * @see #setBytes(byte[], int)
     */
    public static Vwmcucp fromBytes(byte[] bytes, int offset) {
        return new Vwmcucp(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code Vwmcucp} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static Vwmcucp fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code Vwmcucp} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static Vwmcucp fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public int getCustNo() {
        return this.custNo;
    }
    
    public void setCustNo(int custNo) {
        this.custNo = custNo;
    }
    
    public String getCpuId() {
        return this.cpuId;
    }
    
    public void setCpuId(String cpuId) {
        this.cpuId = cpuId;
    }
    
    public String getCpuCode() {
        return this.cpuCode;
    }
    
    public void setCpuCode(String cpuCode) {
        this.cpuCode = cpuCode;
    }
    
    public String getCpuDealerNo() {
        return this.cpuDealerNo;
    }
    
    public void setCpuDealerNo(String cpuDealerNo) {
        this.cpuDealerNo = cpuDealerNo;
    }
    
    public int getBranchNo() {
        return this.branchNo;
    }
    
    public void setBranchNo(int branchNo) {
        this.branchNo = branchNo;
    }
    
    public int getBuyGrpNo() {
        return this.buyGrpNo;
    }
    
    public void setBuyGrpNo(int buyGrpNo) {
        this.buyGrpNo = buyGrpNo;
    }
    
    public int getDistLocNo() {
        return this.distLocNo;
    }
    
    public void setDistLocNo(int distLocNo) {
        this.distLocNo = distLocNo;
    }
    
    public int getDlrLocNo() {
        return this.dlrLocNo;
    }
    
    public void setDlrLocNo(int dlrLocNo) {
        this.dlrLocNo = dlrLocNo;
    }
    
    public int getMfgLocNo() {
        return this.mfgLocNo;
    }
    
    public void setMfgLocNo(int mfgLocNo) {
        this.mfgLocNo = mfgLocNo;
    }
    
    public String getExplodeInd() {
        return this.explodeInd;
    }
    
    public void setExplodeInd(String explodeInd) {
        this.explodeInd = explodeInd;
    }
    
    public Date getHoldCredDate() {
        return this.holdCredDate;
    }
    
    public void setHoldCredDate(Date holdCredDate) {
        this.holdCredDate = holdCredDate;
    }
    
    public String getProdCode() {
        return this.prodCode;
    }
    
    public void setProdCode(String prodCode) {
        this.prodCode = prodCode;
    }
    
    public String getSpecInvoiceFlag() {
        return this.specInvoiceFlag;
    }
    
    public void setSpecInvoiceFlag(String specInvoiceFlag) {
        this.specInvoiceFlag = specInvoiceFlag;
    }
    
    public String getTitleCode() {
        return this.titleCode;
    }
    
    public void setTitleCode(String titleCode) {
        this.titleCode = titleCode;
    }
    
    public int getModtermCode() {
        return this.modtermCode;
    }
    
    public void setModtermCode(int modtermCode) {
        this.modtermCode = modtermCode;
    }
    
    public int getOvrdModtermCode() {
        return this.ovrdModtermCode;
    }
    
    public void setOvrdModtermCode(int ovrdModtermCode) {
        this.ovrdModtermCode = ovrdModtermCode;
    }
    
    public String getServiceFeeType() {
        return this.serviceFeeType;
    }
    
    public void setServiceFeeType(String serviceFeeType) {
        this.serviceFeeType = serviceFeeType;
    }
    
    public int getServiceFeeRate() {
        return this.serviceFeeRate;
    }
    
    public void setServiceFeeRate(int serviceFeeRate) {
        this.serviceFeeRate = serviceFeeRate;
    }
    
    public int getServiceFeeAmt() {
        return this.serviceFeeAmt;
    }
    
    public void setServiceFeeAmt(int serviceFeeAmt) {
        this.serviceFeeAmt = serviceFeeAmt;
    }
    
    public String getMasterDlrFlag() {
        return this.masterDlrFlag;
    }
    
    public void setMasterDlrFlag(String masterDlrFlag) {
        this.masterDlrFlag = masterDlrFlag;
    }
    
    public String getLastUpdLogonId() {
        return this.lastUpdLogonId;
    }
    
    public void setLastUpdLogonId(String lastUpdLogonId) {
        this.lastUpdLogonId = lastUpdLogonId;
    }
    
    public Date getLastUpdDate() {
        return this.lastUpdDate;
    }
    
    public void setLastUpdDate(Date lastUpdDate) {
        this.lastUpdDate = lastUpdDate;
    }
    
    public Date getAuditTs() {
        return this.auditTs;
    }
    
    public void setAuditTs(Date auditTs) {
        this.auditTs = auditTs;
    }
    
    public String getAuditLogonId() {
        return this.auditLogonId;
    }
    
    public void setAuditLogonId(String auditLogonId) {
        this.auditLogonId = auditLogonId;
    }
    
    public String getAuditProcessTx() {
        return this.auditProcessTx;
    }
    
    public void setAuditProcessTx(String auditProcessTx) {
        this.auditProcessTx = auditProcessTx;
    }
    
    public String getAuditDeleteFl() {
        return this.auditDeleteFl;
    }
    
    public void setAuditDeleteFl(String auditDeleteFl) {
        this.auditDeleteFl = auditDeleteFl;
    }
    
    public String getOtbAuctionFl() {
        return this.otbAuctionFl;
    }
    
    public void setOtbAuctionFl(String otbAuctionFl) {
        this.otbAuctionFl = otbAuctionFl;
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ custNo=\"");
        s.append(getCustNo());
        s.append("\"");
        s.append(", cpuId=\"");
        s.append(getCpuId());
        s.append("\"");
        s.append(", cpuCode=\"");
        s.append(getCpuCode());
        s.append("\"");
        s.append(", cpuDealerNo=\"");
        s.append(getCpuDealerNo());
        s.append("\"");
        s.append(", branchNo=\"");
        s.append(getBranchNo());
        s.append("\"");
        s.append(", buyGrpNo=\"");
        s.append(getBuyGrpNo());
        s.append("\"");
        s.append(", distLocNo=\"");
        s.append(getDistLocNo());
        s.append("\"");
        s.append(", dlrLocNo=\"");
        s.append(getDlrLocNo());
        s.append("\"");
        s.append(", mfgLocNo=\"");
        s.append(getMfgLocNo());
        s.append("\"");
        s.append(", explodeInd=\"");
        s.append(getExplodeInd());
        s.append("\"");
        s.append(", holdCredDate=\"");
        s.append(getHoldCredDate());
        s.append("\"");
        s.append(", prodCode=\"");
        s.append(getProdCode());
        s.append("\"");
        s.append(", specInvoiceFlag=\"");
        s.append(getSpecInvoiceFlag());
        s.append("\"");
        s.append(", titleCode=\"");
        s.append(getTitleCode());
        s.append("\"");
        s.append(", modtermCode=\"");
        s.append(getModtermCode());
        s.append("\"");
        s.append(", ovrdModtermCode=\"");
        s.append(getOvrdModtermCode());
        s.append("\"");
        s.append(", serviceFeeType=\"");
        s.append(getServiceFeeType());
        s.append("\"");
        s.append(", serviceFeeRate=\"");
        s.append(getServiceFeeRate());
        s.append("\"");
        s.append(", serviceFeeAmt=\"");
        s.append(getServiceFeeAmt());
        s.append("\"");
        s.append(", masterDlrFlag=\"");
        s.append(getMasterDlrFlag());
        s.append("\"");
        s.append(", lastUpdLogonId=\"");
        s.append(getLastUpdLogonId());
        s.append("\"");
        s.append(", lastUpdDate=\"");
        s.append(getLastUpdDate());
        s.append("\"");
        s.append(", auditTs=\"");
        s.append(getAuditTs());
        s.append("\"");
        s.append(", auditLogonId=\"");
        s.append(getAuditLogonId());
        s.append("\"");
        s.append(", auditProcessTx=\"");
        s.append(getAuditProcessTx());
        s.append("\"");
        s.append(", auditDeleteFl=\"");
        s.append(getAuditDeleteFl());
        s.append("\"");
        s.append(", otbAuctionFl=\"");
        s.append(getOtbAuctionFl());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(Vwmcucp that) {
        return this.custNo == that.custNo &&
            this.cpuId.equals(that.cpuId) &&
            this.cpuCode.equals(that.cpuCode) &&
            this.cpuDealerNo.equals(that.cpuDealerNo) &&
            this.branchNo == that.branchNo &&
            this.buyGrpNo == that.buyGrpNo &&
            this.distLocNo == that.distLocNo &&
            this.dlrLocNo == that.dlrLocNo &&
            this.mfgLocNo == that.mfgLocNo &&
            this.explodeInd.equals(that.explodeInd) &&
            this.holdCredDate.equals(that.holdCredDate) &&
            this.prodCode.equals(that.prodCode) &&
            this.specInvoiceFlag.equals(that.specInvoiceFlag) &&
            this.titleCode.equals(that.titleCode) &&
            this.modtermCode == that.modtermCode &&
            this.ovrdModtermCode == that.ovrdModtermCode &&
            this.serviceFeeType.equals(that.serviceFeeType) &&
            this.serviceFeeRate == that.serviceFeeRate &&
            this.serviceFeeAmt == that.serviceFeeAmt &&
            this.masterDlrFlag.equals(that.masterDlrFlag) &&
            this.lastUpdLogonId.equals(that.lastUpdLogonId) &&
            this.lastUpdDate.equals(that.lastUpdDate) &&
            this.auditTs.equals(that.auditTs) &&
            this.auditLogonId.equals(that.auditLogonId) &&
            this.auditProcessTx.equals(that.auditProcessTx) &&
            this.auditDeleteFl.equals(that.auditDeleteFl) &&
            this.otbAuctionFl.equals(that.otbAuctionFl);
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof Vwmcucp other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof Vwmcucp;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Integer.hashCode(custNo);
        result = 31 * result + Objects.hashCode(cpuId);
        result = 31 * result + Objects.hashCode(cpuCode);
        result = 31 * result + Objects.hashCode(cpuDealerNo);
        result = 31 * result + Integer.hashCode(branchNo);
        result = 31 * result + Integer.hashCode(buyGrpNo);
        result = 31 * result + Integer.hashCode(distLocNo);
        result = 31 * result + Integer.hashCode(dlrLocNo);
        result = 31 * result + Integer.hashCode(mfgLocNo);
        result = 31 * result + Objects.hashCode(explodeInd);
        result = 31 * result + Objects.hashCode(holdCredDate);
        result = 31 * result + Objects.hashCode(prodCode);
        result = 31 * result + Objects.hashCode(specInvoiceFlag);
        result = 31 * result + Objects.hashCode(titleCode);
        result = 31 * result + Integer.hashCode(modtermCode);
        result = 31 * result + Integer.hashCode(ovrdModtermCode);
        result = 31 * result + Objects.hashCode(serviceFeeType);
        result = 31 * result + Integer.hashCode(serviceFeeRate);
        result = 31 * result + Integer.hashCode(serviceFeeAmt);
        result = 31 * result + Objects.hashCode(masterDlrFlag);
        result = 31 * result + Objects.hashCode(lastUpdLogonId);
        result = 31 * result + Objects.hashCode(lastUpdDate);
        result = 31 * result + Objects.hashCode(auditTs);
        result = 31 * result + Objects.hashCode(auditLogonId);
        result = 31 * result + Objects.hashCode(auditProcessTx);
        result = 31 * result + Objects.hashCode(auditDeleteFl);
        result = 31 * result + Objects.hashCode(otbAuctionFl);
        return result;
    }
    
    @Override
    public int compareTo(Vwmcucp that) {
        int c = 0;
        c = Integer.compare(this.custNo, that.custNo);
        if ( c != 0 ) return c;
        c = this.cpuId.compareTo(that.cpuId);
        if ( c != 0 ) return c;
        c = this.cpuCode.compareTo(that.cpuCode);
        if ( c != 0 ) return c;
        c = this.cpuDealerNo.compareTo(that.cpuDealerNo);
        if ( c != 0 ) return c;
        c = Integer.compare(this.branchNo, that.branchNo);
        if ( c != 0 ) return c;
        c = Integer.compare(this.buyGrpNo, that.buyGrpNo);
        if ( c != 0 ) return c;
        c = Integer.compare(this.distLocNo, that.distLocNo);
        if ( c != 0 ) return c;
        c = Integer.compare(this.dlrLocNo, that.dlrLocNo);
        if ( c != 0 ) return c;
        c = Integer.compare(this.mfgLocNo, that.mfgLocNo);
        if ( c != 0 ) return c;
        c = this.explodeInd.compareTo(that.explodeInd);
        if ( c != 0 ) return c;
        c = this.holdCredDate.compareTo(that.holdCredDate);
        if ( c != 0 ) return c;
        c = this.prodCode.compareTo(that.prodCode);
        if ( c != 0 ) return c;
        c = this.specInvoiceFlag.compareTo(that.specInvoiceFlag);
        if ( c != 0 ) return c;
        c = this.titleCode.compareTo(that.titleCode);
        if ( c != 0 ) return c;
        c = Integer.compare(this.modtermCode, that.modtermCode);
        if ( c != 0 ) return c;
        c = Integer.compare(this.ovrdModtermCode, that.ovrdModtermCode);
        if ( c != 0 ) return c;
        c = this.serviceFeeType.compareTo(that.serviceFeeType);
        if ( c != 0 ) return c;
        c = Integer.compare(this.serviceFeeRate, that.serviceFeeRate);
        if ( c != 0 ) return c;
        c = Integer.compare(this.serviceFeeAmt, that.serviceFeeAmt);
        if ( c != 0 ) return c;
        c = this.masterDlrFlag.compareTo(that.masterDlrFlag);
        if ( c != 0 ) return c;
        c = this.lastUpdLogonId.compareTo(that.lastUpdLogonId);
        if ( c != 0 ) return c;
        c = this.lastUpdDate.compareTo(that.lastUpdDate);
        if ( c != 0 ) return c;
        c = this.auditTs.compareTo(that.auditTs);
        if ( c != 0 ) return c;
        c = this.auditLogonId.compareTo(that.auditLogonId);
        if ( c != 0 ) return c;
        c = this.auditProcessTx.compareTo(that.auditProcessTx);
        if ( c != 0 ) return c;
        c = this.auditDeleteFl.compareTo(that.auditDeleteFl);
        if ( c != 0 ) return c;
        c = this.otbAuctionFl.compareTo(that.otbAuctionFl);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final ExternalDecimalAsIntField CUST_NO = factory.getExternalDecimalAsIntField(5, true);
    private static final StringField CPU_ID = factory.getStringField(5);
    private static final StringField CPU_CODE = factory.getStringField(5);
    private static final StringField CPU_DEALER_NO = factory.getStringField(14);
    private static final ExternalDecimalAsIntField BRANCH_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField BUY_GRP_NO = factory.getExternalDecimalAsIntField(5, true);
    private static final ExternalDecimalAsIntField DIST_LOC_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField DLR_LOC_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField MFG_LOC_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField EXPLODE_IND = factory.getStringField(2);
    private static final StringField HOLD_CRED_DATE = factory.getStringField(8);
    private static final DateTimeFormatter HOLD_CRED_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField PROD_CODE = factory.getStringField(5);
    private static final StringField SPEC_INVOICE_FLAG = factory.getStringField(2);
    private static final StringField TITLE_CODE = factory.getStringField(3);
    private static final ExternalDecimalAsIntField MODTERM_CODE = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField OVRD_MODTERM_CODE = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField SERVICE_FEE_TYPE = factory.getStringField(2);
    private static final ExternalDecimalAsIntField SERVICE_FEE_RATE = factory.getExternalDecimalAsIntField(5, true);
    private static final ExternalDecimalAsIntField SERVICE_FEE_AMT = factory.getExternalDecimalAsIntField(5, true);
    private static final StringField MASTER_DLR_FLAG = factory.getStringField(2);
    private static final StringField LAST_UPD_LOGON_ID = factory.getStringField(9);
    private static final StringField LAST_UPD_DATE = factory.getStringField(8);
    private static final DateTimeFormatter LAST_UPD_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField AUDIT_TS = factory.getStringField(14);
    private static final DateTimeFormatter AUDIT_TS_FMT = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    private static final StringField AUDIT_LOGON_ID = factory.getStringField(9);
    private static final StringField AUDIT_PROCESS_TX = factory.getStringField(63);
    private static final StringField AUDIT_DELETE_FL = factory.getStringField(2);
    private static final StringField OTB_AUCTION_FL = factory.getStringField(2);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code Vwmcucp} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code VWMCUCP} record
     * @see "VWMCUCP record at VWMCUCP.CPY:8"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        CUST_NO.putInt(custNo, bytes, offset);
        CPU_ID.putString(cpuId, bytes, offset);
        CPU_CODE.putString(cpuCode, bytes, offset);
        CPU_DEALER_NO.putString(cpuDealerNo, bytes, offset);
        BRANCH_NO.putInt(branchNo, bytes, offset);
        BUY_GRP_NO.putInt(buyGrpNo, bytes, offset);
        DIST_LOC_NO.putInt(distLocNo, bytes, offset);
        DLR_LOC_NO.putInt(dlrLocNo, bytes, offset);
        MFG_LOC_NO.putInt(mfgLocNo, bytes, offset);
        EXPLODE_IND.putString(explodeInd, bytes, offset);
        HOLD_CRED_DATE.putString(holdCredDate.toLocalDate().format(HOLD_CRED_DATE_FMT), bytes, offset);
        PROD_CODE.putString(prodCode, bytes, offset);
        SPEC_INVOICE_FLAG.putString(specInvoiceFlag, bytes, offset);
        TITLE_CODE.putString(titleCode, bytes, offset);
        MODTERM_CODE.putInt(modtermCode, bytes, offset);
        OVRD_MODTERM_CODE.putInt(ovrdModtermCode, bytes, offset);
        SERVICE_FEE_TYPE.putString(serviceFeeType, bytes, offset);
        SERVICE_FEE_RATE.putInt(serviceFeeRate, bytes, offset);
        SERVICE_FEE_AMT.putInt(serviceFeeAmt, bytes, offset);
        MASTER_DLR_FLAG.putString(masterDlrFlag, bytes, offset);
        LAST_UPD_LOGON_ID.putString(lastUpdLogonId, bytes, offset);
        LAST_UPD_DATE.putString(lastUpdDate.toLocalDate().format(LAST_UPD_DATE_FMT), bytes, offset);
        AUDIT_TS.putString(auditTs.toLocalDate().format(AUDIT_TS_FMT), bytes, offset);
        AUDIT_LOGON_ID.putString(auditLogonId, bytes, offset);
        AUDIT_PROCESS_TX.putString(auditProcessTx, bytes, offset);
        AUDIT_DELETE_FL.putString(auditDeleteFl, bytes, offset);
        OTB_AUCTION_FL.putString(otbAuctionFl, bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code Vwmcucp} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code Vwmcucp} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code Vwmcucp} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code VWMCUCP} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "VWMCUCP record at VWMCUCP.CPY:8"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        custNo = CUST_NO.getInt(bytes, offset);
        cpuId = CPU_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        cpuCode = CPU_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        cpuDealerNo = CPU_DEALER_NO.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        branchNo = BRANCH_NO.getInt(bytes, offset);
        buyGrpNo = BUY_GRP_NO.getInt(bytes, offset);
        distLocNo = DIST_LOC_NO.getInt(bytes, offset);
        dlrLocNo = DLR_LOC_NO.getInt(bytes, offset);
        mfgLocNo = MFG_LOC_NO.getInt(bytes, offset);
        explodeInd = EXPLODE_IND.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        holdCredDate = Date.valueOf(LocalDate.parse(HOLD_CRED_DATE.getString(bytes, offset), HOLD_CRED_DATE_FMT));
        prodCode = PROD_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        specInvoiceFlag = SPEC_INVOICE_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        titleCode = TITLE_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        modtermCode = MODTERM_CODE.getInt(bytes, offset);
        ovrdModtermCode = OVRD_MODTERM_CODE.getInt(bytes, offset);
        serviceFeeType = SERVICE_FEE_TYPE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        serviceFeeRate = SERVICE_FEE_RATE.getInt(bytes, offset);
        serviceFeeAmt = SERVICE_FEE_AMT.getInt(bytes, offset);
        masterDlrFlag = MASTER_DLR_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        lastUpdLogonId = LAST_UPD_LOGON_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        lastUpdDate = Date.valueOf(LocalDate.parse(LAST_UPD_DATE.getString(bytes, offset), LAST_UPD_DATE_FMT));
        auditTs = Date.valueOf(LocalDate.parse(AUDIT_TS.getString(bytes, offset), AUDIT_TS_FMT));
        auditLogonId = AUDIT_LOGON_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        auditProcessTx = AUDIT_PROCESS_TX.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        auditDeleteFl = AUDIT_DELETE_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        otbAuctionFl = OTB_AUCTION_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
