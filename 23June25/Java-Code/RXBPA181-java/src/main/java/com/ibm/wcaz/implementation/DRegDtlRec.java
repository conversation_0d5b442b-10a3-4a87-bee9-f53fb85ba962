package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class DRegDtlRec extends CpuRegdtlLayout {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private static final String D_DTL_REC_VALUE = "URBD";
    
    private String dRecordType = "";
    private String dCdfCustId = "";
    private String dCdfCustCode = "";
    private String dCreatDate = "";
    private String dCreatTime = "";
    private String dVendDlrNo = "";
    private String dVendDlrName = "";
    private String dModelNbr = "";
    private String dModelDesc = "";
    private String dSerialNbr = "";
    private String dRegComplDate = "";
    private String dRegType = "";
    private String dDistNo = "";
    
    /** Initialize fields to non-null default values */
    public DRegDtlRec() {}
    
    /** Initialize all fields to provided values */
    public DRegDtlRec(String dRecordType, String dCdfCustId, String dCdfCustCode, String dCreatDate, String dCreatTime, String dVendDlrNo, String dVendDlrName, String dModelNbr, String dModelDesc, String dSerialNbr, String dRegComplDate, String dRegType, String dDistNo) {
        this.dRecordType = dRecordType;
        this.dCdfCustId = dCdfCustId;
        this.dCdfCustCode = dCdfCustCode;
        this.dCreatDate = dCreatDate;
        this.dCreatTime = dCreatTime;
        this.dVendDlrNo = dVendDlrNo;
        this.dVendDlrName = dVendDlrName;
        this.dModelNbr = dModelNbr;
        this.dModelDesc = dModelDesc;
        this.dSerialNbr = dSerialNbr;
        this.dRegComplDate = dRegComplDate;
        this.dRegType = dRegType;
        this.dDistNo = dDistNo;
    }
    
    @Override
    public DRegDtlRec clone() throws CloneNotSupportedException {
        DRegDtlRec cloned = (DRegDtlRec) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code DRegDtlRec} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected DRegDtlRec(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code DRegDtlRec} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected DRegDtlRec(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code DRegDtlRec} object
     * @see #setBytes(byte[], int)
     */
    public static DRegDtlRec fromBytes(byte[] bytes, int offset) {
        return new DRegDtlRec(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code DRegDtlRec} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static DRegDtlRec fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code DRegDtlRec} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static DRegDtlRec fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getDRecordType() {
        return this.dRecordType;
    }
    
    public void setDRecordType(String dRecordType) {
        this.dRecordType = dRecordType;
    }
    
    public boolean isDDtlRec() {
        return dRecordType.equals(D_DTL_REC_VALUE);
    }
    
    public void setDDtlRec() {
        dRecordType = D_DTL_REC_VALUE;
    }
    
    public String getDCdfCustId() {
        return this.dCdfCustId;
    }
    
    public void setDCdfCustId(String dCdfCustId) {
        this.dCdfCustId = dCdfCustId;
    }
    
    public String getDCdfCustCode() {
        return this.dCdfCustCode;
    }
    
    public void setDCdfCustCode(String dCdfCustCode) {
        this.dCdfCustCode = dCdfCustCode;
    }
    
    public String getDCreatDate() {
        return this.dCreatDate;
    }
    
    public void setDCreatDate(String dCreatDate) {
        this.dCreatDate = dCreatDate;
    }
    
    public String getDCreatTime() {
        return this.dCreatTime;
    }
    
    public void setDCreatTime(String dCreatTime) {
        this.dCreatTime = dCreatTime;
    }
    
    public String getDVendDlrNo() {
        return this.dVendDlrNo;
    }
    
    public void setDVendDlrNo(String dVendDlrNo) {
        this.dVendDlrNo = dVendDlrNo;
    }
    
    public String getDVendDlrName() {
        return this.dVendDlrName;
    }
    
    public void setDVendDlrName(String dVendDlrName) {
        this.dVendDlrName = dVendDlrName;
    }
    
    public String getDModelNbr() {
        return this.dModelNbr;
    }
    
    public void setDModelNbr(String dModelNbr) {
        this.dModelNbr = dModelNbr;
    }
    
    public String getDModelDesc() {
        return this.dModelDesc;
    }
    
    public void setDModelDesc(String dModelDesc) {
        this.dModelDesc = dModelDesc;
    }
    
    public String getDSerialNbr() {
        return this.dSerialNbr;
    }
    
    public void setDSerialNbr(String dSerialNbr) {
        this.dSerialNbr = dSerialNbr;
    }
    
    public String getDRegComplDate() {
        return this.dRegComplDate;
    }
    
    public void setDRegComplDate(String dRegComplDate) {
        this.dRegComplDate = dRegComplDate;
    }
    
    public String getDRegType() {
        return this.dRegType;
    }
    
    public void setDRegType(String dRegType) {
        this.dRegType = dRegType;
    }
    
    public String getDDistNo() {
        return this.dDistNo;
    }
    
    public void setDDistNo(String dDistNo) {
        this.dDistNo = dDistNo;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        dRecordType = "";
        dCdfCustId = "";
        dCdfCustCode = "";
        dCreatDate = "";
        dCreatTime = "";
        dVendDlrNo = "";
        dVendDlrName = "";
        dModelNbr = "";
        dModelDesc = "";
        dSerialNbr = "";
        dRegComplDate = "";
        dRegType = "";
        dDistNo = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ dRecordType=\"");
        s.append(getDRecordType());
        s.append("\"");
        s.append(", dCdfCustId=\"");
        s.append(getDCdfCustId());
        s.append("\"");
        s.append(", dCdfCustCode=\"");
        s.append(getDCdfCustCode());
        s.append("\"");
        s.append(", dCreatDate=\"");
        s.append(getDCreatDate());
        s.append("\"");
        s.append(", dCreatTime=\"");
        s.append(getDCreatTime());
        s.append("\"");
        s.append(", dVendDlrNo=\"");
        s.append(getDVendDlrNo());
        s.append("\"");
        s.append(", dVendDlrName=\"");
        s.append(getDVendDlrName());
        s.append("\"");
        s.append(", dModelNbr=\"");
        s.append(getDModelNbr());
        s.append("\"");
        s.append(", dModelDesc=\"");
        s.append(getDModelDesc());
        s.append("\"");
        s.append(", dSerialNbr=\"");
        s.append(getDSerialNbr());
        s.append("\"");
        s.append(", dRegComplDate=\"");
        s.append(getDRegComplDate());
        s.append("\"");
        s.append(", dRegType=\"");
        s.append(getDRegType());
        s.append("\"");
        s.append(", dDistNo=\"");
        s.append(getDDistNo());
        s.append("\"");
        s.append(", filler4=\"");
        s.append(new String(filler4, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(DRegDtlRec that) {
        return super.equals(that) &&
            this.dRecordType.equals(that.dRecordType) &&
            this.dCdfCustId.equals(that.dCdfCustId) &&
            this.dCdfCustCode.equals(that.dCdfCustCode) &&
            this.dCreatDate.equals(that.dCreatDate) &&
            this.dCreatTime.equals(that.dCreatTime) &&
            this.dVendDlrNo.equals(that.dVendDlrNo) &&
            this.dVendDlrName.equals(that.dVendDlrName) &&
            this.dModelNbr.equals(that.dModelNbr) &&
            this.dModelDesc.equals(that.dModelDesc) &&
            this.dSerialNbr.equals(that.dSerialNbr) &&
            this.dRegComplDate.equals(that.dRegComplDate) &&
            this.dRegType.equals(that.dRegType) &&
            this.dDistNo.equals(that.dDistNo) &&
            Arrays.equals(this.filler4, that.filler4);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof DRegDtlRec other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof DRegDtlRec;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(dRecordType);
        result = 31 * result + Objects.hashCode(dCdfCustId);
        result = 31 * result + Objects.hashCode(dCdfCustCode);
        result = 31 * result + Objects.hashCode(dCreatDate);
        result = 31 * result + Objects.hashCode(dCreatTime);
        result = 31 * result + Objects.hashCode(dVendDlrNo);
        result = 31 * result + Objects.hashCode(dVendDlrName);
        result = 31 * result + Objects.hashCode(dModelNbr);
        result = 31 * result + Objects.hashCode(dModelDesc);
        result = 31 * result + Objects.hashCode(dSerialNbr);
        result = 31 * result + Objects.hashCode(dRegComplDate);
        result = 31 * result + Objects.hashCode(dRegType);
        result = 31 * result + Objects.hashCode(dDistNo);
        result = 31 * result + Arrays.hashCode(filler4);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(DRegDtlRec that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.dRecordType.compareTo(that.dRecordType);
        if ( c != 0 ) return c;
        c = this.dCdfCustId.compareTo(that.dCdfCustId);
        if ( c != 0 ) return c;
        c = this.dCdfCustCode.compareTo(that.dCdfCustCode);
        if ( c != 0 ) return c;
        c = this.dCreatDate.compareTo(that.dCreatDate);
        if ( c != 0 ) return c;
        c = this.dCreatTime.compareTo(that.dCreatTime);
        if ( c != 0 ) return c;
        c = this.dVendDlrNo.compareTo(that.dVendDlrNo);
        if ( c != 0 ) return c;
        c = this.dVendDlrName.compareTo(that.dVendDlrName);
        if ( c != 0 ) return c;
        c = this.dModelNbr.compareTo(that.dModelNbr);
        if ( c != 0 ) return c;
        c = this.dModelDesc.compareTo(that.dModelDesc);
        if ( c != 0 ) return c;
        c = this.dSerialNbr.compareTo(that.dSerialNbr);
        if ( c != 0 ) return c;
        c = this.dRegComplDate.compareTo(that.dRegComplDate);
        if ( c != 0 ) return c;
        c = this.dRegType.compareTo(that.dRegType);
        if ( c != 0 ) return c;
        c = this.dDistNo.compareTo(that.dDistNo);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler4, that.filler4);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(CpuRegdtlLayout that) {
        if (that instanceof DRegDtlRec other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(CpuRegdtlLayout.SIZE);
    }
    
    private static final StringField D_RECORD_TYPE = factory.getStringField(4);
    private static final StringField D_CDF_CUST_ID = factory.getStringField(4);
    private static final StringField D_CDF_CUST_CODE = factory.getStringField(4);
    private static final StringField D_CREAT_DATE = factory.getStringField(10);
    private static final StringField D_CREAT_TIME = factory.getStringField(5);
    private static final StringField D_VEND_DLR_NO = factory.getStringField(13);
    private static final StringField D_VEND_DLR_NAME = factory.getStringField(35);
    private static final StringField D_MODEL_NBR = factory.getStringField(12);
    private static final StringField D_MODEL_DESC = factory.getStringField(20);
    private static final StringField D_SERIAL_NBR = factory.getStringField(17);
    private static final StringField D_REG_COMPL_DATE = factory.getStringField(10);
    private static final StringField D_REG_TYPE = factory.getStringField(3);
    private static final StringField D_DIST_NO = factory.getStringField(6);
    private static final ByteArrayField FILLER_4 = factory.getByteArrayField(57);
    private byte[] filler4 = new byte[57];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link CpuRegdtlLayout#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "D-REG-DTL-REC record at RXBPASVC.cbl:58"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        D_RECORD_TYPE.putString(dRecordType, bytes, offset);
        D_CDF_CUST_ID.putString(dCdfCustId, bytes, offset);
        D_CDF_CUST_CODE.putString(dCdfCustCode, bytes, offset);
        D_CREAT_DATE.putString(dCreatDate, bytes, offset);
        D_CREAT_TIME.putString(dCreatTime, bytes, offset);
        D_VEND_DLR_NO.putString(dVendDlrNo, bytes, offset);
        D_VEND_DLR_NAME.putString(dVendDlrName, bytes, offset);
        D_MODEL_NBR.putString(dModelNbr, bytes, offset);
        D_MODEL_DESC.putString(dModelDesc, bytes, offset);
        D_SERIAL_NBR.putString(dSerialNbr, bytes, offset);
        D_REG_COMPL_DATE.putString(dRegComplDate, bytes, offset);
        D_REG_TYPE.putString(dRegType, bytes, offset);
        D_DIST_NO.putString(dDistNo, bytes, offset);
        FILLER_4.putByteArray(filler4, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link CpuRegdtlLayout#setBytes(byte[], int)} to set parent-class state.
     * @see "D-REG-DTL-REC record at RXBPASVC.cbl:58"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        dRecordType = D_RECORD_TYPE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        dCdfCustId = D_CDF_CUST_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        dCdfCustCode = D_CDF_CUST_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        dCreatDate = D_CREAT_DATE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        dCreatTime = D_CREAT_TIME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        dVendDlrNo = D_VEND_DLR_NO.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        dVendDlrName = D_VEND_DLR_NAME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        dModelNbr = D_MODEL_NBR.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        dModelDesc = D_MODEL_DESC.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        dSerialNbr = D_SERIAL_NBR.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        dRegComplDate = D_REG_COMPL_DATE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        dRegType = D_REG_TYPE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        dDistNo = D_DIST_NO.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_4.getByteArray(bytes, offset);
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
