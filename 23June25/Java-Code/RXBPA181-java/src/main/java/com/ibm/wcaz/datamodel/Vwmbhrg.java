package com.ibm.wcaz.datamodel;

import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.ExternalDecimalAsIntField;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.sql.Date;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Objects;

public class Vwmbhrg implements Cloneable, Comparable<Vwmbhrg> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private Date procDate = new Date(0);
    private int fileSeqNo;
    private String fileRejDs = "";
    private int itemSeqNo;
    private String itemRejDs = "";
    private String inputMfgNo = "";
    private String inputDistNo = "";
    private String inputDlrNo = "";
    private String inputCpuId = "";
    private String inputCpuCode = "";
    private String inputCreateDate = "";
    private String inputCreateTime = "";
    private String inputMfgName = "";
    private String inputRecCnt = "";
    private String cpuDlrNo = "";
    private String custName = "";
    private String modelNo = "";
    private String modelDs = "";
    private String serialNo = "";
    private String regComplDt = "";
    private String regTypeCd = "";
    private Date vehRegDt = new Date(0);
    private String vehRegMarkTx = "";
    private String mfgMakeDs = "";
    private int prevOwnerCnt;
    private Date prevOwnerChngDt = new Date(0);
    private String finSourceDesc = "";
    private String finSrcPhoneNo = "";
    private String finAgreeTx = "";
    private String finAgreeTypeTx = "";
    private Date finAgreeDt = new Date(0);
    private String finAgreeTermTx = "";
    
    /** Initialize fields to non-null default values */
    public Vwmbhrg() {}
    
    /** Initialize all fields to provided values */
    public Vwmbhrg(Date procDate, int fileSeqNo, String fileRejDs, int itemSeqNo, String itemRejDs, String inputMfgNo, String inputDistNo, String inputDlrNo, String inputCpuId, String inputCpuCode, String inputCreateDate, String inputCreateTime, String inputMfgName, String inputRecCnt, String cpuDlrNo, String custName, String modelNo, String modelDs, String serialNo, String regComplDt, String regTypeCd, Date vehRegDt, String vehRegMarkTx, String mfgMakeDs, int prevOwnerCnt, Date prevOwnerChngDt, String finSourceDesc, String finSrcPhoneNo, String finAgreeTx, String finAgreeTypeTx, Date finAgreeDt, String finAgreeTermTx) {
        this.procDate = procDate;
        this.fileSeqNo = fileSeqNo;
        this.fileRejDs = fileRejDs;
        this.itemSeqNo = itemSeqNo;
        this.itemRejDs = itemRejDs;
        this.inputMfgNo = inputMfgNo;
        this.inputDistNo = inputDistNo;
        this.inputDlrNo = inputDlrNo;
        this.inputCpuId = inputCpuId;
        this.inputCpuCode = inputCpuCode;
        this.inputCreateDate = inputCreateDate;
        this.inputCreateTime = inputCreateTime;
        this.inputMfgName = inputMfgName;
        this.inputRecCnt = inputRecCnt;
        this.cpuDlrNo = cpuDlrNo;
        this.custName = custName;
        this.modelNo = modelNo;
        this.modelDs = modelDs;
        this.serialNo = serialNo;
        this.regComplDt = regComplDt;
        this.regTypeCd = regTypeCd;
        this.vehRegDt = vehRegDt;
        this.vehRegMarkTx = vehRegMarkTx;
        this.mfgMakeDs = mfgMakeDs;
        this.prevOwnerCnt = prevOwnerCnt;
        this.prevOwnerChngDt = prevOwnerChngDt;
        this.finSourceDesc = finSourceDesc;
        this.finSrcPhoneNo = finSrcPhoneNo;
        this.finAgreeTx = finAgreeTx;
        this.finAgreeTypeTx = finAgreeTypeTx;
        this.finAgreeDt = finAgreeDt;
        this.finAgreeTermTx = finAgreeTermTx;
    }
    
    @Override
    public Vwmbhrg clone() throws CloneNotSupportedException {
        Vwmbhrg cloned = (Vwmbhrg) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code Vwmbhrg} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected Vwmbhrg(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code Vwmbhrg} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected Vwmbhrg(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code Vwmbhrg} object
     * @see #setBytes(byte[], int)
     */
    public static Vwmbhrg fromBytes(byte[] bytes, int offset) {
        return new Vwmbhrg(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code Vwmbhrg} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static Vwmbhrg fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code Vwmbhrg} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static Vwmbhrg fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public Date getProcDate() {
        return this.procDate;
    }
    
    public void setProcDate(Date procDate) {
        this.procDate = procDate;
    }
    
    public int getFileSeqNo() {
        return this.fileSeqNo;
    }
    
    public void setFileSeqNo(int fileSeqNo) {
        this.fileSeqNo = fileSeqNo;
    }
    
    public String getFileRejDs() {
        return this.fileRejDs;
    }
    
    public void setFileRejDs(String fileRejDs) {
        this.fileRejDs = fileRejDs;
    }
    
    public int getItemSeqNo() {
        return this.itemSeqNo;
    }
    
    public void setItemSeqNo(int itemSeqNo) {
        this.itemSeqNo = itemSeqNo;
    }
    
    public String getItemRejDs() {
        return this.itemRejDs;
    }
    
    public void setItemRejDs(String itemRejDs) {
        this.itemRejDs = itemRejDs;
    }
    
    public String getInputMfgNo() {
        return this.inputMfgNo;
    }
    
    public void setInputMfgNo(String inputMfgNo) {
        this.inputMfgNo = inputMfgNo;
    }
    
    public String getInputDistNo() {
        return this.inputDistNo;
    }
    
    public void setInputDistNo(String inputDistNo) {
        this.inputDistNo = inputDistNo;
    }
    
    public String getInputDlrNo() {
        return this.inputDlrNo;
    }
    
    public void setInputDlrNo(String inputDlrNo) {
        this.inputDlrNo = inputDlrNo;
    }
    
    public String getInputCpuId() {
        return this.inputCpuId;
    }
    
    public void setInputCpuId(String inputCpuId) {
        this.inputCpuId = inputCpuId;
    }
    
    public String getInputCpuCode() {
        return this.inputCpuCode;
    }
    
    public void setInputCpuCode(String inputCpuCode) {
        this.inputCpuCode = inputCpuCode;
    }
    
    public String getInputCreateDate() {
        return this.inputCreateDate;
    }
    
    public void setInputCreateDate(String inputCreateDate) {
        this.inputCreateDate = inputCreateDate;
    }
    
    public String getInputCreateTime() {
        return this.inputCreateTime;
    }
    
    public void setInputCreateTime(String inputCreateTime) {
        this.inputCreateTime = inputCreateTime;
    }
    
    public String getInputMfgName() {
        return this.inputMfgName;
    }
    
    public void setInputMfgName(String inputMfgName) {
        this.inputMfgName = inputMfgName;
    }
    
    public String getInputRecCnt() {
        return this.inputRecCnt;
    }
    
    public void setInputRecCnt(String inputRecCnt) {
        this.inputRecCnt = inputRecCnt;
    }
    
    public String getCpuDlrNo() {
        return this.cpuDlrNo;
    }
    
    public void setCpuDlrNo(String cpuDlrNo) {
        this.cpuDlrNo = cpuDlrNo;
    }
    
    public String getCustName() {
        return this.custName;
    }
    
    public void setCustName(String custName) {
        this.custName = custName;
    }
    
    public String getModelNo() {
        return this.modelNo;
    }
    
    public void setModelNo(String modelNo) {
        this.modelNo = modelNo;
    }
    
    public String getModelDs() {
        return this.modelDs;
    }
    
    public void setModelDs(String modelDs) {
        this.modelDs = modelDs;
    }
    
    public String getSerialNo() {
        return this.serialNo;
    }
    
    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }
    
    public String getRegComplDt() {
        return this.regComplDt;
    }
    
    public void setRegComplDt(String regComplDt) {
        this.regComplDt = regComplDt;
    }
    
    public String getRegTypeCd() {
        return this.regTypeCd;
    }
    
    public void setRegTypeCd(String regTypeCd) {
        this.regTypeCd = regTypeCd;
    }
    
    public Date getVehRegDt() {
        return this.vehRegDt;
    }
    
    public void setVehRegDt(Date vehRegDt) {
        this.vehRegDt = vehRegDt;
    }
    
    public String getVehRegMarkTx() {
        return this.vehRegMarkTx;
    }
    
    public void setVehRegMarkTx(String vehRegMarkTx) {
        this.vehRegMarkTx = vehRegMarkTx;
    }
    
    public String getMfgMakeDs() {
        return this.mfgMakeDs;
    }
    
    public void setMfgMakeDs(String mfgMakeDs) {
        this.mfgMakeDs = mfgMakeDs;
    }
    
    public int getPrevOwnerCnt() {
        return this.prevOwnerCnt;
    }
    
    public void setPrevOwnerCnt(int prevOwnerCnt) {
        this.prevOwnerCnt = prevOwnerCnt;
    }
    
    public Date getPrevOwnerChngDt() {
        return this.prevOwnerChngDt;
    }
    
    public void setPrevOwnerChngDt(Date prevOwnerChngDt) {
        this.prevOwnerChngDt = prevOwnerChngDt;
    }
    
    public String getFinSourceDesc() {
        return this.finSourceDesc;
    }
    
    public void setFinSourceDesc(String finSourceDesc) {
        this.finSourceDesc = finSourceDesc;
    }
    
    public String getFinSrcPhoneNo() {
        return this.finSrcPhoneNo;
    }
    
    public void setFinSrcPhoneNo(String finSrcPhoneNo) {
        this.finSrcPhoneNo = finSrcPhoneNo;
    }
    
    public String getFinAgreeTx() {
        return this.finAgreeTx;
    }
    
    public void setFinAgreeTx(String finAgreeTx) {
        this.finAgreeTx = finAgreeTx;
    }
    
    public String getFinAgreeTypeTx() {
        return this.finAgreeTypeTx;
    }
    
    public void setFinAgreeTypeTx(String finAgreeTypeTx) {
        this.finAgreeTypeTx = finAgreeTypeTx;
    }
    
    public Date getFinAgreeDt() {
        return this.finAgreeDt;
    }
    
    public void setFinAgreeDt(Date finAgreeDt) {
        this.finAgreeDt = finAgreeDt;
    }
    
    public String getFinAgreeTermTx() {
        return this.finAgreeTermTx;
    }
    
    public void setFinAgreeTermTx(String finAgreeTermTx) {
        this.finAgreeTermTx = finAgreeTermTx;
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ procDate=\"");
        s.append(getProcDate());
        s.append("\"");
        s.append(", fileSeqNo=\"");
        s.append(getFileSeqNo());
        s.append("\"");
        s.append(", fileRejDs=\"");
        s.append(getFileRejDs());
        s.append("\"");
        s.append(", itemSeqNo=\"");
        s.append(getItemSeqNo());
        s.append("\"");
        s.append(", itemRejDs=\"");
        s.append(getItemRejDs());
        s.append("\"");
        s.append(", inputMfgNo=\"");
        s.append(getInputMfgNo());
        s.append("\"");
        s.append(", inputDistNo=\"");
        s.append(getInputDistNo());
        s.append("\"");
        s.append(", inputDlrNo=\"");
        s.append(getInputDlrNo());
        s.append("\"");
        s.append(", inputCpuId=\"");
        s.append(getInputCpuId());
        s.append("\"");
        s.append(", inputCpuCode=\"");
        s.append(getInputCpuCode());
        s.append("\"");
        s.append(", inputCreateDate=\"");
        s.append(getInputCreateDate());
        s.append("\"");
        s.append(", inputCreateTime=\"");
        s.append(getInputCreateTime());
        s.append("\"");
        s.append(", inputMfgName=\"");
        s.append(getInputMfgName());
        s.append("\"");
        s.append(", inputRecCnt=\"");
        s.append(getInputRecCnt());
        s.append("\"");
        s.append(", cpuDlrNo=\"");
        s.append(getCpuDlrNo());
        s.append("\"");
        s.append(", custName=\"");
        s.append(getCustName());
        s.append("\"");
        s.append(", modelNo=\"");
        s.append(getModelNo());
        s.append("\"");
        s.append(", modelDs=\"");
        s.append(getModelDs());
        s.append("\"");
        s.append(", serialNo=\"");
        s.append(getSerialNo());
        s.append("\"");
        s.append(", regComplDt=\"");
        s.append(getRegComplDt());
        s.append("\"");
        s.append(", regTypeCd=\"");
        s.append(getRegTypeCd());
        s.append("\"");
        s.append(", vehRegDt=\"");
        s.append(getVehRegDt());
        s.append("\"");
        s.append(", vehRegMarkTx=\"");
        s.append(getVehRegMarkTx());
        s.append("\"");
        s.append(", mfgMakeDs=\"");
        s.append(getMfgMakeDs());
        s.append("\"");
        s.append(", prevOwnerCnt=\"");
        s.append(getPrevOwnerCnt());
        s.append("\"");
        s.append(", prevOwnerChngDt=\"");
        s.append(getPrevOwnerChngDt());
        s.append("\"");
        s.append(", finSourceDesc=\"");
        s.append(getFinSourceDesc());
        s.append("\"");
        s.append(", finSrcPhoneNo=\"");
        s.append(getFinSrcPhoneNo());
        s.append("\"");
        s.append(", finAgreeTx=\"");
        s.append(getFinAgreeTx());
        s.append("\"");
        s.append(", finAgreeTypeTx=\"");
        s.append(getFinAgreeTypeTx());
        s.append("\"");
        s.append(", finAgreeDt=\"");
        s.append(getFinAgreeDt());
        s.append("\"");
        s.append(", finAgreeTermTx=\"");
        s.append(getFinAgreeTermTx());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(Vwmbhrg that) {
        return this.procDate.equals(that.procDate) &&
            this.fileSeqNo == that.fileSeqNo &&
            this.fileRejDs.equals(that.fileRejDs) &&
            this.itemSeqNo == that.itemSeqNo &&
            this.itemRejDs.equals(that.itemRejDs) &&
            this.inputMfgNo.equals(that.inputMfgNo) &&
            this.inputDistNo.equals(that.inputDistNo) &&
            this.inputDlrNo.equals(that.inputDlrNo) &&
            this.inputCpuId.equals(that.inputCpuId) &&
            this.inputCpuCode.equals(that.inputCpuCode) &&
            this.inputCreateDate.equals(that.inputCreateDate) &&
            this.inputCreateTime.equals(that.inputCreateTime) &&
            this.inputMfgName.equals(that.inputMfgName) &&
            this.inputRecCnt.equals(that.inputRecCnt) &&
            this.cpuDlrNo.equals(that.cpuDlrNo) &&
            this.custName.equals(that.custName) &&
            this.modelNo.equals(that.modelNo) &&
            this.modelDs.equals(that.modelDs) &&
            this.serialNo.equals(that.serialNo) &&
            this.regComplDt.equals(that.regComplDt) &&
            this.regTypeCd.equals(that.regTypeCd) &&
            this.vehRegDt.equals(that.vehRegDt) &&
            this.vehRegMarkTx.equals(that.vehRegMarkTx) &&
            this.mfgMakeDs.equals(that.mfgMakeDs) &&
            this.prevOwnerCnt == that.prevOwnerCnt &&
            this.prevOwnerChngDt.equals(that.prevOwnerChngDt) &&
            this.finSourceDesc.equals(that.finSourceDesc) &&
            this.finSrcPhoneNo.equals(that.finSrcPhoneNo) &&
            this.finAgreeTx.equals(that.finAgreeTx) &&
            this.finAgreeTypeTx.equals(that.finAgreeTypeTx) &&
            this.finAgreeDt.equals(that.finAgreeDt) &&
            this.finAgreeTermTx.equals(that.finAgreeTermTx);
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof Vwmbhrg other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof Vwmbhrg;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Objects.hashCode(procDate);
        result = 31 * result + Integer.hashCode(fileSeqNo);
        result = 31 * result + Objects.hashCode(fileRejDs);
        result = 31 * result + Integer.hashCode(itemSeqNo);
        result = 31 * result + Objects.hashCode(itemRejDs);
        result = 31 * result + Objects.hashCode(inputMfgNo);
        result = 31 * result + Objects.hashCode(inputDistNo);
        result = 31 * result + Objects.hashCode(inputDlrNo);
        result = 31 * result + Objects.hashCode(inputCpuId);
        result = 31 * result + Objects.hashCode(inputCpuCode);
        result = 31 * result + Objects.hashCode(inputCreateDate);
        result = 31 * result + Objects.hashCode(inputCreateTime);
        result = 31 * result + Objects.hashCode(inputMfgName);
        result = 31 * result + Objects.hashCode(inputRecCnt);
        result = 31 * result + Objects.hashCode(cpuDlrNo);
        result = 31 * result + Objects.hashCode(custName);
        result = 31 * result + Objects.hashCode(modelNo);
        result = 31 * result + Objects.hashCode(modelDs);
        result = 31 * result + Objects.hashCode(serialNo);
        result = 31 * result + Objects.hashCode(regComplDt);
        result = 31 * result + Objects.hashCode(regTypeCd);
        result = 31 * result + Objects.hashCode(vehRegDt);
        result = 31 * result + Objects.hashCode(vehRegMarkTx);
        result = 31 * result + Objects.hashCode(mfgMakeDs);
        result = 31 * result + Integer.hashCode(prevOwnerCnt);
        result = 31 * result + Objects.hashCode(prevOwnerChngDt);
        result = 31 * result + Objects.hashCode(finSourceDesc);
        result = 31 * result + Objects.hashCode(finSrcPhoneNo);
        result = 31 * result + Objects.hashCode(finAgreeTx);
        result = 31 * result + Objects.hashCode(finAgreeTypeTx);
        result = 31 * result + Objects.hashCode(finAgreeDt);
        result = 31 * result + Objects.hashCode(finAgreeTermTx);
        return result;
    }
    
    @Override
    public int compareTo(Vwmbhrg that) {
        int c = 0;
        c = this.procDate.compareTo(that.procDate);
        if ( c != 0 ) return c;
        c = Integer.compare(this.fileSeqNo, that.fileSeqNo);
        if ( c != 0 ) return c;
        c = this.fileRejDs.compareTo(that.fileRejDs);
        if ( c != 0 ) return c;
        c = Integer.compare(this.itemSeqNo, that.itemSeqNo);
        if ( c != 0 ) return c;
        c = this.itemRejDs.compareTo(that.itemRejDs);
        if ( c != 0 ) return c;
        c = this.inputMfgNo.compareTo(that.inputMfgNo);
        if ( c != 0 ) return c;
        c = this.inputDistNo.compareTo(that.inputDistNo);
        if ( c != 0 ) return c;
        c = this.inputDlrNo.compareTo(that.inputDlrNo);
        if ( c != 0 ) return c;
        c = this.inputCpuId.compareTo(that.inputCpuId);
        if ( c != 0 ) return c;
        c = this.inputCpuCode.compareTo(that.inputCpuCode);
        if ( c != 0 ) return c;
        c = this.inputCreateDate.compareTo(that.inputCreateDate);
        if ( c != 0 ) return c;
        c = this.inputCreateTime.compareTo(that.inputCreateTime);
        if ( c != 0 ) return c;
        c = this.inputMfgName.compareTo(that.inputMfgName);
        if ( c != 0 ) return c;
        c = this.inputRecCnt.compareTo(that.inputRecCnt);
        if ( c != 0 ) return c;
        c = this.cpuDlrNo.compareTo(that.cpuDlrNo);
        if ( c != 0 ) return c;
        c = this.custName.compareTo(that.custName);
        if ( c != 0 ) return c;
        c = this.modelNo.compareTo(that.modelNo);
        if ( c != 0 ) return c;
        c = this.modelDs.compareTo(that.modelDs);
        if ( c != 0 ) return c;
        c = this.serialNo.compareTo(that.serialNo);
        if ( c != 0 ) return c;
        c = this.regComplDt.compareTo(that.regComplDt);
        if ( c != 0 ) return c;
        c = this.regTypeCd.compareTo(that.regTypeCd);
        if ( c != 0 ) return c;
        c = this.vehRegDt.compareTo(that.vehRegDt);
        if ( c != 0 ) return c;
        c = this.vehRegMarkTx.compareTo(that.vehRegMarkTx);
        if ( c != 0 ) return c;
        c = this.mfgMakeDs.compareTo(that.mfgMakeDs);
        if ( c != 0 ) return c;
        c = Integer.compare(this.prevOwnerCnt, that.prevOwnerCnt);
        if ( c != 0 ) return c;
        c = this.prevOwnerChngDt.compareTo(that.prevOwnerChngDt);
        if ( c != 0 ) return c;
        c = this.finSourceDesc.compareTo(that.finSourceDesc);
        if ( c != 0 ) return c;
        c = this.finSrcPhoneNo.compareTo(that.finSrcPhoneNo);
        if ( c != 0 ) return c;
        c = this.finAgreeTx.compareTo(that.finAgreeTx);
        if ( c != 0 ) return c;
        c = this.finAgreeTypeTx.compareTo(that.finAgreeTypeTx);
        if ( c != 0 ) return c;
        c = this.finAgreeDt.compareTo(that.finAgreeDt);
        if ( c != 0 ) return c;
        c = this.finAgreeTermTx.compareTo(that.finAgreeTermTx);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final StringField PROC_DATE = factory.getStringField(8);
    private static final DateTimeFormatter PROC_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final ExternalDecimalAsIntField FILE_SEQ_NO = factory.getExternalDecimalAsIntField(5, true);
    private static final StringField FILE_REJ_DS = factory.getStringField(21);
    private static final ExternalDecimalAsIntField ITEM_SEQ_NO = factory.getExternalDecimalAsIntField(5, true);
    private static final StringField ITEM_REJ_DS = factory.getStringField(21);
    private static final StringField INPUT_MFG_NO = factory.getStringField(7);
    private static final StringField INPUT_DIST_NO = factory.getStringField(7);
    private static final StringField INPUT_DLR_NO = factory.getStringField(7);
    private static final StringField INPUT_CPU_ID = factory.getStringField(5);
    private static final StringField INPUT_CPU_CODE = factory.getStringField(5);
    private static final StringField INPUT_CREATE_DATE = factory.getStringField(11);
    private static final StringField INPUT_CREATE_TIME = factory.getStringField(6);
    private static final StringField INPUT_MFG_NAME = factory.getStringField(21);
    private static final StringField INPUT_REC_CNT = factory.getStringField(7);
    private static final StringField CPU_DLR_NO = factory.getStringField(14);
    private static final StringField CUST_NAME = factory.getStringField(36);
    private static final StringField MODEL_NO = factory.getStringField(13);
    private static final StringField MODEL_DS = factory.getStringField(21);
    private static final StringField SERIAL_NO = factory.getStringField(21);
    private static final StringField REG_COMPL_DT = factory.getStringField(11);
    private static final StringField REG_TYPE_CD = factory.getStringField(4);
    private static final StringField VEH_REG_DT = factory.getStringField(8);
    private static final DateTimeFormatter VEH_REG_DT_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField VEH_REG_MARK_TX = factory.getStringField(9);
    private static final StringField MFG_MAKE_DS = factory.getStringField(21);
    private static final ExternalDecimalAsIntField PREV_OWNER_CNT = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField PREV_OWNER_CHNG_DT = factory.getStringField(8);
    private static final DateTimeFormatter PREV_OWNER_CHNG_DT_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField FIN_SOURCE_DESC = factory.getStringField(73);
    private static final StringField FIN_SRC_PHONE_NO = factory.getStringField(21);
    private static final StringField FIN_AGREE_TX = factory.getStringField(26);
    private static final StringField FIN_AGREE_TYPE_TX = factory.getStringField(21);
    private static final StringField FIN_AGREE_DT = factory.getStringField(8);
    private static final DateTimeFormatter FIN_AGREE_DT_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField FIN_AGREE_TERM_TX = factory.getStringField(4);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code Vwmbhrg} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code VWMBHRG} record
     * @see "VWMBHRG record at VWMBHRG.CPY:8"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        PROC_DATE.putString(procDate.toLocalDate().format(PROC_DATE_FMT), bytes, offset);
        FILE_SEQ_NO.putInt(fileSeqNo, bytes, offset);
        FILE_REJ_DS.putString(fileRejDs, bytes, offset);
        ITEM_SEQ_NO.putInt(itemSeqNo, bytes, offset);
        ITEM_REJ_DS.putString(itemRejDs, bytes, offset);
        INPUT_MFG_NO.putString(inputMfgNo, bytes, offset);
        INPUT_DIST_NO.putString(inputDistNo, bytes, offset);
        INPUT_DLR_NO.putString(inputDlrNo, bytes, offset);
        INPUT_CPU_ID.putString(inputCpuId, bytes, offset);
        INPUT_CPU_CODE.putString(inputCpuCode, bytes, offset);
        INPUT_CREATE_DATE.putString(inputCreateDate, bytes, offset);
        INPUT_CREATE_TIME.putString(inputCreateTime, bytes, offset);
        INPUT_MFG_NAME.putString(inputMfgName, bytes, offset);
        INPUT_REC_CNT.putString(inputRecCnt, bytes, offset);
        CPU_DLR_NO.putString(cpuDlrNo, bytes, offset);
        CUST_NAME.putString(custName, bytes, offset);
        MODEL_NO.putString(modelNo, bytes, offset);
        MODEL_DS.putString(modelDs, bytes, offset);
        SERIAL_NO.putString(serialNo, bytes, offset);
        REG_COMPL_DT.putString(regComplDt, bytes, offset);
        REG_TYPE_CD.putString(regTypeCd, bytes, offset);
        VEH_REG_DT.putString(vehRegDt.toLocalDate().format(VEH_REG_DT_FMT), bytes, offset);
        VEH_REG_MARK_TX.putString(vehRegMarkTx, bytes, offset);
        MFG_MAKE_DS.putString(mfgMakeDs, bytes, offset);
        PREV_OWNER_CNT.putInt(prevOwnerCnt, bytes, offset);
        PREV_OWNER_CHNG_DT.putString(prevOwnerChngDt.toLocalDate().format(PREV_OWNER_CHNG_DT_FMT), bytes, offset);
        FIN_SOURCE_DESC.putString(finSourceDesc, bytes, offset);
        FIN_SRC_PHONE_NO.putString(finSrcPhoneNo, bytes, offset);
        FIN_AGREE_TX.putString(finAgreeTx, bytes, offset);
        FIN_AGREE_TYPE_TX.putString(finAgreeTypeTx, bytes, offset);
        FIN_AGREE_DT.putString(finAgreeDt.toLocalDate().format(FIN_AGREE_DT_FMT), bytes, offset);
        FIN_AGREE_TERM_TX.putString(finAgreeTermTx, bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code Vwmbhrg} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code Vwmbhrg} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code Vwmbhrg} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code VWMBHRG} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "VWMBHRG record at VWMBHRG.CPY:8"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        procDate = Date.valueOf(LocalDate.parse(PROC_DATE.getString(bytes, offset), PROC_DATE_FMT));
        fileSeqNo = FILE_SEQ_NO.getInt(bytes, offset);
        fileRejDs = FILE_REJ_DS.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        itemSeqNo = ITEM_SEQ_NO.getInt(bytes, offset);
        itemRejDs = ITEM_REJ_DS.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        inputMfgNo = INPUT_MFG_NO.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        inputDistNo = INPUT_DIST_NO.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        inputDlrNo = INPUT_DLR_NO.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        inputCpuId = INPUT_CPU_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        inputCpuCode = INPUT_CPU_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        inputCreateDate = INPUT_CREATE_DATE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        inputCreateTime = INPUT_CREATE_TIME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        inputMfgName = INPUT_MFG_NAME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        inputRecCnt = INPUT_REC_CNT.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        cpuDlrNo = CPU_DLR_NO.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        custName = CUST_NAME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        modelNo = MODEL_NO.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        modelDs = MODEL_DS.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        serialNo = SERIAL_NO.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        regComplDt = REG_COMPL_DT.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        regTypeCd = REG_TYPE_CD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        vehRegDt = Date.valueOf(LocalDate.parse(VEH_REG_DT.getString(bytes, offset), VEH_REG_DT_FMT));
        vehRegMarkTx = VEH_REG_MARK_TX.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        mfgMakeDs = MFG_MAKE_DS.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        prevOwnerCnt = PREV_OWNER_CNT.getInt(bytes, offset);
        prevOwnerChngDt = Date.valueOf(LocalDate.parse(PREV_OWNER_CHNG_DT.getString(bytes, offset), PREV_OWNER_CHNG_DT_FMT));
        finSourceDesc = FIN_SOURCE_DESC.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        finSrcPhoneNo = FIN_SRC_PHONE_NO.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        finAgreeTx = FIN_AGREE_TX.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        finAgreeTypeTx = FIN_AGREE_TYPE_TX.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        finAgreeDt = Date.valueOf(LocalDate.parse(FIN_AGREE_DT.getString(bytes, offset), FIN_AGREE_DT_FMT));
        finAgreeTermTx = FIN_AGREE_TERM_TX.getString(bytes, offset).replaceFirst("\\s+\\z", "");
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
