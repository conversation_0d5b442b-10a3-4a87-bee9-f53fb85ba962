package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.ExternalDecimalAsIntField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class OutputLastMoYr implements Cloneable, Comparable<OutputLastMoYr> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private int outputLastMonth;
    
    /** Initialize fields to non-null default values */
    public OutputLastMoYr() {}
    
    /** Initialize all fields to provided values */
    public OutputLastMoYr(int outputLastMonth) {
        this.outputLastMonth = outputLastMonth;
    }
    
    @Override
    public OutputLastMoYr clone() throws CloneNotSupportedException {
        OutputLastMoYr cloned = (OutputLastMoYr) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code OutputLastMoYr} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected OutputLastMoYr(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code OutputLastMoYr} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected OutputLastMoYr(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code OutputLastMoYr} object
     * @see #setBytes(byte[], int)
     */
    public static OutputLastMoYr fromBytes(byte[] bytes, int offset) {
        return new OutputLastMoYr(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code OutputLastMoYr} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static OutputLastMoYr fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code OutputLastMoYr} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static OutputLastMoYr fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public int getOutputLastMonth() {
        return this.outputLastMonth;
    }
    
    public void setOutputLastMonth(int outputLastMonth) {
        this.outputLastMonth = outputLastMonth;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        outputLastMonth = 0;
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ outputLastMonth=\"");
        s.append(getOutputLastMonth());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(OutputLastMoYr that) {
        return this.outputLastMonth == that.outputLastMonth;
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof OutputLastMoYr other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof OutputLastMoYr;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Integer.hashCode(outputLastMonth);
        return result;
    }
    
    @Override
    public int compareTo(OutputLastMoYr that) {
        int c = 0;
        c = Integer.compare(this.outputLastMonth, that.outputLastMonth);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final ExternalDecimalAsIntField OUTPUT_LAST_MONTH = factory.getExternalDecimalAsIntField(2, true);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code OutputLastMoYr} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code OUTPUT-LAST-MO-YR} record
     * @see "OUTPUT-LAST-MO-YR record at MXWW01.CPY:389"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        OUTPUT_LAST_MONTH.putInt(outputLastMonth, bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code OutputLastMoYr} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code OutputLastMoYr} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code OutputLastMoYr} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code OUTPUT-LAST-MO-YR} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "OUTPUT-LAST-MO-YR record at MXWW01.CPY:389"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        outputLastMonth = OUTPUT_LAST_MONTH.getInt(bytes, offset);
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
