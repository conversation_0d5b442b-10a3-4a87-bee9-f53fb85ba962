package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.BinaryAsIntField;
import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.ExternalDecimalAsIntField;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class DateParmArea implements Cloneable, Comparable<DateParmArea> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private static final int SUN_REQ_VALUE = 1;
    private static final int MON_REQ_VALUE = 2;
    private static final int TUE_REQ_VALUE = 3;
    private static final int WED_REQ_VALUE = 4;
    private static final int THU_REQ_VALUE = 5;
    private static final int FRI_REQ_VALUE = 6;
    private static final int SAT_REQ_VALUE = 7;
    private static final int VALID_REQ_DAY_MIN = 1;
    private static final int VALID_REQ_DAY_MAX = 7;
    private static final int DATE_TO_DATE_VALUE = 0;
    private static final int NEXT_BUSS_DAY_VALUE = 1;
    private static final int PREVIOUS_BUSS_DAY_VALUE = 2;
    private static final int FIRST_BUSS_DAY_OF_MONTH_VALUE = 3;
    private static final int LAST_BUSS_DAY_OF_MONTH_VALUE = 4;
    private static final int FIRST_DAY_OF_MONTH_VALUE = 5;
    private static final int LAST_DAY_OF_MONTH_VALUE = 6;
    private static final int ADD_BUSS_DAYS_VALUE = 7;
    private static final int SUBTRACT_BUSS_DAYS_VALUE = 8;
    private static final int ADD_REGULAR_DAYS_VALUE = 9;
    private static final int SUBTRACT_REGULAR_DAYS_VALUE = 10;
    private static final int NEXT_MONTH_AND_YEAR_VALUE = 11;
    private static final int LAST_MONTH_AND_YEAR_VALUE = 12;
    private static final int BUSS_DAYS_BETWEEN_DATES_VALUE = 13;
    private static final int DAYS_BETWEEN_DATES_VALUE = 14;
    private static final int VALID_CASH_DAY_VALUE = 15;
    private static final int VALID_BUSS_DAY_VALUE = 16;
    private static final int LAST_DAY_NEXT_MONTH_VALUE = 17;
    private static final int LAST_DAY_LAST_MONTH_VALUE = 18;
    private static final int ADD_MONTHS_VALUE = 19;
    private static final int SUBTRACT_MONTHS_VALUE = 20;
    private static final int NEXT_SPECIFIC_DAY_VALUE = 21;
    private static final int LAST_SPECIFIC_DAY_VALUE = 22;
    private static final int DAY_NAME_NUMBER_VALUE = 23;
    private static final int NEXT_PROCESS_DAY_VALUE = 24;
    private static final int VALID_MANIPULATE_REQ_TYPE_MIN = 0;
    private static final int VALID_MANIPULATE_REQ_TYPE_MAX = 24;
    private static final int I_CURRENT_DATE_VALUE = 0;
    private static final int I_ISO_FORMAT_VALUE = 1;
    private static final int I_USA_FORMAT_VALUE = 2;
    private static final int I_EUR_FORMAT_VALUE = 3;
    private static final int I_JIS_FORMAT_VALUE = 4;
    private static final int I_MMDDYY_S_FORMAT_VALUE = 5;
    private static final int I_MMDDYY_FORMAT_VALUE = 6;
    private static final int I_MMDDCCYY_S_FORMAT_VALUE = 7;
    private static final int I_MMDDCCYY_FORMAT_VALUE = 8;
    private static final int I_YYMMDD_FORMAT_VALUE = 9;
    private static final int I_CCYYMMDD_FORMAT_VALUE = 10;
    private static final int I_JUL_FORMAT_VALUE = 11;
    private static final int I_EXP_FORMAT_VALUE = 12;
    private static final int VALID_INPUT_DATE_TYPE_MIN = 0;
    private static final int VALID_INPUT_DATE_TYPE_MAX = 12;
    
    private int reqDay;
    private int manipulateReqType;
    private int numberOfDays;
    private int numberOfMonths;
    private int inputDateType;
    private int oIsoCc;
    private int oIsoYy;
    private int oIsoMm;
    private int oIsoDd;
    private int oUsaMm;
    private int oUsaDd;
    private int oUsaCc;
    private int oUsaYy;
    private int oEurDd;
    private int oEurMm;
    private int oEurCc;
    private int oEurYy;
    private int oJisCc;
    private int oJisYy;
    private int oJisMm;
    private int oJisDd;
    private int oMdySlshMm;
    private int oMdySlshDd;
    private int oMdySlshYy;
    private int oMdyMm;
    private int oMdyDd;
    private int oMdyYy;
    private int oMdcySlshMm;
    private int oMdcySlshDd;
    private int oMdcySlshCc;
    private int oMdcySlshYy;
    private int oMdcyMm;
    private int oMdcyDd;
    private int oMdcyCc;
    private int oMdcyYy;
    private int oYmdYy;
    private int oYmdMm;
    private int oYmdDd;
    private int oCymdCc;
    private int oCymdYy;
    private int oCymdMm;
    private int oCymdDd;
    private int oJulYy;
    private int oJulDdd;
    private String oExpMm = "";
    private int oExpDd;
    private int oExpCc;
    private int oExpYy;
    private int differenceInDays;
    private int oDayNumber;
    private String reqCountry = "";
    private OutputNextMoYr outputNextMoYr = new OutputNextMoYr();
    private OutputLastMoYr outputLastMoYr = new OutputLastMoYr();
    
    /** Initialize fields to non-null default values */
    public DateParmArea() {}
    
    /** Initialize all fields to provided values */
    public DateParmArea(int reqDay, int manipulateReqType, int numberOfDays, int numberOfMonths, int inputDateType, int oIsoCc, int oIsoYy, int oIsoMm, int oIsoDd, int oUsaMm, int oUsaDd, int oUsaCc, int oUsaYy, int oEurDd, int oEurMm, int oEurCc, int oEurYy, int oJisCc, int oJisYy, int oJisMm, int oJisDd, int oMdySlshMm, int oMdySlshDd, int oMdySlshYy, int oMdyMm, int oMdyDd, int oMdyYy, int oMdcySlshMm, int oMdcySlshDd, int oMdcySlshCc, int oMdcySlshYy, int oMdcyMm, int oMdcyDd, int oMdcyCc, int oMdcyYy, int oYmdYy, int oYmdMm, int oYmdDd, int oCymdCc, int oCymdYy, int oCymdMm, int oCymdDd, int oJulYy, int oJulDdd, String oExpMm, int oExpDd, int oExpCc, int oExpYy, int differenceInDays, int oDayNumber, String reqCountry, OutputNextMoYr outputNextMoYr, OutputLastMoYr outputLastMoYr) {
        this.reqDay = reqDay;
        this.manipulateReqType = manipulateReqType;
        this.numberOfDays = numberOfDays;
        this.numberOfMonths = numberOfMonths;
        this.inputDateType = inputDateType;
        this.oIsoCc = oIsoCc;
        this.oIsoYy = oIsoYy;
        this.oIsoMm = oIsoMm;
        this.oIsoDd = oIsoDd;
        this.oUsaMm = oUsaMm;
        this.oUsaDd = oUsaDd;
        this.oUsaCc = oUsaCc;
        this.oUsaYy = oUsaYy;
        this.oEurDd = oEurDd;
        this.oEurMm = oEurMm;
        this.oEurCc = oEurCc;
        this.oEurYy = oEurYy;
        this.oJisCc = oJisCc;
        this.oJisYy = oJisYy;
        this.oJisMm = oJisMm;
        this.oJisDd = oJisDd;
        this.oMdySlshMm = oMdySlshMm;
        this.oMdySlshDd = oMdySlshDd;
        this.oMdySlshYy = oMdySlshYy;
        this.oMdyMm = oMdyMm;
        this.oMdyDd = oMdyDd;
        this.oMdyYy = oMdyYy;
        this.oMdcySlshMm = oMdcySlshMm;
        this.oMdcySlshDd = oMdcySlshDd;
        this.oMdcySlshCc = oMdcySlshCc;
        this.oMdcySlshYy = oMdcySlshYy;
        this.oMdcyMm = oMdcyMm;
        this.oMdcyDd = oMdcyDd;
        this.oMdcyCc = oMdcyCc;
        this.oMdcyYy = oMdcyYy;
        this.oYmdYy = oYmdYy;
        this.oYmdMm = oYmdMm;
        this.oYmdDd = oYmdDd;
        this.oCymdCc = oCymdCc;
        this.oCymdYy = oCymdYy;
        this.oCymdMm = oCymdMm;
        this.oCymdDd = oCymdDd;
        this.oJulYy = oJulYy;
        this.oJulDdd = oJulDdd;
        this.oExpMm = oExpMm;
        this.oExpDd = oExpDd;
        this.oExpCc = oExpCc;
        this.oExpYy = oExpYy;
        this.differenceInDays = differenceInDays;
        this.oDayNumber = oDayNumber;
        this.reqCountry = reqCountry;
        this.outputNextMoYr = outputNextMoYr;
        this.outputLastMoYr = outputLastMoYr;
    }
    
    @Override
    public DateParmArea clone() throws CloneNotSupportedException {
        DateParmArea cloned = (DateParmArea) super.clone();
        cloned.outputNextMoYr = this.outputNextMoYr.clone();
        cloned.outputLastMoYr = this.outputLastMoYr.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code DateParmArea} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected DateParmArea(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code DateParmArea} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected DateParmArea(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code DateParmArea} object
     * @see #setBytes(byte[], int)
     */
    public static DateParmArea fromBytes(byte[] bytes, int offset) {
        return new DateParmArea(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code DateParmArea} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static DateParmArea fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code DateParmArea} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static DateParmArea fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public int getReqDay() {
        return this.reqDay;
    }
    
    public void setReqDay(int reqDay) {
        this.reqDay = reqDay;
    }
    
    public boolean isSunReq() {
        return reqDay == SUN_REQ_VALUE;
    }
    
    public void setSunReq() {
        reqDay = SUN_REQ_VALUE;
    }
    
    public boolean isMonReq() {
        return reqDay == MON_REQ_VALUE;
    }
    
    public void setMonReq() {
        reqDay = MON_REQ_VALUE;
    }
    
    public boolean isTueReq() {
        return reqDay == TUE_REQ_VALUE;
    }
    
    public void setTueReq() {
        reqDay = TUE_REQ_VALUE;
    }
    
    public boolean isWedReq() {
        return reqDay == WED_REQ_VALUE;
    }
    
    public void setWedReq() {
        reqDay = WED_REQ_VALUE;
    }
    
    public boolean isThuReq() {
        return reqDay == THU_REQ_VALUE;
    }
    
    public void setThuReq() {
        reqDay = THU_REQ_VALUE;
    }
    
    public boolean isFriReq() {
        return reqDay == FRI_REQ_VALUE;
    }
    
    public void setFriReq() {
        reqDay = FRI_REQ_VALUE;
    }
    
    public boolean isSatReq() {
        return reqDay == SAT_REQ_VALUE;
    }
    
    public void setSatReq() {
        reqDay = SAT_REQ_VALUE;
    }
    
    public boolean isValidReqDay() {
        return VALID_REQ_DAY_MIN <= reqDay && reqDay <= VALID_REQ_DAY_MAX;
    }
    
    public void setValidReqDay() {
        reqDay = VALID_REQ_DAY_MIN;
    }
    
    public int getManipulateReqType() {
        return this.manipulateReqType;
    }
    
    public void setManipulateReqType(int manipulateReqType) {
        this.manipulateReqType = manipulateReqType;
    }
    
    public boolean isDateToDate() {
        return manipulateReqType == DATE_TO_DATE_VALUE;
    }
    
    public void setDateToDate() {
        manipulateReqType = DATE_TO_DATE_VALUE;
    }
    
    public boolean isNextBussDay() {
        return manipulateReqType == NEXT_BUSS_DAY_VALUE;
    }
    
    public void setNextBussDay() {
        manipulateReqType = NEXT_BUSS_DAY_VALUE;
    }
    
    public boolean isPreviousBussDay() {
        return manipulateReqType == PREVIOUS_BUSS_DAY_VALUE;
    }
    
    public void setPreviousBussDay() {
        manipulateReqType = PREVIOUS_BUSS_DAY_VALUE;
    }
    
    public boolean isFirstBussDayOfMonth() {
        return manipulateReqType == FIRST_BUSS_DAY_OF_MONTH_VALUE;
    }
    
    public void setFirstBussDayOfMonth() {
        manipulateReqType = FIRST_BUSS_DAY_OF_MONTH_VALUE;
    }
    
    public boolean isLastBussDayOfMonth() {
        return manipulateReqType == LAST_BUSS_DAY_OF_MONTH_VALUE;
    }
    
    public void setLastBussDayOfMonth() {
        manipulateReqType = LAST_BUSS_DAY_OF_MONTH_VALUE;
    }
    
    public boolean isFirstDayOfMonth() {
        return manipulateReqType == FIRST_DAY_OF_MONTH_VALUE;
    }
    
    public void setFirstDayOfMonth() {
        manipulateReqType = FIRST_DAY_OF_MONTH_VALUE;
    }
    
    public boolean isLastDayOfMonth() {
        return manipulateReqType == LAST_DAY_OF_MONTH_VALUE;
    }
    
    public void setLastDayOfMonth() {
        manipulateReqType = LAST_DAY_OF_MONTH_VALUE;
    }
    
    public boolean isAddBussDays() {
        return manipulateReqType == ADD_BUSS_DAYS_VALUE;
    }
    
    public void setAddBussDays() {
        manipulateReqType = ADD_BUSS_DAYS_VALUE;
    }
    
    public boolean isSubtractBussDays() {
        return manipulateReqType == SUBTRACT_BUSS_DAYS_VALUE;
    }
    
    public void setSubtractBussDays() {
        manipulateReqType = SUBTRACT_BUSS_DAYS_VALUE;
    }
    
    public boolean isAddRegularDays() {
        return manipulateReqType == ADD_REGULAR_DAYS_VALUE;
    }
    
    public void setAddRegularDays() {
        manipulateReqType = ADD_REGULAR_DAYS_VALUE;
    }
    
    public boolean isSubtractRegularDays() {
        return manipulateReqType == SUBTRACT_REGULAR_DAYS_VALUE;
    }
    
    public void setSubtractRegularDays() {
        manipulateReqType = SUBTRACT_REGULAR_DAYS_VALUE;
    }
    
    public boolean isNextMonthAndYear() {
        return manipulateReqType == NEXT_MONTH_AND_YEAR_VALUE;
    }
    
    public void setNextMonthAndYear() {
        manipulateReqType = NEXT_MONTH_AND_YEAR_VALUE;
    }
    
    public boolean isLastMonthAndYear() {
        return manipulateReqType == LAST_MONTH_AND_YEAR_VALUE;
    }
    
    public void setLastMonthAndYear() {
        manipulateReqType = LAST_MONTH_AND_YEAR_VALUE;
    }
    
    public boolean isBussDaysBetweenDates() {
        return manipulateReqType == BUSS_DAYS_BETWEEN_DATES_VALUE;
    }
    
    public void setBussDaysBetweenDates() {
        manipulateReqType = BUSS_DAYS_BETWEEN_DATES_VALUE;
    }
    
    public boolean isDaysBetweenDates() {
        return manipulateReqType == DAYS_BETWEEN_DATES_VALUE;
    }
    
    public void setDaysBetweenDates() {
        manipulateReqType = DAYS_BETWEEN_DATES_VALUE;
    }
    
    public boolean isValidCashDay() {
        return manipulateReqType == VALID_CASH_DAY_VALUE;
    }
    
    public void setValidCashDay() {
        manipulateReqType = VALID_CASH_DAY_VALUE;
    }
    
    public boolean isValidBussDay() {
        return manipulateReqType == VALID_BUSS_DAY_VALUE;
    }
    
    public void setValidBussDay() {
        manipulateReqType = VALID_BUSS_DAY_VALUE;
    }
    
    public boolean isLastDayNextMonth() {
        return manipulateReqType == LAST_DAY_NEXT_MONTH_VALUE;
    }
    
    public void setLastDayNextMonth() {
        manipulateReqType = LAST_DAY_NEXT_MONTH_VALUE;
    }
    
    public boolean isLastDayLastMonth() {
        return manipulateReqType == LAST_DAY_LAST_MONTH_VALUE;
    }
    
    public void setLastDayLastMonth() {
        manipulateReqType = LAST_DAY_LAST_MONTH_VALUE;
    }
    
    public boolean isAddMonths() {
        return manipulateReqType == ADD_MONTHS_VALUE;
    }
    
    public void setAddMonths() {
        manipulateReqType = ADD_MONTHS_VALUE;
    }
    
    public boolean isSubtractMonths() {
        return manipulateReqType == SUBTRACT_MONTHS_VALUE;
    }
    
    public void setSubtractMonths() {
        manipulateReqType = SUBTRACT_MONTHS_VALUE;
    }
    
    public boolean isNextSpecificDay() {
        return manipulateReqType == NEXT_SPECIFIC_DAY_VALUE;
    }
    
    public void setNextSpecificDay() {
        manipulateReqType = NEXT_SPECIFIC_DAY_VALUE;
    }
    
    public boolean isLastSpecificDay() {
        return manipulateReqType == LAST_SPECIFIC_DAY_VALUE;
    }
    
    public void setLastSpecificDay() {
        manipulateReqType = LAST_SPECIFIC_DAY_VALUE;
    }
    
    public boolean isDayNameNumber() {
        return manipulateReqType == DAY_NAME_NUMBER_VALUE;
    }
    
    public void setDayNameNumber() {
        manipulateReqType = DAY_NAME_NUMBER_VALUE;
    }
    
    public boolean isNextProcessDay() {
        return manipulateReqType == NEXT_PROCESS_DAY_VALUE;
    }
    
    public void setNextProcessDay() {
        manipulateReqType = NEXT_PROCESS_DAY_VALUE;
    }
    
    public boolean isValidManipulateReqType() {
        return VALID_MANIPULATE_REQ_TYPE_MIN <= manipulateReqType && manipulateReqType <= VALID_MANIPULATE_REQ_TYPE_MAX;
    }
    
    public void setValidManipulateReqType() {
        manipulateReqType = VALID_MANIPULATE_REQ_TYPE_MIN;
    }
    
    public int getNumberOfDays() {
        return this.numberOfDays;
    }
    
    public void setNumberOfDays(int numberOfDays) {
        this.numberOfDays = numberOfDays;
    }
    
    public int getNumberOfMonths() {
        return this.numberOfMonths;
    }
    
    public void setNumberOfMonths(int numberOfMonths) {
        this.numberOfMonths = numberOfMonths;
    }
    
    public int getInputDateType() {
        return this.inputDateType;
    }
    
    public void setInputDateType(int inputDateType) {
        this.inputDateType = inputDateType;
    }
    
    public boolean isICurrentDate() {
        return inputDateType == I_CURRENT_DATE_VALUE;
    }
    
    public void setICurrentDate() {
        inputDateType = I_CURRENT_DATE_VALUE;
    }
    
    public boolean isIIsoFormat() {
        return inputDateType == I_ISO_FORMAT_VALUE;
    }
    
    public void setIIsoFormat() {
        inputDateType = I_ISO_FORMAT_VALUE;
    }
    
    public boolean isIUsaFormat() {
        return inputDateType == I_USA_FORMAT_VALUE;
    }
    
    public void setIUsaFormat() {
        inputDateType = I_USA_FORMAT_VALUE;
    }
    
    public boolean isIEurFormat() {
        return inputDateType == I_EUR_FORMAT_VALUE;
    }
    
    public void setIEurFormat() {
        inputDateType = I_EUR_FORMAT_VALUE;
    }
    
    public boolean isIJisFormat() {
        return inputDateType == I_JIS_FORMAT_VALUE;
    }
    
    public void setIJisFormat() {
        inputDateType = I_JIS_FORMAT_VALUE;
    }
    
    public boolean isIMmddyySFormat() {
        return inputDateType == I_MMDDYY_S_FORMAT_VALUE;
    }
    
    public void setIMmddyySFormat() {
        inputDateType = I_MMDDYY_S_FORMAT_VALUE;
    }
    
    public boolean isIMmddyyFormat() {
        return inputDateType == I_MMDDYY_FORMAT_VALUE;
    }
    
    public void setIMmddyyFormat() {
        inputDateType = I_MMDDYY_FORMAT_VALUE;
    }
    
    public boolean isIMmddccyySFormat() {
        return inputDateType == I_MMDDCCYY_S_FORMAT_VALUE;
    }
    
    public void setIMmddccyySFormat() {
        inputDateType = I_MMDDCCYY_S_FORMAT_VALUE;
    }
    
    public boolean isIMmddccyyFormat() {
        return inputDateType == I_MMDDCCYY_FORMAT_VALUE;
    }
    
    public void setIMmddccyyFormat() {
        inputDateType = I_MMDDCCYY_FORMAT_VALUE;
    }
    
    public boolean isIYymmddFormat() {
        return inputDateType == I_YYMMDD_FORMAT_VALUE;
    }
    
    public void setIYymmddFormat() {
        inputDateType = I_YYMMDD_FORMAT_VALUE;
    }
    
    public boolean isICcyymmddFormat() {
        return inputDateType == I_CCYYMMDD_FORMAT_VALUE;
    }
    
    public void setICcyymmddFormat() {
        inputDateType = I_CCYYMMDD_FORMAT_VALUE;
    }
    
    public boolean isIJulFormat() {
        return inputDateType == I_JUL_FORMAT_VALUE;
    }
    
    public void setIJulFormat() {
        inputDateType = I_JUL_FORMAT_VALUE;
    }
    
    public boolean isIExpFormat() {
        return inputDateType == I_EXP_FORMAT_VALUE;
    }
    
    public void setIExpFormat() {
        inputDateType = I_EXP_FORMAT_VALUE;
    }
    
    public boolean isValidInputDateType() {
        return VALID_INPUT_DATE_TYPE_MIN <= inputDateType && inputDateType <= VALID_INPUT_DATE_TYPE_MAX;
    }
    
    public void setValidInputDateType() {
        inputDateType = VALID_INPUT_DATE_TYPE_MIN;
    }
    
    public int getOIsoCc() {
        return this.oIsoCc;
    }
    
    public void setOIsoCc(int oIsoCc) {
        this.oIsoCc = oIsoCc;
    }
    
    public int getOIsoYy() {
        return this.oIsoYy;
    }
    
    public void setOIsoYy(int oIsoYy) {
        this.oIsoYy = oIsoYy;
    }
    
    public int getOIsoMm() {
        return this.oIsoMm;
    }
    
    public void setOIsoMm(int oIsoMm) {
        this.oIsoMm = oIsoMm;
    }
    
    public int getOIsoDd() {
        return this.oIsoDd;
    }
    
    public void setOIsoDd(int oIsoDd) {
        this.oIsoDd = oIsoDd;
    }
    
    public int getOUsaMm() {
        return this.oUsaMm;
    }
    
    public void setOUsaMm(int oUsaMm) {
        this.oUsaMm = oUsaMm;
    }
    
    public int getOUsaDd() {
        return this.oUsaDd;
    }
    
    public void setOUsaDd(int oUsaDd) {
        this.oUsaDd = oUsaDd;
    }
    
    public int getOUsaCc() {
        return this.oUsaCc;
    }
    
    public void setOUsaCc(int oUsaCc) {
        this.oUsaCc = oUsaCc;
    }
    
    public int getOUsaYy() {
        return this.oUsaYy;
    }
    
    public void setOUsaYy(int oUsaYy) {
        this.oUsaYy = oUsaYy;
    }
    
    public int getOEurDd() {
        return this.oEurDd;
    }
    
    public void setOEurDd(int oEurDd) {
        this.oEurDd = oEurDd;
    }
    
    public int getOEurMm() {
        return this.oEurMm;
    }
    
    public void setOEurMm(int oEurMm) {
        this.oEurMm = oEurMm;
    }
    
    public int getOEurCc() {
        return this.oEurCc;
    }
    
    public void setOEurCc(int oEurCc) {
        this.oEurCc = oEurCc;
    }
    
    public int getOEurYy() {
        return this.oEurYy;
    }
    
    public void setOEurYy(int oEurYy) {
        this.oEurYy = oEurYy;
    }
    
    public int getOJisCc() {
        return this.oJisCc;
    }
    
    public void setOJisCc(int oJisCc) {
        this.oJisCc = oJisCc;
    }
    
    public int getOJisYy() {
        return this.oJisYy;
    }
    
    public void setOJisYy(int oJisYy) {
        this.oJisYy = oJisYy;
    }
    
    public int getOJisMm() {
        return this.oJisMm;
    }
    
    public void setOJisMm(int oJisMm) {
        this.oJisMm = oJisMm;
    }
    
    public int getOJisDd() {
        return this.oJisDd;
    }
    
    public void setOJisDd(int oJisDd) {
        this.oJisDd = oJisDd;
    }
    
    public int getOMdySlshMm() {
        return this.oMdySlshMm;
    }
    
    public void setOMdySlshMm(int oMdySlshMm) {
        this.oMdySlshMm = oMdySlshMm;
    }
    
    public int getOMdySlshDd() {
        return this.oMdySlshDd;
    }
    
    public void setOMdySlshDd(int oMdySlshDd) {
        this.oMdySlshDd = oMdySlshDd;
    }
    
    public int getOMdySlshYy() {
        return this.oMdySlshYy;
    }
    
    public void setOMdySlshYy(int oMdySlshYy) {
        this.oMdySlshYy = oMdySlshYy;
    }
    
    public int getOMdyMm() {
        return this.oMdyMm;
    }
    
    public void setOMdyMm(int oMdyMm) {
        this.oMdyMm = oMdyMm;
    }
    
    public int getOMdyDd() {
        return this.oMdyDd;
    }
    
    public void setOMdyDd(int oMdyDd) {
        this.oMdyDd = oMdyDd;
    }
    
    public int getOMdyYy() {
        return this.oMdyYy;
    }
    
    public void setOMdyYy(int oMdyYy) {
        this.oMdyYy = oMdyYy;
    }
    
    public int getOMdcySlshMm() {
        return this.oMdcySlshMm;
    }
    
    public void setOMdcySlshMm(int oMdcySlshMm) {
        this.oMdcySlshMm = oMdcySlshMm;
    }
    
    public int getOMdcySlshDd() {
        return this.oMdcySlshDd;
    }
    
    public void setOMdcySlshDd(int oMdcySlshDd) {
        this.oMdcySlshDd = oMdcySlshDd;
    }
    
    public int getOMdcySlshCc() {
        return this.oMdcySlshCc;
    }
    
    public void setOMdcySlshCc(int oMdcySlshCc) {
        this.oMdcySlshCc = oMdcySlshCc;
    }
    
    public int getOMdcySlshYy() {
        return this.oMdcySlshYy;
    }
    
    public void setOMdcySlshYy(int oMdcySlshYy) {
        this.oMdcySlshYy = oMdcySlshYy;
    }
    
    public int getOMdcyMm() {
        return this.oMdcyMm;
    }
    
    public void setOMdcyMm(int oMdcyMm) {
        this.oMdcyMm = oMdcyMm;
    }
    
    public int getOMdcyDd() {
        return this.oMdcyDd;
    }
    
    public void setOMdcyDd(int oMdcyDd) {
        this.oMdcyDd = oMdcyDd;
    }
    
    public int getOMdcyCc() {
        return this.oMdcyCc;
    }
    
    public void setOMdcyCc(int oMdcyCc) {
        this.oMdcyCc = oMdcyCc;
    }
    
    public int getOMdcyYy() {
        return this.oMdcyYy;
    }
    
    public void setOMdcyYy(int oMdcyYy) {
        this.oMdcyYy = oMdcyYy;
    }
    
    public int getOYmdYy() {
        return this.oYmdYy;
    }
    
    public void setOYmdYy(int oYmdYy) {
        this.oYmdYy = oYmdYy;
    }
    
    public int getOYmdMm() {
        return this.oYmdMm;
    }
    
    public void setOYmdMm(int oYmdMm) {
        this.oYmdMm = oYmdMm;
    }
    
    public int getOYmdDd() {
        return this.oYmdDd;
    }
    
    public void setOYmdDd(int oYmdDd) {
        this.oYmdDd = oYmdDd;
    }
    
    public int getOCymdCc() {
        return this.oCymdCc;
    }
    
    public void setOCymdCc(int oCymdCc) {
        this.oCymdCc = oCymdCc;
    }
    
    public int getOCymdYy() {
        return this.oCymdYy;
    }
    
    public void setOCymdYy(int oCymdYy) {
        this.oCymdYy = oCymdYy;
    }
    
    public int getOCymdMm() {
        return this.oCymdMm;
    }
    
    public void setOCymdMm(int oCymdMm) {
        this.oCymdMm = oCymdMm;
    }
    
    public int getOCymdDd() {
        return this.oCymdDd;
    }
    
    public void setOCymdDd(int oCymdDd) {
        this.oCymdDd = oCymdDd;
    }
    
    public int getOJulYy() {
        return this.oJulYy;
    }
    
    public void setOJulYy(int oJulYy) {
        this.oJulYy = oJulYy;
    }
    
    public int getOJulDdd() {
        return this.oJulDdd;
    }
    
    public void setOJulDdd(int oJulDdd) {
        this.oJulDdd = oJulDdd;
    }
    
    public String getOExpMm() {
        return this.oExpMm;
    }
    
    public void setOExpMm(String oExpMm) {
        this.oExpMm = oExpMm;
    }
    
    public int getOExpDd() {
        return this.oExpDd;
    }
    
    public void setOExpDd(int oExpDd) {
        this.oExpDd = oExpDd;
    }
    
    public int getOExpCc() {
        return this.oExpCc;
    }
    
    public void setOExpCc(int oExpCc) {
        this.oExpCc = oExpCc;
    }
    
    public int getOExpYy() {
        return this.oExpYy;
    }
    
    public void setOExpYy(int oExpYy) {
        this.oExpYy = oExpYy;
    }
    
    public int getDifferenceInDays() {
        return this.differenceInDays;
    }
    
    public void setDifferenceInDays(int differenceInDays) {
        this.differenceInDays = differenceInDays;
    }
    
    public int getODayNumber() {
        return this.oDayNumber;
    }
    
    public void setODayNumber(int oDayNumber) {
        this.oDayNumber = oDayNumber;
    }
    
    public String getReqCountry() {
        return this.reqCountry;
    }
    
    public void setReqCountry(String reqCountry) {
        this.reqCountry = reqCountry;
    }
    
    public OutputNextMoYr getOutputNextMoYr() {
        return this.outputNextMoYr;
    }
    
    public void setOutputNextMoYr(OutputNextMoYr outputNextMoYr) {
        this.outputNextMoYr = outputNextMoYr;
    }
    
    public OutputLastMoYr getOutputLastMoYr() {
        return this.outputLastMoYr;
    }
    
    public void setOutputLastMoYr(OutputLastMoYr outputLastMoYr) {
        this.outputLastMoYr = outputLastMoYr;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        reqDay = 0;
        manipulateReqType = 0;
        numberOfDays = 0;
        numberOfMonths = 0;
        inputDateType = 0;
        oIsoCc = 0;
        oIsoYy = 0;
        oIsoMm = 0;
        oIsoDd = 0;
        oUsaMm = 0;
        oUsaDd = 0;
        oUsaCc = 0;
        oUsaYy = 0;
        oEurDd = 0;
        oEurMm = 0;
        oEurCc = 0;
        oEurYy = 0;
        oJisCc = 0;
        oJisYy = 0;
        oJisMm = 0;
        oJisDd = 0;
        oMdySlshMm = 0;
        oMdySlshDd = 0;
        oMdySlshYy = 0;
        oMdyMm = 0;
        oMdyDd = 0;
        oMdyYy = 0;
        oMdcySlshMm = 0;
        oMdcySlshDd = 0;
        oMdcySlshCc = 0;
        oMdcySlshYy = 0;
        oMdcyMm = 0;
        oMdcyDd = 0;
        oMdcyCc = 0;
        oMdcyYy = 0;
        oYmdYy = 0;
        oYmdMm = 0;
        oYmdDd = 0;
        oCymdCc = 0;
        oCymdYy = 0;
        oCymdMm = 0;
        oCymdDd = 0;
        oJulYy = 0;
        oJulDdd = 0;
        oExpMm = "";
        oExpDd = 0;
        oExpCc = 0;
        oExpYy = 0;
        differenceInDays = 0;
        oDayNumber = 0;
        reqCountry = "";
        outputNextMoYr.reset();
        outputLastMoYr.reset();
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ filler23=\"");
        s.append(new String(filler23, encoding));
        s.append("\"");
        s.append(", reqDay=\"");
        s.append(getReqDay());
        s.append("\"");
        s.append(", manipulateReqType=\"");
        s.append(getManipulateReqType());
        s.append("\"");
        s.append(", numberOfDays=\"");
        s.append(getNumberOfDays());
        s.append("\"");
        s.append(", numberOfMonths=\"");
        s.append(getNumberOfMonths());
        s.append("\"");
        s.append(", inputDateType=\"");
        s.append(getInputDateType());
        s.append("\"");
        s.append(", oIsoCc=\"");
        s.append(getOIsoCc());
        s.append("\"");
        s.append(", oIsoYy=\"");
        s.append(getOIsoYy());
        s.append("\"");
        s.append(", filler74=\"");
        s.append(new String(filler74, encoding));
        s.append("\"");
        s.append(", oIsoMm=\"");
        s.append(getOIsoMm());
        s.append("\"");
        s.append(", filler75=\"");
        s.append(new String(filler75, encoding));
        s.append("\"");
        s.append(", oIsoDd=\"");
        s.append(getOIsoDd());
        s.append("\"");
        s.append(", oUsaMm=\"");
        s.append(getOUsaMm());
        s.append("\"");
        s.append(", filler76=\"");
        s.append(new String(filler76, encoding));
        s.append("\"");
        s.append(", oUsaDd=\"");
        s.append(getOUsaDd());
        s.append("\"");
        s.append(", filler77=\"");
        s.append(new String(filler77, encoding));
        s.append("\"");
        s.append(", oUsaCc=\"");
        s.append(getOUsaCc());
        s.append("\"");
        s.append(", oUsaYy=\"");
        s.append(getOUsaYy());
        s.append("\"");
        s.append(", oEurDd=\"");
        s.append(getOEurDd());
        s.append("\"");
        s.append(", filler78=\"");
        s.append(new String(filler78, encoding));
        s.append("\"");
        s.append(", oEurMm=\"");
        s.append(getOEurMm());
        s.append("\"");
        s.append(", filler79=\"");
        s.append(new String(filler79, encoding));
        s.append("\"");
        s.append(", oEurCc=\"");
        s.append(getOEurCc());
        s.append("\"");
        s.append(", oEurYy=\"");
        s.append(getOEurYy());
        s.append("\"");
        s.append(", oJisCc=\"");
        s.append(getOJisCc());
        s.append("\"");
        s.append(", oJisYy=\"");
        s.append(getOJisYy());
        s.append("\"");
        s.append(", filler80=\"");
        s.append(new String(filler80, encoding));
        s.append("\"");
        s.append(", oJisMm=\"");
        s.append(getOJisMm());
        s.append("\"");
        s.append(", filler81=\"");
        s.append(new String(filler81, encoding));
        s.append("\"");
        s.append(", oJisDd=\"");
        s.append(getOJisDd());
        s.append("\"");
        s.append(", oMdySlshMm=\"");
        s.append(getOMdySlshMm());
        s.append("\"");
        s.append(", filler82=\"");
        s.append(new String(filler82, encoding));
        s.append("\"");
        s.append(", oMdySlshDd=\"");
        s.append(getOMdySlshDd());
        s.append("\"");
        s.append(", filler83=\"");
        s.append(new String(filler83, encoding));
        s.append("\"");
        s.append(", oMdySlshYy=\"");
        s.append(getOMdySlshYy());
        s.append("\"");
        s.append(", oMdyMm=\"");
        s.append(getOMdyMm());
        s.append("\"");
        s.append(", oMdyDd=\"");
        s.append(getOMdyDd());
        s.append("\"");
        s.append(", oMdyYy=\"");
        s.append(getOMdyYy());
        s.append("\"");
        s.append(", oMdcySlshMm=\"");
        s.append(getOMdcySlshMm());
        s.append("\"");
        s.append(", filler84=\"");
        s.append(new String(filler84, encoding));
        s.append("\"");
        s.append(", oMdcySlshDd=\"");
        s.append(getOMdcySlshDd());
        s.append("\"");
        s.append(", filler85=\"");
        s.append(new String(filler85, encoding));
        s.append("\"");
        s.append(", oMdcySlshCc=\"");
        s.append(getOMdcySlshCc());
        s.append("\"");
        s.append(", oMdcySlshYy=\"");
        s.append(getOMdcySlshYy());
        s.append("\"");
        s.append(", oMdcyMm=\"");
        s.append(getOMdcyMm());
        s.append("\"");
        s.append(", oMdcyDd=\"");
        s.append(getOMdcyDd());
        s.append("\"");
        s.append(", oMdcyCc=\"");
        s.append(getOMdcyCc());
        s.append("\"");
        s.append(", oMdcyYy=\"");
        s.append(getOMdcyYy());
        s.append("\"");
        s.append(", oYmdYy=\"");
        s.append(getOYmdYy());
        s.append("\"");
        s.append(", oYmdMm=\"");
        s.append(getOYmdMm());
        s.append("\"");
        s.append(", oYmdDd=\"");
        s.append(getOYmdDd());
        s.append("\"");
        s.append(", oCymdCc=\"");
        s.append(getOCymdCc());
        s.append("\"");
        s.append(", oCymdYy=\"");
        s.append(getOCymdYy());
        s.append("\"");
        s.append(", oCymdMm=\"");
        s.append(getOCymdMm());
        s.append("\"");
        s.append(", oCymdDd=\"");
        s.append(getOCymdDd());
        s.append("\"");
        s.append(", oJulYy=\"");
        s.append(getOJulYy());
        s.append("\"");
        s.append(", oJulDdd=\"");
        s.append(getOJulDdd());
        s.append("\"");
        s.append(", oExpMm=\"");
        s.append(getOExpMm());
        s.append("\"");
        s.append(", filler86=\"");
        s.append(new String(filler86, encoding));
        s.append("\"");
        s.append(", oExpDd=\"");
        s.append(getOExpDd());
        s.append("\"");
        s.append(", filler87=\"");
        s.append(new String(filler87, encoding));
        s.append("\"");
        s.append(", oExpCc=\"");
        s.append(getOExpCc());
        s.append("\"");
        s.append(", oExpYy=\"");
        s.append(getOExpYy());
        s.append("\"");
        s.append(", filler88=\"");
        s.append(new String(filler88, encoding));
        s.append("\"");
        s.append(", differenceInDays=\"");
        s.append(getDifferenceInDays());
        s.append("\"");
        s.append(", oDayNumber=\"");
        s.append(getODayNumber());
        s.append("\"");
        s.append(", reqCountry=\"");
        s.append(getReqCountry());
        s.append("\"");
        s.append(", outputNextMoYr=\"");
        s.append(getOutputNextMoYr());
        s.append("\"");
        s.append(", outputLastMoYr=\"");
        s.append(getOutputLastMoYr());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(DateParmArea that) {
        return Arrays.equals(this.filler23, that.filler23) &&
            this.reqDay == that.reqDay &&
            this.manipulateReqType == that.manipulateReqType &&
            this.numberOfDays == that.numberOfDays &&
            this.numberOfMonths == that.numberOfMonths &&
            this.inputDateType == that.inputDateType &&
            this.oIsoCc == that.oIsoCc &&
            this.oIsoYy == that.oIsoYy &&
            Arrays.equals(this.filler74, that.filler74) &&
            this.oIsoMm == that.oIsoMm &&
            Arrays.equals(this.filler75, that.filler75) &&
            this.oIsoDd == that.oIsoDd &&
            this.oUsaMm == that.oUsaMm &&
            Arrays.equals(this.filler76, that.filler76) &&
            this.oUsaDd == that.oUsaDd &&
            Arrays.equals(this.filler77, that.filler77) &&
            this.oUsaCc == that.oUsaCc &&
            this.oUsaYy == that.oUsaYy &&
            this.oEurDd == that.oEurDd &&
            Arrays.equals(this.filler78, that.filler78) &&
            this.oEurMm == that.oEurMm &&
            Arrays.equals(this.filler79, that.filler79) &&
            this.oEurCc == that.oEurCc &&
            this.oEurYy == that.oEurYy &&
            this.oJisCc == that.oJisCc &&
            this.oJisYy == that.oJisYy &&
            Arrays.equals(this.filler80, that.filler80) &&
            this.oJisMm == that.oJisMm &&
            Arrays.equals(this.filler81, that.filler81) &&
            this.oJisDd == that.oJisDd &&
            this.oMdySlshMm == that.oMdySlshMm &&
            Arrays.equals(this.filler82, that.filler82) &&
            this.oMdySlshDd == that.oMdySlshDd &&
            Arrays.equals(this.filler83, that.filler83) &&
            this.oMdySlshYy == that.oMdySlshYy &&
            this.oMdyMm == that.oMdyMm &&
            this.oMdyDd == that.oMdyDd &&
            this.oMdyYy == that.oMdyYy &&
            this.oMdcySlshMm == that.oMdcySlshMm &&
            Arrays.equals(this.filler84, that.filler84) &&
            this.oMdcySlshDd == that.oMdcySlshDd &&
            Arrays.equals(this.filler85, that.filler85) &&
            this.oMdcySlshCc == that.oMdcySlshCc &&
            this.oMdcySlshYy == that.oMdcySlshYy &&
            this.oMdcyMm == that.oMdcyMm &&
            this.oMdcyDd == that.oMdcyDd &&
            this.oMdcyCc == that.oMdcyCc &&
            this.oMdcyYy == that.oMdcyYy &&
            this.oYmdYy == that.oYmdYy &&
            this.oYmdMm == that.oYmdMm &&
            this.oYmdDd == that.oYmdDd &&
            this.oCymdCc == that.oCymdCc &&
            this.oCymdYy == that.oCymdYy &&
            this.oCymdMm == that.oCymdMm &&
            this.oCymdDd == that.oCymdDd &&
            this.oJulYy == that.oJulYy &&
            this.oJulDdd == that.oJulDdd &&
            this.oExpMm.equals(that.oExpMm) &&
            Arrays.equals(this.filler86, that.filler86) &&
            this.oExpDd == that.oExpDd &&
            Arrays.equals(this.filler87, that.filler87) &&
            this.oExpCc == that.oExpCc &&
            this.oExpYy == that.oExpYy &&
            Arrays.equals(this.filler88, that.filler88) &&
            this.differenceInDays == that.differenceInDays &&
            this.oDayNumber == that.oDayNumber &&
            this.reqCountry.equals(that.reqCountry) &&
            this.outputNextMoYr.equals(that.outputNextMoYr) &&
            this.outputLastMoYr.equals(that.outputLastMoYr);
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof DateParmArea other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof DateParmArea;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Arrays.hashCode(filler23);
        result = 31 * result + Integer.hashCode(reqDay);
        result = 31 * result + Integer.hashCode(manipulateReqType);
        result = 31 * result + Integer.hashCode(numberOfDays);
        result = 31 * result + Integer.hashCode(numberOfMonths);
        result = 31 * result + Integer.hashCode(inputDateType);
        result = 31 * result + Integer.hashCode(oIsoCc);
        result = 31 * result + Integer.hashCode(oIsoYy);
        result = 31 * result + Arrays.hashCode(filler74);
        result = 31 * result + Integer.hashCode(oIsoMm);
        result = 31 * result + Arrays.hashCode(filler75);
        result = 31 * result + Integer.hashCode(oIsoDd);
        result = 31 * result + Integer.hashCode(oUsaMm);
        result = 31 * result + Arrays.hashCode(filler76);
        result = 31 * result + Integer.hashCode(oUsaDd);
        result = 31 * result + Arrays.hashCode(filler77);
        result = 31 * result + Integer.hashCode(oUsaCc);
        result = 31 * result + Integer.hashCode(oUsaYy);
        result = 31 * result + Integer.hashCode(oEurDd);
        result = 31 * result + Arrays.hashCode(filler78);
        result = 31 * result + Integer.hashCode(oEurMm);
        result = 31 * result + Arrays.hashCode(filler79);
        result = 31 * result + Integer.hashCode(oEurCc);
        result = 31 * result + Integer.hashCode(oEurYy);
        result = 31 * result + Integer.hashCode(oJisCc);
        result = 31 * result + Integer.hashCode(oJisYy);
        result = 31 * result + Arrays.hashCode(filler80);
        result = 31 * result + Integer.hashCode(oJisMm);
        result = 31 * result + Arrays.hashCode(filler81);
        result = 31 * result + Integer.hashCode(oJisDd);
        result = 31 * result + Integer.hashCode(oMdySlshMm);
        result = 31 * result + Arrays.hashCode(filler82);
        result = 31 * result + Integer.hashCode(oMdySlshDd);
        result = 31 * result + Arrays.hashCode(filler83);
        result = 31 * result + Integer.hashCode(oMdySlshYy);
        result = 31 * result + Integer.hashCode(oMdyMm);
        result = 31 * result + Integer.hashCode(oMdyDd);
        result = 31 * result + Integer.hashCode(oMdyYy);
        result = 31 * result + Integer.hashCode(oMdcySlshMm);
        result = 31 * result + Arrays.hashCode(filler84);
        result = 31 * result + Integer.hashCode(oMdcySlshDd);
        result = 31 * result + Arrays.hashCode(filler85);
        result = 31 * result + Integer.hashCode(oMdcySlshCc);
        result = 31 * result + Integer.hashCode(oMdcySlshYy);
        result = 31 * result + Integer.hashCode(oMdcyMm);
        result = 31 * result + Integer.hashCode(oMdcyDd);
        result = 31 * result + Integer.hashCode(oMdcyCc);
        result = 31 * result + Integer.hashCode(oMdcyYy);
        result = 31 * result + Integer.hashCode(oYmdYy);
        result = 31 * result + Integer.hashCode(oYmdMm);
        result = 31 * result + Integer.hashCode(oYmdDd);
        result = 31 * result + Integer.hashCode(oCymdCc);
        result = 31 * result + Integer.hashCode(oCymdYy);
        result = 31 * result + Integer.hashCode(oCymdMm);
        result = 31 * result + Integer.hashCode(oCymdDd);
        result = 31 * result + Integer.hashCode(oJulYy);
        result = 31 * result + Integer.hashCode(oJulDdd);
        result = 31 * result + Objects.hashCode(oExpMm);
        result = 31 * result + Arrays.hashCode(filler86);
        result = 31 * result + Integer.hashCode(oExpDd);
        result = 31 * result + Arrays.hashCode(filler87);
        result = 31 * result + Integer.hashCode(oExpCc);
        result = 31 * result + Integer.hashCode(oExpYy);
        result = 31 * result + Arrays.hashCode(filler88);
        result = 31 * result + Integer.hashCode(differenceInDays);
        result = 31 * result + Integer.hashCode(oDayNumber);
        result = 31 * result + Objects.hashCode(reqCountry);
        result = 31 * result + Objects.hashCode(outputNextMoYr);
        result = 31 * result + Objects.hashCode(outputLastMoYr);
        return result;
    }
    
    @Override
    public int compareTo(DateParmArea that) {
        int c = 0;
        c = Arrays.compare(this.filler23, that.filler23);
        if ( c != 0 ) return c;
        c = Integer.compare(this.reqDay, that.reqDay);
        if ( c != 0 ) return c;
        c = Integer.compare(this.manipulateReqType, that.manipulateReqType);
        if ( c != 0 ) return c;
        c = Integer.compare(this.numberOfDays, that.numberOfDays);
        if ( c != 0 ) return c;
        c = Integer.compare(this.numberOfMonths, that.numberOfMonths);
        if ( c != 0 ) return c;
        c = Integer.compare(this.inputDateType, that.inputDateType);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oIsoCc, that.oIsoCc);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oIsoYy, that.oIsoYy);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler74, that.filler74);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oIsoMm, that.oIsoMm);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler75, that.filler75);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oIsoDd, that.oIsoDd);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oUsaMm, that.oUsaMm);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler76, that.filler76);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oUsaDd, that.oUsaDd);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler77, that.filler77);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oUsaCc, that.oUsaCc);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oUsaYy, that.oUsaYy);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oEurDd, that.oEurDd);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler78, that.filler78);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oEurMm, that.oEurMm);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler79, that.filler79);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oEurCc, that.oEurCc);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oEurYy, that.oEurYy);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oJisCc, that.oJisCc);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oJisYy, that.oJisYy);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler80, that.filler80);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oJisMm, that.oJisMm);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler81, that.filler81);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oJisDd, that.oJisDd);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oMdySlshMm, that.oMdySlshMm);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler82, that.filler82);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oMdySlshDd, that.oMdySlshDd);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler83, that.filler83);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oMdySlshYy, that.oMdySlshYy);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oMdyMm, that.oMdyMm);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oMdyDd, that.oMdyDd);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oMdyYy, that.oMdyYy);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oMdcySlshMm, that.oMdcySlshMm);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler84, that.filler84);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oMdcySlshDd, that.oMdcySlshDd);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler85, that.filler85);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oMdcySlshCc, that.oMdcySlshCc);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oMdcySlshYy, that.oMdcySlshYy);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oMdcyMm, that.oMdcyMm);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oMdcyDd, that.oMdcyDd);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oMdcyCc, that.oMdcyCc);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oMdcyYy, that.oMdcyYy);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oYmdYy, that.oYmdYy);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oYmdMm, that.oYmdMm);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oYmdDd, that.oYmdDd);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oCymdCc, that.oCymdCc);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oCymdYy, that.oCymdYy);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oCymdMm, that.oCymdMm);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oCymdDd, that.oCymdDd);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oJulYy, that.oJulYy);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oJulDdd, that.oJulDdd);
        if ( c != 0 ) return c;
        c = this.oExpMm.compareTo(that.oExpMm);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler86, that.filler86);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oExpDd, that.oExpDd);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler87, that.filler87);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oExpCc, that.oExpCc);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oExpYy, that.oExpYy);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler88, that.filler88);
        if ( c != 0 ) return c;
        c = Integer.compare(this.differenceInDays, that.differenceInDays);
        if ( c != 0 ) return c;
        c = Integer.compare(this.oDayNumber, that.oDayNumber);
        if ( c != 0 ) return c;
        c = this.reqCountry.compareTo(that.reqCountry);
        if ( c != 0 ) return c;
        c = this.outputNextMoYr.compareTo(that.outputNextMoYr);
        if ( c != 0 ) return c;
        c = this.outputLastMoYr.compareTo(that.outputLastMoYr);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final ByteArrayField FILLER_23 = factory.getByteArrayField(1);
    private byte[] filler23 = new byte[1];
    private static final ExternalDecimalAsIntField REQ_DAY = factory.getExternalDecimalAsIntField(1, true);
    private static final ExternalDecimalAsIntField MANIPULATE_REQ_TYPE = factory.getExternalDecimalAsIntField(2, true);
    private static final BinaryAsIntField NUMBER_OF_DAYS = factory.getBinaryAsIntField(5, true);
    private static final BinaryAsIntField NUMBER_OF_MONTHS = factory.getBinaryAsIntField(5, true);
    private static final ExternalDecimalAsIntField INPUT_DATE_TYPE = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField O_ISO_CC = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField O_ISO_YY = factory.getExternalDecimalAsIntField(2, true);
    private static final ByteArrayField FILLER_74 = factory.getByteArrayField(1);
    private byte[] filler74 = new byte[1];
    private static final ExternalDecimalAsIntField O_ISO_MM = factory.getExternalDecimalAsIntField(2, true);
    private static final ByteArrayField FILLER_75 = factory.getByteArrayField(1);
    private byte[] filler75 = new byte[1];
    private static final ExternalDecimalAsIntField O_ISO_DD = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField O_USA_MM = factory.getExternalDecimalAsIntField(2, true);
    private static final ByteArrayField FILLER_76 = factory.getByteArrayField(1);
    private byte[] filler76 = new byte[1];
    private static final ExternalDecimalAsIntField O_USA_DD = factory.getExternalDecimalAsIntField(2, true);
    private static final ByteArrayField FILLER_77 = factory.getByteArrayField(1);
    private byte[] filler77 = new byte[1];
    private static final ExternalDecimalAsIntField O_USA_CC = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField O_USA_YY = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField O_EUR_DD = factory.getExternalDecimalAsIntField(2, true);
    private static final ByteArrayField FILLER_78 = factory.getByteArrayField(1);
    private byte[] filler78 = new byte[1];
    private static final ExternalDecimalAsIntField O_EUR_MM = factory.getExternalDecimalAsIntField(2, true);
    private static final ByteArrayField FILLER_79 = factory.getByteArrayField(1);
    private byte[] filler79 = new byte[1];
    private static final ExternalDecimalAsIntField O_EUR_CC = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField O_EUR_YY = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField O_JIS_CC = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField O_JIS_YY = factory.getExternalDecimalAsIntField(2, true);
    private static final ByteArrayField FILLER_80 = factory.getByteArrayField(1);
    private byte[] filler80 = new byte[1];
    private static final ExternalDecimalAsIntField O_JIS_MM = factory.getExternalDecimalAsIntField(2, true);
    private static final ByteArrayField FILLER_81 = factory.getByteArrayField(1);
    private byte[] filler81 = new byte[1];
    private static final ExternalDecimalAsIntField O_JIS_DD = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField O_MDY_SLSH_MM = factory.getExternalDecimalAsIntField(2, true);
    private static final ByteArrayField FILLER_82 = factory.getByteArrayField(1);
    private byte[] filler82 = new byte[1];
    private static final ExternalDecimalAsIntField O_MDY_SLSH_DD = factory.getExternalDecimalAsIntField(2, true);
    private static final ByteArrayField FILLER_83 = factory.getByteArrayField(1);
    private byte[] filler83 = new byte[1];
    private static final ExternalDecimalAsIntField O_MDY_SLSH_YY = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField O_MDY_MM = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField O_MDY_DD = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField O_MDY_YY = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField O_MDCY_SLSH_MM = factory.getExternalDecimalAsIntField(2, true);
    private static final ByteArrayField FILLER_84 = factory.getByteArrayField(1);
    private byte[] filler84 = new byte[1];
    private static final ExternalDecimalAsIntField O_MDCY_SLSH_DD = factory.getExternalDecimalAsIntField(2, true);
    private static final ByteArrayField FILLER_85 = factory.getByteArrayField(1);
    private byte[] filler85 = new byte[1];
    private static final ExternalDecimalAsIntField O_MDCY_SLSH_CC = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField O_MDCY_SLSH_YY = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField O_MDCY_MM = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField O_MDCY_DD = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField O_MDCY_CC = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField O_MDCY_YY = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField O_YMD_YY = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField O_YMD_MM = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField O_YMD_DD = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField O_CYMD_CC = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField O_CYMD_YY = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField O_CYMD_MM = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField O_CYMD_DD = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField O_JUL_YY = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField O_JUL_DDD = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField O_EXP_MM = factory.getStringField(3);
    private static final ByteArrayField FILLER_86 = factory.getByteArrayField(1);
    private byte[] filler86 = new byte[1];
    private static final ExternalDecimalAsIntField O_EXP_DD = factory.getExternalDecimalAsIntField(2, true);
    private static final ByteArrayField FILLER_87 = factory.getByteArrayField(2);
    private byte[] filler87 = new byte[2];
    private static final ExternalDecimalAsIntField O_EXP_CC = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField O_EXP_YY = factory.getExternalDecimalAsIntField(2, true);
    private static final ByteArrayField FILLER_88 = factory.getByteArrayField(8);
    private byte[] filler88 = new byte[8];
    private static final BinaryAsIntField DIFFERENCE_IN_DAYS = factory.getBinaryAsIntField(5, true);
    private static final ExternalDecimalAsIntField O_DAY_NUMBER = factory.getExternalDecimalAsIntField(1, true);
    private static final StringField REQ_COUNTRY = factory.getStringField(3);
    private static final ByteArrayField OUTPUT_NEXT_MO_YR = factory.getByteArrayField(OutputNextMoYr.SIZE);
    private static final ByteArrayField OUTPUT_LAST_MO_YR = factory.getByteArrayField(OutputLastMoYr.SIZE);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code DateParmArea} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code DATE-PARM-AREA} record
     * @see "DATE-PARM-AREA record at MXWW01.CPY:19"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        FILLER_23.putByteArray(filler23, bytes, offset);
        REQ_DAY.putInt(reqDay, bytes, offset);
        MANIPULATE_REQ_TYPE.putInt(manipulateReqType, bytes, offset);
        NUMBER_OF_DAYS.putInt(numberOfDays, bytes, offset);
        NUMBER_OF_MONTHS.putInt(numberOfMonths, bytes, offset);
        INPUT_DATE_TYPE.putInt(inputDateType, bytes, offset);
        O_ISO_CC.putInt(oIsoCc, bytes, offset);
        O_ISO_YY.putInt(oIsoYy, bytes, offset);
        FILLER_74.putByteArray(filler74, bytes, offset);
        O_ISO_MM.putInt(oIsoMm, bytes, offset);
        FILLER_75.putByteArray(filler75, bytes, offset);
        O_ISO_DD.putInt(oIsoDd, bytes, offset);
        O_USA_MM.putInt(oUsaMm, bytes, offset);
        FILLER_76.putByteArray(filler76, bytes, offset);
        O_USA_DD.putInt(oUsaDd, bytes, offset);
        FILLER_77.putByteArray(filler77, bytes, offset);
        O_USA_CC.putInt(oUsaCc, bytes, offset);
        O_USA_YY.putInt(oUsaYy, bytes, offset);
        O_EUR_DD.putInt(oEurDd, bytes, offset);
        FILLER_78.putByteArray(filler78, bytes, offset);
        O_EUR_MM.putInt(oEurMm, bytes, offset);
        FILLER_79.putByteArray(filler79, bytes, offset);
        O_EUR_CC.putInt(oEurCc, bytes, offset);
        O_EUR_YY.putInt(oEurYy, bytes, offset);
        O_JIS_CC.putInt(oJisCc, bytes, offset);
        O_JIS_YY.putInt(oJisYy, bytes, offset);
        FILLER_80.putByteArray(filler80, bytes, offset);
        O_JIS_MM.putInt(oJisMm, bytes, offset);
        FILLER_81.putByteArray(filler81, bytes, offset);
        O_JIS_DD.putInt(oJisDd, bytes, offset);
        O_MDY_SLSH_MM.putInt(oMdySlshMm, bytes, offset);
        FILLER_82.putByteArray(filler82, bytes, offset);
        O_MDY_SLSH_DD.putInt(oMdySlshDd, bytes, offset);
        FILLER_83.putByteArray(filler83, bytes, offset);
        O_MDY_SLSH_YY.putInt(oMdySlshYy, bytes, offset);
        O_MDY_MM.putInt(oMdyMm, bytes, offset);
        O_MDY_DD.putInt(oMdyDd, bytes, offset);
        O_MDY_YY.putInt(oMdyYy, bytes, offset);
        O_MDCY_SLSH_MM.putInt(oMdcySlshMm, bytes, offset);
        FILLER_84.putByteArray(filler84, bytes, offset);
        O_MDCY_SLSH_DD.putInt(oMdcySlshDd, bytes, offset);
        FILLER_85.putByteArray(filler85, bytes, offset);
        O_MDCY_SLSH_CC.putInt(oMdcySlshCc, bytes, offset);
        O_MDCY_SLSH_YY.putInt(oMdcySlshYy, bytes, offset);
        O_MDCY_MM.putInt(oMdcyMm, bytes, offset);
        O_MDCY_DD.putInt(oMdcyDd, bytes, offset);
        O_MDCY_CC.putInt(oMdcyCc, bytes, offset);
        O_MDCY_YY.putInt(oMdcyYy, bytes, offset);
        O_YMD_YY.putInt(oYmdYy, bytes, offset);
        O_YMD_MM.putInt(oYmdMm, bytes, offset);
        O_YMD_DD.putInt(oYmdDd, bytes, offset);
        O_CYMD_CC.putInt(oCymdCc, bytes, offset);
        O_CYMD_YY.putInt(oCymdYy, bytes, offset);
        O_CYMD_MM.putInt(oCymdMm, bytes, offset);
        O_CYMD_DD.putInt(oCymdDd, bytes, offset);
        O_JUL_YY.putInt(oJulYy, bytes, offset);
        O_JUL_DDD.putInt(oJulDdd, bytes, offset);
        O_EXP_MM.putString(oExpMm, bytes, offset);
        FILLER_86.putByteArray(filler86, bytes, offset);
        O_EXP_DD.putInt(oExpDd, bytes, offset);
        FILLER_87.putByteArray(filler87, bytes, offset);
        O_EXP_CC.putInt(oExpCc, bytes, offset);
        O_EXP_YY.putInt(oExpYy, bytes, offset);
        FILLER_88.putByteArray(filler88, bytes, offset);
        DIFFERENCE_IN_DAYS.putInt(differenceInDays, bytes, offset);
        O_DAY_NUMBER.putInt(oDayNumber, bytes, offset);
        REQ_COUNTRY.putString(reqCountry, bytes, offset);
        outputNextMoYr.getBytes(bytes, OUTPUT_NEXT_MO_YR.getOffset() + offset);
        outputLastMoYr.getBytes(bytes, OUTPUT_LAST_MO_YR.getOffset() + offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code DateParmArea} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code DateParmArea} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code DateParmArea} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code DATE-PARM-AREA} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "DATE-PARM-AREA record at MXWW01.CPY:19"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        FILLER_23.getByteArray(bytes, offset);
        reqDay = REQ_DAY.getInt(bytes, offset);
        manipulateReqType = MANIPULATE_REQ_TYPE.getInt(bytes, offset);
        numberOfDays = NUMBER_OF_DAYS.getInt(bytes, offset);
        numberOfMonths = NUMBER_OF_MONTHS.getInt(bytes, offset);
        inputDateType = INPUT_DATE_TYPE.getInt(bytes, offset);
        oIsoCc = O_ISO_CC.getInt(bytes, offset);
        oIsoYy = O_ISO_YY.getInt(bytes, offset);
        FILLER_74.getByteArray(bytes, offset);
        oIsoMm = O_ISO_MM.getInt(bytes, offset);
        FILLER_75.getByteArray(bytes, offset);
        oIsoDd = O_ISO_DD.getInt(bytes, offset);
        oUsaMm = O_USA_MM.getInt(bytes, offset);
        FILLER_76.getByteArray(bytes, offset);
        oUsaDd = O_USA_DD.getInt(bytes, offset);
        FILLER_77.getByteArray(bytes, offset);
        oUsaCc = O_USA_CC.getInt(bytes, offset);
        oUsaYy = O_USA_YY.getInt(bytes, offset);
        oEurDd = O_EUR_DD.getInt(bytes, offset);
        FILLER_78.getByteArray(bytes, offset);
        oEurMm = O_EUR_MM.getInt(bytes, offset);
        FILLER_79.getByteArray(bytes, offset);
        oEurCc = O_EUR_CC.getInt(bytes, offset);
        oEurYy = O_EUR_YY.getInt(bytes, offset);
        oJisCc = O_JIS_CC.getInt(bytes, offset);
        oJisYy = O_JIS_YY.getInt(bytes, offset);
        FILLER_80.getByteArray(bytes, offset);
        oJisMm = O_JIS_MM.getInt(bytes, offset);
        FILLER_81.getByteArray(bytes, offset);
        oJisDd = O_JIS_DD.getInt(bytes, offset);
        oMdySlshMm = O_MDY_SLSH_MM.getInt(bytes, offset);
        FILLER_82.getByteArray(bytes, offset);
        oMdySlshDd = O_MDY_SLSH_DD.getInt(bytes, offset);
        FILLER_83.getByteArray(bytes, offset);
        oMdySlshYy = O_MDY_SLSH_YY.getInt(bytes, offset);
        oMdyMm = O_MDY_MM.getInt(bytes, offset);
        oMdyDd = O_MDY_DD.getInt(bytes, offset);
        oMdyYy = O_MDY_YY.getInt(bytes, offset);
        oMdcySlshMm = O_MDCY_SLSH_MM.getInt(bytes, offset);
        FILLER_84.getByteArray(bytes, offset);
        oMdcySlshDd = O_MDCY_SLSH_DD.getInt(bytes, offset);
        FILLER_85.getByteArray(bytes, offset);
        oMdcySlshCc = O_MDCY_SLSH_CC.getInt(bytes, offset);
        oMdcySlshYy = O_MDCY_SLSH_YY.getInt(bytes, offset);
        oMdcyMm = O_MDCY_MM.getInt(bytes, offset);
        oMdcyDd = O_MDCY_DD.getInt(bytes, offset);
        oMdcyCc = O_MDCY_CC.getInt(bytes, offset);
        oMdcyYy = O_MDCY_YY.getInt(bytes, offset);
        oYmdYy = O_YMD_YY.getInt(bytes, offset);
        oYmdMm = O_YMD_MM.getInt(bytes, offset);
        oYmdDd = O_YMD_DD.getInt(bytes, offset);
        oCymdCc = O_CYMD_CC.getInt(bytes, offset);
        oCymdYy = O_CYMD_YY.getInt(bytes, offset);
        oCymdMm = O_CYMD_MM.getInt(bytes, offset);
        oCymdDd = O_CYMD_DD.getInt(bytes, offset);
        oJulYy = O_JUL_YY.getInt(bytes, offset);
        oJulDdd = O_JUL_DDD.getInt(bytes, offset);
        oExpMm = O_EXP_MM.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_86.getByteArray(bytes, offset);
        oExpDd = O_EXP_DD.getInt(bytes, offset);
        FILLER_87.getByteArray(bytes, offset);
        oExpCc = O_EXP_CC.getInt(bytes, offset);
        oExpYy = O_EXP_YY.getInt(bytes, offset);
        FILLER_88.getByteArray(bytes, offset);
        differenceInDays = DIFFERENCE_IN_DAYS.getInt(bytes, offset);
        oDayNumber = O_DAY_NUMBER.getInt(bytes, offset);
        reqCountry = REQ_COUNTRY.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        outputNextMoYr.setBytes(bytes, OUTPUT_NEXT_MO_YR.getOffset() + offset);
        outputLastMoYr.setBytes(bytes, OUTPUT_LAST_MO_YR.getOffset() + offset);
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
