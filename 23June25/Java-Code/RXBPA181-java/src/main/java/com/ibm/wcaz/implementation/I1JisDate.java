package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class I1JisDate extends DateParmArea {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String i1JisCc = "";
    private String i1JisYy = "";
    private String i1JisMm = "";
    private String i1JisDd = "";
    
    /** Initialize fields to non-null default values */
    public I1JisDate() {}
    
    /** Initialize all fields to provided values */
    public I1JisDate(int reqDay, int manipulateReqType, int numberOfDays, int numberOfMonths, int inputDateType, int oIsoCc, int oIsoYy, int oIsoMm, int oIsoDd, int oUsaMm, int oUsaDd, int oUsaCc, int oUsaYy, int oEurDd, int oEurMm, int oEurCc, int oEurYy, int oJisCc, int oJisYy, int oJisMm, int oJisDd, int oMdySlshMm, int oMdySlshDd, int oMdySlshYy, int oMdyMm, int oMdyDd, int oMdyYy, int oMdcySlshMm, int oMdcySlshDd, int oMdcySlshCc, int oMdcySlshYy, int oMdcyMm, int oMdcyDd, int oMdcyCc, int oMdcyYy, int oYmdYy, int oYmdMm, int oYmdDd, int oCymdCc, int oCymdYy, int oCymdMm, int oCymdDd, int oJulYy, int oJulDdd, String oExpMm, int oExpDd, int oExpCc, int oExpYy, int differenceInDays, int oDayNumber, String reqCountry, OutputNextMoYr outputNextMoYr, OutputLastMoYr outputLastMoYr, String i1JisCc, String i1JisYy, String i1JisMm, String i1JisDd) {
        super(reqDay, manipulateReqType, numberOfDays, numberOfMonths, inputDateType, oIsoCc, oIsoYy, oIsoMm, oIsoDd, oUsaMm, oUsaDd, oUsaCc, oUsaYy, oEurDd, oEurMm, oEurCc, oEurYy, oJisCc, oJisYy, oJisMm, oJisDd, oMdySlshMm, oMdySlshDd, oMdySlshYy, oMdyMm, oMdyDd, oMdyYy, oMdcySlshMm, oMdcySlshDd, oMdcySlshCc, oMdcySlshYy, oMdcyMm, oMdcyDd, oMdcyCc, oMdcyYy, oYmdYy, oYmdMm, oYmdDd, oCymdCc, oCymdYy, oCymdMm, oCymdDd, oJulYy, oJulDdd, oExpMm, oExpDd, oExpCc, oExpYy, differenceInDays, oDayNumber, reqCountry, outputNextMoYr, outputLastMoYr);
        this.i1JisCc = i1JisCc;
        this.i1JisYy = i1JisYy;
        this.i1JisMm = i1JisMm;
        this.i1JisDd = i1JisDd;
    }
    
    @Override
    public I1JisDate clone() throws CloneNotSupportedException {
        I1JisDate cloned = (I1JisDate) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code I1JisDate} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected I1JisDate(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code I1JisDate} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected I1JisDate(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I1JisDate} object
     * @see #setBytes(byte[], int)
     */
    public static I1JisDate fromBytes(byte[] bytes, int offset) {
        return new I1JisDate(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I1JisDate} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static I1JisDate fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code I1JisDate} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static I1JisDate fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getI1JisCc() {
        return this.i1JisCc;
    }
    
    public void setI1JisCc(String i1JisCc) {
        this.i1JisCc = i1JisCc;
    }
    
    public String getI1JisYy() {
        return this.i1JisYy;
    }
    
    public void setI1JisYy(String i1JisYy) {
        this.i1JisYy = i1JisYy;
    }
    
    public String getI1JisMm() {
        return this.i1JisMm;
    }
    
    public void setI1JisMm(String i1JisMm) {
        this.i1JisMm = i1JisMm;
    }
    
    public String getI1JisDd() {
        return this.i1JisDd;
    }
    
    public void setI1JisDd(String i1JisDd) {
        this.i1JisDd = i1JisDd;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        i1JisCc = "";
        i1JisYy = "";
        i1JisMm = "";
        i1JisDd = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ i1JisCc=\"");
        s.append(getI1JisCc());
        s.append("\"");
        s.append(", i1JisYy=\"");
        s.append(getI1JisYy());
        s.append("\"");
        s.append(", filler33=\"");
        s.append(new String(filler33, encoding));
        s.append("\"");
        s.append(", i1JisMm=\"");
        s.append(getI1JisMm());
        s.append("\"");
        s.append(", filler34=\"");
        s.append(new String(filler34, encoding));
        s.append("\"");
        s.append(", i1JisDd=\"");
        s.append(getI1JisDd());
        s.append("\"");
        s.append(", filler35=\"");
        s.append(new String(filler35, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(I1JisDate that) {
        return super.equals(that) &&
            this.i1JisCc.equals(that.i1JisCc) &&
            this.i1JisYy.equals(that.i1JisYy) &&
            Arrays.equals(this.filler33, that.filler33) &&
            this.i1JisMm.equals(that.i1JisMm) &&
            Arrays.equals(this.filler34, that.filler34) &&
            this.i1JisDd.equals(that.i1JisDd) &&
            Arrays.equals(this.filler35, that.filler35);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof I1JisDate other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof I1JisDate;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(i1JisCc);
        result = 31 * result + Objects.hashCode(i1JisYy);
        result = 31 * result + Arrays.hashCode(filler33);
        result = 31 * result + Objects.hashCode(i1JisMm);
        result = 31 * result + Arrays.hashCode(filler34);
        result = 31 * result + Objects.hashCode(i1JisDd);
        result = 31 * result + Arrays.hashCode(filler35);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(I1JisDate that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.i1JisCc.compareTo(that.i1JisCc);
        if ( c != 0 ) return c;
        c = this.i1JisYy.compareTo(that.i1JisYy);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler33, that.filler33);
        if ( c != 0 ) return c;
        c = this.i1JisMm.compareTo(that.i1JisMm);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler34, that.filler34);
        if ( c != 0 ) return c;
        c = this.i1JisDd.compareTo(that.i1JisDd);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler35, that.filler35);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(DateParmArea that) {
        if (that instanceof I1JisDate other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(DateParmArea.SIZE);
    }
    
    private static final StringField I_1_JIS_CC = factory.getStringField(2);
    private static final StringField I_1_JIS_YY = factory.getStringField(2);
    private static final ByteArrayField FILLER_33 = factory.getByteArrayField(1);
    private byte[] filler33 = new byte[1];
    private static final StringField I_1_JIS_MM = factory.getStringField(2);
    private static final ByteArrayField FILLER_34 = factory.getByteArrayField(1);
    private byte[] filler34 = new byte[1];
    private static final StringField I_1_JIS_DD = factory.getStringField(2);
    private static final ByteArrayField FILLER_35 = factory.getByteArrayField(2);
    private byte[] filler35 = new byte[2];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "I1-JIS-DATE record at MXWW01.CPY:119"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        I_1_JIS_CC.putString(i1JisCc, bytes, offset);
        I_1_JIS_YY.putString(i1JisYy, bytes, offset);
        FILLER_33.putByteArray(filler33, bytes, offset);
        I_1_JIS_MM.putString(i1JisMm, bytes, offset);
        FILLER_34.putByteArray(filler34, bytes, offset);
        I_1_JIS_DD.putString(i1JisDd, bytes, offset);
        FILLER_35.putByteArray(filler35, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#setBytes(byte[], int)} to set parent-class state.
     * @see "I1-JIS-DATE record at MXWW01.CPY:119"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        i1JisCc = I_1_JIS_CC.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        i1JisYy = I_1_JIS_YY.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_33.getByteArray(bytes, offset);
        i1JisMm = I_1_JIS_MM.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_34.getByteArray(bytes, offset);
        i1JisDd = I_1_JIS_DD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_35.getByteArray(bytes, offset);
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
