package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class I2UsaDate extends DateParmArea {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String i2UsaMm = "";
    private String i2UsaDd = "";
    private String i2UsaCc = "";
    private String i2UsaYy = "";
    
    /** Initialize fields to non-null default values */
    public I2UsaDate() {}
    
    /** Initialize all fields to provided values */
    public I2UsaDate(int reqDay, int manipulateReqType, int numberOfDays, int numberOfMonths, int inputDateType, int oIsoCc, int oIsoYy, int oIsoMm, int oIsoDd, int oUsaMm, int oUsaDd, int oUsaCc, int oUsaYy, int oEurDd, int oEurMm, int oEurCc, int oEurYy, int oJisCc, int oJisYy, int oJisMm, int oJisDd, int oMdySlshMm, int oMdySlshDd, int oMdySlshYy, int oMdyMm, int oMdyDd, int oMdyYy, int oMdcySlshMm, int oMdcySlshDd, int oMdcySlshCc, int oMdcySlshYy, int oMdcyMm, int oMdcyDd, int oMdcyCc, int oMdcyYy, int oYmdYy, int oYmdMm, int oYmdDd, int oCymdCc, int oCymdYy, int oCymdMm, int oCymdDd, int oJulYy, int oJulDdd, String oExpMm, int oExpDd, int oExpCc, int oExpYy, int differenceInDays, int oDayNumber, String reqCountry, OutputNextMoYr outputNextMoYr, OutputLastMoYr outputLastMoYr, String i2UsaMm, String i2UsaDd, String i2UsaCc, String i2UsaYy) {
        super(reqDay, manipulateReqType, numberOfDays, numberOfMonths, inputDateType, oIsoCc, oIsoYy, oIsoMm, oIsoDd, oUsaMm, oUsaDd, oUsaCc, oUsaYy, oEurDd, oEurMm, oEurCc, oEurYy, oJisCc, oJisYy, oJisMm, oJisDd, oMdySlshMm, oMdySlshDd, oMdySlshYy, oMdyMm, oMdyDd, oMdyYy, oMdcySlshMm, oMdcySlshDd, oMdcySlshCc, oMdcySlshYy, oMdcyMm, oMdcyDd, oMdcyCc, oMdcyYy, oYmdYy, oYmdMm, oYmdDd, oCymdCc, oCymdYy, oCymdMm, oCymdDd, oJulYy, oJulDdd, oExpMm, oExpDd, oExpCc, oExpYy, differenceInDays, oDayNumber, reqCountry, outputNextMoYr, outputLastMoYr);
        this.i2UsaMm = i2UsaMm;
        this.i2UsaDd = i2UsaDd;
        this.i2UsaCc = i2UsaCc;
        this.i2UsaYy = i2UsaYy;
    }
    
    @Override
    public I2UsaDate clone() throws CloneNotSupportedException {
        I2UsaDate cloned = (I2UsaDate) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code I2UsaDate} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected I2UsaDate(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code I2UsaDate} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected I2UsaDate(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I2UsaDate} object
     * @see #setBytes(byte[], int)
     */
    public static I2UsaDate fromBytes(byte[] bytes, int offset) {
        return new I2UsaDate(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I2UsaDate} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static I2UsaDate fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code I2UsaDate} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static I2UsaDate fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getI2UsaMm() {
        return this.i2UsaMm;
    }
    
    public void setI2UsaMm(String i2UsaMm) {
        this.i2UsaMm = i2UsaMm;
    }
    
    public String getI2UsaDd() {
        return this.i2UsaDd;
    }
    
    public void setI2UsaDd(String i2UsaDd) {
        this.i2UsaDd = i2UsaDd;
    }
    
    public String getI2UsaCc() {
        return this.i2UsaCc;
    }
    
    public void setI2UsaCc(String i2UsaCc) {
        this.i2UsaCc = i2UsaCc;
    }
    
    public String getI2UsaYy() {
        return this.i2UsaYy;
    }
    
    public void setI2UsaYy(String i2UsaYy) {
        this.i2UsaYy = i2UsaYy;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        i2UsaMm = "";
        i2UsaDd = "";
        i2UsaCc = "";
        i2UsaYy = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ i2UsaMm=\"");
        s.append(getI2UsaMm());
        s.append("\"");
        s.append(", filler52=\"");
        s.append(new String(filler52, encoding));
        s.append("\"");
        s.append(", i2UsaDd=\"");
        s.append(getI2UsaDd());
        s.append("\"");
        s.append(", filler53=\"");
        s.append(new String(filler53, encoding));
        s.append("\"");
        s.append(", i2UsaCc=\"");
        s.append(getI2UsaCc());
        s.append("\"");
        s.append(", i2UsaYy=\"");
        s.append(getI2UsaYy());
        s.append("\"");
        s.append(", filler54=\"");
        s.append(new String(filler54, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(I2UsaDate that) {
        return super.equals(that) &&
            this.i2UsaMm.equals(that.i2UsaMm) &&
            Arrays.equals(this.filler52, that.filler52) &&
            this.i2UsaDd.equals(that.i2UsaDd) &&
            Arrays.equals(this.filler53, that.filler53) &&
            this.i2UsaCc.equals(that.i2UsaCc) &&
            this.i2UsaYy.equals(that.i2UsaYy) &&
            Arrays.equals(this.filler54, that.filler54);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof I2UsaDate other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof I2UsaDate;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(i2UsaMm);
        result = 31 * result + Arrays.hashCode(filler52);
        result = 31 * result + Objects.hashCode(i2UsaDd);
        result = 31 * result + Arrays.hashCode(filler53);
        result = 31 * result + Objects.hashCode(i2UsaCc);
        result = 31 * result + Objects.hashCode(i2UsaYy);
        result = 31 * result + Arrays.hashCode(filler54);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(I2UsaDate that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.i2UsaMm.compareTo(that.i2UsaMm);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler52, that.filler52);
        if ( c != 0 ) return c;
        c = this.i2UsaDd.compareTo(that.i2UsaDd);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler53, that.filler53);
        if ( c != 0 ) return c;
        c = this.i2UsaCc.compareTo(that.i2UsaCc);
        if ( c != 0 ) return c;
        c = this.i2UsaYy.compareTo(that.i2UsaYy);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler54, that.filler54);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(DateParmArea that) {
        if (that instanceof I2UsaDate other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(DateParmArea.SIZE);
    }
    
    private static final StringField I_2_USA_MM = factory.getStringField(2);
    private static final ByteArrayField FILLER_52 = factory.getByteArrayField(1);
    private byte[] filler52 = new byte[1];
    private static final StringField I_2_USA_DD = factory.getStringField(2);
    private static final ByteArrayField FILLER_53 = factory.getByteArrayField(1);
    private byte[] filler53 = new byte[1];
    private static final StringField I_2_USA_CC = factory.getStringField(2);
    private static final StringField I_2_USA_YY = factory.getStringField(2);
    private static final ByteArrayField FILLER_54 = factory.getByteArrayField(2);
    private byte[] filler54 = new byte[2];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "I2-USA-DATE record at MXWW01.CPY:190"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        I_2_USA_MM.putString(i2UsaMm, bytes, offset);
        FILLER_52.putByteArray(filler52, bytes, offset);
        I_2_USA_DD.putString(i2UsaDd, bytes, offset);
        FILLER_53.putByteArray(filler53, bytes, offset);
        I_2_USA_CC.putString(i2UsaCc, bytes, offset);
        I_2_USA_YY.putString(i2UsaYy, bytes, offset);
        FILLER_54.putByteArray(filler54, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#setBytes(byte[], int)} to set parent-class state.
     * @see "I2-USA-DATE record at MXWW01.CPY:190"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        i2UsaMm = I_2_USA_MM.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_52.getByteArray(bytes, offset);
        i2UsaDd = I_2_USA_DD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_53.getByteArray(bytes, offset);
        i2UsaCc = I_2_USA_CC.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        i2UsaYy = I_2_USA_YY.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_54.getByteArray(bytes, offset);
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
