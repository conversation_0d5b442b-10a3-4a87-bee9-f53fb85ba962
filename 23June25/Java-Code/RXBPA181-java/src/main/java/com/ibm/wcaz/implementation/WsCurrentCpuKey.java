package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class WsCurrentCpuKey implements Cloneable, Comparable<WsCurrentCpuKey> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String wsCurrentCdfCustId = "";
    private String wsCurrentCdfCustCode = "";
    private String wsCurrentCreatDate = "";
    private String wsCurrentCreatTime = "";
    
    /** Initialize fields to non-null default values */
    public WsCurrentCpuKey() {}
    
    /** Initialize all fields to provided values */
    public WsCurrentCpuKey(String wsCurrentCdfCustId, String wsCurrentCdfCustCode, String wsCurrentCreatDate, String wsCurrentCreatTime) {
        this.wsCurrentCdfCustId = wsCurrentCdfCustId;
        this.wsCurrentCdfCustCode = wsCurrentCdfCustCode;
        this.wsCurrentCreatDate = wsCurrentCreatDate;
        this.wsCurrentCreatTime = wsCurrentCreatTime;
    }
    
    @Override
    public WsCurrentCpuKey clone() throws CloneNotSupportedException {
        WsCurrentCpuKey cloned = (WsCurrentCpuKey) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code WsCurrentCpuKey} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected WsCurrentCpuKey(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code WsCurrentCpuKey} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected WsCurrentCpuKey(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsCurrentCpuKey} object
     * @see #setBytes(byte[], int)
     */
    public static WsCurrentCpuKey fromBytes(byte[] bytes, int offset) {
        return new WsCurrentCpuKey(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsCurrentCpuKey} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static WsCurrentCpuKey fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code WsCurrentCpuKey} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static WsCurrentCpuKey fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getWsCurrentCdfCustId() {
        return this.wsCurrentCdfCustId;
    }
    
    public void setWsCurrentCdfCustId(String wsCurrentCdfCustId) {
        this.wsCurrentCdfCustId = wsCurrentCdfCustId;
    }
    
    public String getWsCurrentCdfCustCode() {
        return this.wsCurrentCdfCustCode;
    }
    
    public void setWsCurrentCdfCustCode(String wsCurrentCdfCustCode) {
        this.wsCurrentCdfCustCode = wsCurrentCdfCustCode;
    }
    
    public String getWsCurrentCreatDate() {
        return this.wsCurrentCreatDate;
    }
    
    public void setWsCurrentCreatDate(String wsCurrentCreatDate) {
        this.wsCurrentCreatDate = wsCurrentCreatDate;
    }
    
    public String getWsCurrentCreatTime() {
        return this.wsCurrentCreatTime;
    }
    
    public void setWsCurrentCreatTime(String wsCurrentCreatTime) {
        this.wsCurrentCreatTime = wsCurrentCreatTime;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        wsCurrentCdfCustId = "";
        wsCurrentCdfCustCode = "";
        wsCurrentCreatDate = "";
        wsCurrentCreatTime = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ wsCurrentCdfCustId=\"");
        s.append(getWsCurrentCdfCustId());
        s.append("\"");
        s.append(", wsCurrentCdfCustCode=\"");
        s.append(getWsCurrentCdfCustCode());
        s.append("\"");
        s.append(", wsCurrentCreatDate=\"");
        s.append(getWsCurrentCreatDate());
        s.append("\"");
        s.append(", wsCurrentCreatTime=\"");
        s.append(getWsCurrentCreatTime());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(WsCurrentCpuKey that) {
        return this.wsCurrentCdfCustId.equals(that.wsCurrentCdfCustId) &&
            this.wsCurrentCdfCustCode.equals(that.wsCurrentCdfCustCode) &&
            this.wsCurrentCreatDate.equals(that.wsCurrentCreatDate) &&
            this.wsCurrentCreatTime.equals(that.wsCurrentCreatTime);
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof WsCurrentCpuKey other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof WsCurrentCpuKey;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Objects.hashCode(wsCurrentCdfCustId);
        result = 31 * result + Objects.hashCode(wsCurrentCdfCustCode);
        result = 31 * result + Objects.hashCode(wsCurrentCreatDate);
        result = 31 * result + Objects.hashCode(wsCurrentCreatTime);
        return result;
    }
    
    @Override
    public int compareTo(WsCurrentCpuKey that) {
        int c = 0;
        c = this.wsCurrentCdfCustId.compareTo(that.wsCurrentCdfCustId);
        if ( c != 0 ) return c;
        c = this.wsCurrentCdfCustCode.compareTo(that.wsCurrentCdfCustCode);
        if ( c != 0 ) return c;
        c = this.wsCurrentCreatDate.compareTo(that.wsCurrentCreatDate);
        if ( c != 0 ) return c;
        c = this.wsCurrentCreatTime.compareTo(that.wsCurrentCreatTime);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final StringField WS_CURRENT_CDF_CUST_ID = factory.getStringField(4);
    private static final StringField WS_CURRENT_CDF_CUST_CODE = factory.getStringField(4);
    private static final StringField WS_CURRENT_CREAT_DATE = factory.getStringField(10);
    private static final StringField WS_CURRENT_CREAT_TIME = factory.getStringField(5);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsCurrentCpuKey} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code WS-CURRENT-CPU-KEY} record
     * @see "WS-CURRENT-CPU-KEY record at RXBPASVC.cbl:138"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        WS_CURRENT_CDF_CUST_ID.putString(wsCurrentCdfCustId, bytes, offset);
        WS_CURRENT_CDF_CUST_CODE.putString(wsCurrentCdfCustCode, bytes, offset);
        WS_CURRENT_CREAT_DATE.putString(wsCurrentCreatDate, bytes, offset);
        WS_CURRENT_CREAT_TIME.putString(wsCurrentCreatTime, bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsCurrentCpuKey} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsCurrentCpuKey} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code WsCurrentCpuKey} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code WS-CURRENT-CPU-KEY} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "WS-CURRENT-CPU-KEY record at RXBPASVC.cbl:138"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        wsCurrentCdfCustId = WS_CURRENT_CDF_CUST_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        wsCurrentCdfCustCode = WS_CURRENT_CDF_CUST_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        wsCurrentCreatDate = WS_CURRENT_CREAT_DATE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        wsCurrentCreatTime = WS_CURRENT_CREAT_TIME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
