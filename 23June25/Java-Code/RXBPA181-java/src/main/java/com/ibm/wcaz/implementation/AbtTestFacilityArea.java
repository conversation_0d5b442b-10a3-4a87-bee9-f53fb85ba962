package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class AbtTestFacilityArea implements Cloneable, Comparable<AbtTestFacilityArea> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private static final char ABT_TEST_FACILITY_ACTIVE_VALUE = 'Y';
    private static final char ABT_TEST_FACILITY_NOT_ACTIVE_VALUE = 'N';
    private static final char ABT_TEST_MODE_ABT_VALUE = 'A';
    private static final char ABT_TEST_MODE_PGM_VALUE = 'P';
    
    private char abtTestFacilityInd = 'N';
    private char abtTestModeInd = 'P';
    private String abtTestFacilityReserve = "\0".repeat(4);
    
    /** Initialize fields to non-null default values */
    public AbtTestFacilityArea() {
        initFiller();
    }
    
    /** Initialize all fields to provided values */
    public AbtTestFacilityArea(char abtTestFacilityInd, char abtTestModeInd, String abtTestFacilityReserve) {
        this.abtTestFacilityInd = abtTestFacilityInd;
        this.abtTestModeInd = abtTestModeInd;
        this.abtTestFacilityReserve = abtTestFacilityReserve;
        initFiller();
    }
    
    @Override
    public AbtTestFacilityArea clone() throws CloneNotSupportedException {
        AbtTestFacilityArea cloned = (AbtTestFacilityArea) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code AbtTestFacilityArea} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected AbtTestFacilityArea(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code AbtTestFacilityArea} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected AbtTestFacilityArea(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code AbtTestFacilityArea} object
     * @see #setBytes(byte[], int)
     */
    public static AbtTestFacilityArea fromBytes(byte[] bytes, int offset) {
        return new AbtTestFacilityArea(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code AbtTestFacilityArea} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static AbtTestFacilityArea fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code AbtTestFacilityArea} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static AbtTestFacilityArea fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public char getAbtTestFacilityInd() {
        return this.abtTestFacilityInd;
    }
    
    public void setAbtTestFacilityInd(char abtTestFacilityInd) {
        this.abtTestFacilityInd = abtTestFacilityInd;
    }
    
    public boolean isAbtTestFacilityActive() {
        return abtTestFacilityInd == ABT_TEST_FACILITY_ACTIVE_VALUE;
    }
    
    public void setAbtTestFacilityActive() {
        abtTestFacilityInd = ABT_TEST_FACILITY_ACTIVE_VALUE;
    }
    
    public boolean isAbtTestFacilityNotActive() {
        return abtTestFacilityInd == ABT_TEST_FACILITY_NOT_ACTIVE_VALUE;
    }
    
    public void setAbtTestFacilityNotActive() {
        abtTestFacilityInd = ABT_TEST_FACILITY_NOT_ACTIVE_VALUE;
    }
    
    public char getAbtTestModeInd() {
        return this.abtTestModeInd;
    }
    
    public void setAbtTestModeInd(char abtTestModeInd) {
        this.abtTestModeInd = abtTestModeInd;
    }
    
    public boolean isAbtTestModeAbt() {
        return abtTestModeInd == ABT_TEST_MODE_ABT_VALUE;
    }
    
    public void setAbtTestModeAbt() {
        abtTestModeInd = ABT_TEST_MODE_ABT_VALUE;
    }
    
    public boolean isAbtTestModePgm() {
        return abtTestModeInd == ABT_TEST_MODE_PGM_VALUE;
    }
    
    public void setAbtTestModePgm() {
        abtTestModeInd = ABT_TEST_MODE_PGM_VALUE;
    }
    
    public String getAbtTestFacilityReserve() {
        return this.abtTestFacilityReserve;
    }
    
    public void setAbtTestFacilityReserve(String abtTestFacilityReserve) {
        this.abtTestFacilityReserve = abtTestFacilityReserve;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        abtTestFacilityInd = ' ';
        abtTestModeInd = ' ';
        abtTestFacilityReserve = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ abtTestFacilityInd=\"");
        s.append(getAbtTestFacilityInd());
        s.append("\"");
        s.append(", abtTestModeInd=\"");
        s.append(getAbtTestModeInd());
        s.append("\"");
        s.append(", filler7=\"");
        s.append(new String(filler7, encoding));
        s.append("\"");
        s.append(", abtTestFacilityReserve=\"");
        s.append(getAbtTestFacilityReserve());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(AbtTestFacilityArea that) {
        return this.abtTestFacilityInd == that.abtTestFacilityInd &&
            this.abtTestModeInd == that.abtTestModeInd &&
            Arrays.equals(this.filler7, that.filler7) &&
            this.abtTestFacilityReserve.equals(that.abtTestFacilityReserve);
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof AbtTestFacilityArea other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof AbtTestFacilityArea;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Character.hashCode(abtTestFacilityInd);
        result = 31 * result + Character.hashCode(abtTestModeInd);
        result = 31 * result + Arrays.hashCode(filler7);
        result = 31 * result + Objects.hashCode(abtTestFacilityReserve);
        return result;
    }
    
    @Override
    public int compareTo(AbtTestFacilityArea that) {
        int c = 0;
        c = Character.compare(this.abtTestFacilityInd, that.abtTestFacilityInd);
        if ( c != 0 ) return c;
        c = Character.compare(this.abtTestModeInd, that.abtTestModeInd);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler7, that.filler7);
        if ( c != 0 ) return c;
        c = this.abtTestFacilityReserve.compareTo(that.abtTestFacilityReserve);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final StringField ABT_TEST_FACILITY_IND = factory.getStringField(1);
    private static final StringField ABT_TEST_MODE_IND = factory.getStringField(1);
    private static final ByteArrayField FILLER_7 = factory.getByteArrayField(2);
    private byte[] filler7 = new byte[2];
    private static final StringField ABT_TEST_FACILITY_RESERVE = factory.getStringField(4);
    private void initFiller() {
        new StringField(0, 2).putString("\0\0", filler7);
    }
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code AbtTestFacilityArea} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code ABT-TEST-FACILITY-AREA} record
     * @see "ABT-TEST-FACILITY-AREA record at MXWW03.CPY:7"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        ABT_TEST_FACILITY_IND.putString(Character.toString(abtTestFacilityInd), bytes, offset);
        ABT_TEST_MODE_IND.putString(Character.toString(abtTestModeInd), bytes, offset);
        FILLER_7.putByteArray(filler7, bytes, offset);
        ABT_TEST_FACILITY_RESERVE.putString(abtTestFacilityReserve, bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code AbtTestFacilityArea} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code AbtTestFacilityArea} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code AbtTestFacilityArea} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code ABT-TEST-FACILITY-AREA} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "ABT-TEST-FACILITY-AREA record at MXWW03.CPY:7"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        abtTestFacilityInd = ABT_TEST_FACILITY_IND.getString(bytes, offset).charAt(0);
        abtTestModeInd = ABT_TEST_MODE_IND.getString(bytes, offset).charAt(0);
        FILLER_7.getByteArray(bytes, offset);
        abtTestFacilityReserve = ABT_TEST_FACILITY_RESERVE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
