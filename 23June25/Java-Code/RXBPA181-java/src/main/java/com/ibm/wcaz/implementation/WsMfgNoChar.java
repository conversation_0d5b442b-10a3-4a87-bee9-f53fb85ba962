package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class WsMfgNoChar extends WsSaveArea {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String wsSaveMfgNo1St3 = "";
    private String wsSaveMfgNoLast6 = "";
    
    /** Initialize fields to non-null default values */
    public WsMfgNoChar() {}
    
    /** Initialize all fields to provided values */
    public WsMfgNoChar(String wsSaveCdfCustId, String wsSaveCdfCustCode, String wsSaveCreatDate, String wsSaveCreatTime, String wsSaveMfgName, String wsSaveMfgNo1St3, String wsSaveMfgNoLast6) {
        super(wsSaveCdfCustId, wsSaveCdfCustCode, wsSaveCreatDate, wsSaveCreatTime, wsSaveMfgName);
        this.wsSaveMfgNo1St3 = wsSaveMfgNo1St3;
        this.wsSaveMfgNoLast6 = wsSaveMfgNoLast6;
    }
    
    @Override
    public WsMfgNoChar clone() throws CloneNotSupportedException {
        WsMfgNoChar cloned = (WsMfgNoChar) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code WsMfgNoChar} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected WsMfgNoChar(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code WsMfgNoChar} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected WsMfgNoChar(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsMfgNoChar} object
     * @see #setBytes(byte[], int)
     */
    public static WsMfgNoChar fromBytes(byte[] bytes, int offset) {
        return new WsMfgNoChar(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsMfgNoChar} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static WsMfgNoChar fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code WsMfgNoChar} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static WsMfgNoChar fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getWsSaveMfgNo1St3() {
        return this.wsSaveMfgNo1St3;
    }
    
    public void setWsSaveMfgNo1St3(String wsSaveMfgNo1St3) {
        this.wsSaveMfgNo1St3 = wsSaveMfgNo1St3;
    }
    
    public String getWsSaveMfgNoLast6() {
        return this.wsSaveMfgNoLast6;
    }
    
    public void setWsSaveMfgNoLast6(String wsSaveMfgNoLast6) {
        this.wsSaveMfgNoLast6 = wsSaveMfgNoLast6;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        wsSaveMfgNo1St3 = "";
        wsSaveMfgNoLast6 = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ wsSaveMfgNo1St3=\"");
        s.append(getWsSaveMfgNo1St3());
        s.append("\"");
        s.append(", wsSaveMfgNoLast6=\"");
        s.append(getWsSaveMfgNoLast6());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(WsMfgNoChar that) {
        return super.equals(that) &&
            this.wsSaveMfgNo1St3.equals(that.wsSaveMfgNo1St3) &&
            this.wsSaveMfgNoLast6.equals(that.wsSaveMfgNoLast6);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof WsMfgNoChar other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof WsMfgNoChar;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(wsSaveMfgNo1St3);
        result = 31 * result + Objects.hashCode(wsSaveMfgNoLast6);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(WsMfgNoChar that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.wsSaveMfgNo1St3.compareTo(that.wsSaveMfgNo1St3);
        if ( c != 0 ) return c;
        c = this.wsSaveMfgNoLast6.compareTo(that.wsSaveMfgNoLast6);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(WsSaveArea that) {
        if (that instanceof WsMfgNoChar other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(WsSaveArea.SIZE);
    }
    
    private static final StringField WS_SAVE_MFG_NO_1_ST_3 = factory.getStringField(3);
    private static final StringField WS_SAVE_MFG_NO_LAST_6 = factory.getStringField(6);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link WsSaveArea#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "WS-MFG-NO-CHAR record at RXBPASVC.cbl:104"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        WS_SAVE_MFG_NO_1_ST_3.putString(wsSaveMfgNo1St3, bytes, offset);
        WS_SAVE_MFG_NO_LAST_6.putString(wsSaveMfgNoLast6, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link WsSaveArea#setBytes(byte[], int)} to set parent-class state.
     * @see "WS-MFG-NO-CHAR record at RXBPASVC.cbl:104"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        wsSaveMfgNo1St3 = WS_SAVE_MFG_NO_1_ST_3.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        wsSaveMfgNoLast6 = WS_SAVE_MFG_NO_LAST_6.getString(bytes, offset).replaceFirst("\\s+\\z", "");
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
