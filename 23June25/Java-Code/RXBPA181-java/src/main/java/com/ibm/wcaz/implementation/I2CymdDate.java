package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class I2CymdDate extends DateParmArea {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String i2CymdCc = "";
    private String i2CymdYy = "";
    private String i2CymdMm = "";
    private String i2CymdDd = "";
    
    /** Initialize fields to non-null default values */
    public I2CymdDate() {}
    
    /** Initialize all fields to provided values */
    public I2CymdDate(int reqDay, int manipulateReqType, int numberOfDays, int numberOfMonths, int inputDateType, int oIsoCc, int oIsoYy, int oIsoMm, int oIsoDd, int oUsaMm, int oUsaDd, int oUsaCc, int oUsaYy, int oEurDd, int oEurMm, int oEurCc, int oEurYy, int oJisCc, int oJisYy, int oJisMm, int oJisDd, int oMdySlshMm, int oMdySlshDd, int oMdySlshYy, int oMdyMm, int oMdyDd, int oMdyYy, int oMdcySlshMm, int oMdcySlshDd, int oMdcySlshCc, int oMdcySlshYy, int oMdcyMm, int oMdcyDd, int oMdcyCc, int oMdcyYy, int oYmdYy, int oYmdMm, int oYmdDd, int oCymdCc, int oCymdYy, int oCymdMm, int oCymdDd, int oJulYy, int oJulDdd, String oExpMm, int oExpDd, int oExpCc, int oExpYy, int differenceInDays, int oDayNumber, String reqCountry, OutputNextMoYr outputNextMoYr, OutputLastMoYr outputLastMoYr, String i2CymdCc, String i2CymdYy, String i2CymdMm, String i2CymdDd) {
        super(reqDay, manipulateReqType, numberOfDays, numberOfMonths, inputDateType, oIsoCc, oIsoYy, oIsoMm, oIsoDd, oUsaMm, oUsaDd, oUsaCc, oUsaYy, oEurDd, oEurMm, oEurCc, oEurYy, oJisCc, oJisYy, oJisMm, oJisDd, oMdySlshMm, oMdySlshDd, oMdySlshYy, oMdyMm, oMdyDd, oMdyYy, oMdcySlshMm, oMdcySlshDd, oMdcySlshCc, oMdcySlshYy, oMdcyMm, oMdcyDd, oMdcyCc, oMdcyYy, oYmdYy, oYmdMm, oYmdDd, oCymdCc, oCymdYy, oCymdMm, oCymdDd, oJulYy, oJulDdd, oExpMm, oExpDd, oExpCc, oExpYy, differenceInDays, oDayNumber, reqCountry, outputNextMoYr, outputLastMoYr);
        this.i2CymdCc = i2CymdCc;
        this.i2CymdYy = i2CymdYy;
        this.i2CymdMm = i2CymdMm;
        this.i2CymdDd = i2CymdDd;
    }
    
    @Override
    public I2CymdDate clone() throws CloneNotSupportedException {
        I2CymdDate cloned = (I2CymdDate) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code I2CymdDate} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected I2CymdDate(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code I2CymdDate} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected I2CymdDate(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I2CymdDate} object
     * @see #setBytes(byte[], int)
     */
    public static I2CymdDate fromBytes(byte[] bytes, int offset) {
        return new I2CymdDate(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I2CymdDate} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static I2CymdDate fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code I2CymdDate} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static I2CymdDate fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getI2CymdCc() {
        return this.i2CymdCc;
    }
    
    public void setI2CymdCc(String i2CymdCc) {
        this.i2CymdCc = i2CymdCc;
    }
    
    public String getI2CymdYy() {
        return this.i2CymdYy;
    }
    
    public void setI2CymdYy(String i2CymdYy) {
        this.i2CymdYy = i2CymdYy;
    }
    
    public String getI2CymdMm() {
        return this.i2CymdMm;
    }
    
    public void setI2CymdMm(String i2CymdMm) {
        this.i2CymdMm = i2CymdMm;
    }
    
    public String getI2CymdDd() {
        return this.i2CymdDd;
    }
    
    public void setI2CymdDd(String i2CymdDd) {
        this.i2CymdDd = i2CymdDd;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        i2CymdCc = "";
        i2CymdYy = "";
        i2CymdMm = "";
        i2CymdDd = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ i2CymdCc=\"");
        s.append(getI2CymdCc());
        s.append("\"");
        s.append(", i2CymdYy=\"");
        s.append(getI2CymdYy());
        s.append("\"");
        s.append(", i2CymdMm=\"");
        s.append(getI2CymdMm());
        s.append("\"");
        s.append(", i2CymdDd=\"");
        s.append(getI2CymdDd());
        s.append("\"");
        s.append(", filler70=\"");
        s.append(new String(filler70, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(I2CymdDate that) {
        return super.equals(that) &&
            this.i2CymdCc.equals(that.i2CymdCc) &&
            this.i2CymdYy.equals(that.i2CymdYy) &&
            this.i2CymdMm.equals(that.i2CymdMm) &&
            this.i2CymdDd.equals(that.i2CymdDd) &&
            Arrays.equals(this.filler70, that.filler70);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof I2CymdDate other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof I2CymdDate;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(i2CymdCc);
        result = 31 * result + Objects.hashCode(i2CymdYy);
        result = 31 * result + Objects.hashCode(i2CymdMm);
        result = 31 * result + Objects.hashCode(i2CymdDd);
        result = 31 * result + Arrays.hashCode(filler70);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(I2CymdDate that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.i2CymdCc.compareTo(that.i2CymdCc);
        if ( c != 0 ) return c;
        c = this.i2CymdYy.compareTo(that.i2CymdYy);
        if ( c != 0 ) return c;
        c = this.i2CymdMm.compareTo(that.i2CymdMm);
        if ( c != 0 ) return c;
        c = this.i2CymdDd.compareTo(that.i2CymdDd);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler70, that.filler70);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(DateParmArea that) {
        if (that instanceof I2CymdDate other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(DateParmArea.SIZE);
    }
    
    private static final StringField I_2_CYMD_CC = factory.getStringField(2);
    private static final StringField I_2_CYMD_YY = factory.getStringField(2);
    private static final StringField I_2_CYMD_MM = factory.getStringField(2);
    private static final StringField I_2_CYMD_DD = factory.getStringField(2);
    private static final ByteArrayField FILLER_70 = factory.getByteArrayField(4);
    private byte[] filler70 = new byte[4];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "I2-CYMD-DATE record at MXWW01.CPY:250"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        I_2_CYMD_CC.putString(i2CymdCc, bytes, offset);
        I_2_CYMD_YY.putString(i2CymdYy, bytes, offset);
        I_2_CYMD_MM.putString(i2CymdMm, bytes, offset);
        I_2_CYMD_DD.putString(i2CymdDd, bytes, offset);
        FILLER_70.putByteArray(filler70, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#setBytes(byte[], int)} to set parent-class state.
     * @see "I2-CYMD-DATE record at MXWW01.CPY:250"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        i2CymdCc = I_2_CYMD_CC.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        i2CymdYy = I_2_CYMD_YY.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        i2CymdMm = I_2_CYMD_MM.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        i2CymdDd = I_2_CYMD_DD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_70.getByteArray(bytes, offset);
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
