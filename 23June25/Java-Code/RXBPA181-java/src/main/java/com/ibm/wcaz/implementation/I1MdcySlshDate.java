package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class I1MdcySlshDate extends DateParmArea {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String i1MdcySlshMm = "";
    private String i1MdcySlshDd = "";
    private String i1MdcySlshCc = "";
    private String i1MdcySlshYy = "";
    
    /** Initialize fields to non-null default values */
    public I1MdcySlshDate() {}
    
    /** Initialize all fields to provided values */
    public I1MdcySlshDate(int reqDay, int manipulateReqType, int numberOfDays, int numberOfMonths, int inputDateType, int oIsoCc, int oIsoYy, int oIsoMm, int oIsoDd, int oUsaMm, int oUsaDd, int oUsaCc, int oUsaYy, int oEurDd, int oEurMm, int oEurCc, int oEurYy, int oJisCc, int oJisYy, int oJisMm, int oJisDd, int oMdySlshMm, int oMdySlshDd, int oMdySlshYy, int oMdyMm, int oMdyDd, int oMdyYy, int oMdcySlshMm, int oMdcySlshDd, int oMdcySlshCc, int oMdcySlshYy, int oMdcyMm, int oMdcyDd, int oMdcyCc, int oMdcyYy, int oYmdYy, int oYmdMm, int oYmdDd, int oCymdCc, int oCymdYy, int oCymdMm, int oCymdDd, int oJulYy, int oJulDdd, String oExpMm, int oExpDd, int oExpCc, int oExpYy, int differenceInDays, int oDayNumber, String reqCountry, OutputNextMoYr outputNextMoYr, OutputLastMoYr outputLastMoYr, String i1MdcySlshMm, String i1MdcySlshDd, String i1MdcySlshCc, String i1MdcySlshYy) {
        super(reqDay, manipulateReqType, numberOfDays, numberOfMonths, inputDateType, oIsoCc, oIsoYy, oIsoMm, oIsoDd, oUsaMm, oUsaDd, oUsaCc, oUsaYy, oEurDd, oEurMm, oEurCc, oEurYy, oJisCc, oJisYy, oJisMm, oJisDd, oMdySlshMm, oMdySlshDd, oMdySlshYy, oMdyMm, oMdyDd, oMdyYy, oMdcySlshMm, oMdcySlshDd, oMdcySlshCc, oMdcySlshYy, oMdcyMm, oMdcyDd, oMdcyCc, oMdcyYy, oYmdYy, oYmdMm, oYmdDd, oCymdCc, oCymdYy, oCymdMm, oCymdDd, oJulYy, oJulDdd, oExpMm, oExpDd, oExpCc, oExpYy, differenceInDays, oDayNumber, reqCountry, outputNextMoYr, outputLastMoYr);
        this.i1MdcySlshMm = i1MdcySlshMm;
        this.i1MdcySlshDd = i1MdcySlshDd;
        this.i1MdcySlshCc = i1MdcySlshCc;
        this.i1MdcySlshYy = i1MdcySlshYy;
    }
    
    @Override
    public I1MdcySlshDate clone() throws CloneNotSupportedException {
        I1MdcySlshDate cloned = (I1MdcySlshDate) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code I1MdcySlshDate} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected I1MdcySlshDate(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code I1MdcySlshDate} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected I1MdcySlshDate(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I1MdcySlshDate} object
     * @see #setBytes(byte[], int)
     */
    public static I1MdcySlshDate fromBytes(byte[] bytes, int offset) {
        return new I1MdcySlshDate(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I1MdcySlshDate} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static I1MdcySlshDate fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code I1MdcySlshDate} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static I1MdcySlshDate fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getI1MdcySlshMm() {
        return this.i1MdcySlshMm;
    }
    
    public void setI1MdcySlshMm(String i1MdcySlshMm) {
        this.i1MdcySlshMm = i1MdcySlshMm;
    }
    
    public String getI1MdcySlshDd() {
        return this.i1MdcySlshDd;
    }
    
    public void setI1MdcySlshDd(String i1MdcySlshDd) {
        this.i1MdcySlshDd = i1MdcySlshDd;
    }
    
    public String getI1MdcySlshCc() {
        return this.i1MdcySlshCc;
    }
    
    public void setI1MdcySlshCc(String i1MdcySlshCc) {
        this.i1MdcySlshCc = i1MdcySlshCc;
    }
    
    public String getI1MdcySlshYy() {
        return this.i1MdcySlshYy;
    }
    
    public void setI1MdcySlshYy(String i1MdcySlshYy) {
        this.i1MdcySlshYy = i1MdcySlshYy;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        i1MdcySlshMm = "";
        i1MdcySlshDd = "";
        i1MdcySlshCc = "";
        i1MdcySlshYy = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ i1MdcySlshMm=\"");
        s.append(getI1MdcySlshMm());
        s.append("\"");
        s.append(", filler40=\"");
        s.append(new String(filler40, encoding));
        s.append("\"");
        s.append(", i1MdcySlshDd=\"");
        s.append(getI1MdcySlshDd());
        s.append("\"");
        s.append(", filler41=\"");
        s.append(new String(filler41, encoding));
        s.append("\"");
        s.append(", i1MdcySlshCc=\"");
        s.append(getI1MdcySlshCc());
        s.append("\"");
        s.append(", i1MdcySlshYy=\"");
        s.append(getI1MdcySlshYy());
        s.append("\"");
        s.append(", filler42=\"");
        s.append(new String(filler42, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(I1MdcySlshDate that) {
        return super.equals(that) &&
            this.i1MdcySlshMm.equals(that.i1MdcySlshMm) &&
            Arrays.equals(this.filler40, that.filler40) &&
            this.i1MdcySlshDd.equals(that.i1MdcySlshDd) &&
            Arrays.equals(this.filler41, that.filler41) &&
            this.i1MdcySlshCc.equals(that.i1MdcySlshCc) &&
            this.i1MdcySlshYy.equals(that.i1MdcySlshYy) &&
            Arrays.equals(this.filler42, that.filler42);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof I1MdcySlshDate other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof I1MdcySlshDate;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(i1MdcySlshMm);
        result = 31 * result + Arrays.hashCode(filler40);
        result = 31 * result + Objects.hashCode(i1MdcySlshDd);
        result = 31 * result + Arrays.hashCode(filler41);
        result = 31 * result + Objects.hashCode(i1MdcySlshCc);
        result = 31 * result + Objects.hashCode(i1MdcySlshYy);
        result = 31 * result + Arrays.hashCode(filler42);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(I1MdcySlshDate that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.i1MdcySlshMm.compareTo(that.i1MdcySlshMm);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler40, that.filler40);
        if ( c != 0 ) return c;
        c = this.i1MdcySlshDd.compareTo(that.i1MdcySlshDd);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler41, that.filler41);
        if ( c != 0 ) return c;
        c = this.i1MdcySlshCc.compareTo(that.i1MdcySlshCc);
        if ( c != 0 ) return c;
        c = this.i1MdcySlshYy.compareTo(that.i1MdcySlshYy);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler42, that.filler42);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(DateParmArea that) {
        if (that instanceof I1MdcySlshDate other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(DateParmArea.SIZE);
    }
    
    private static final StringField I_1_MDCY_SLSH_MM = factory.getStringField(2);
    private static final ByteArrayField FILLER_40 = factory.getByteArrayField(1);
    private byte[] filler40 = new byte[1];
    private static final StringField I_1_MDCY_SLSH_DD = factory.getStringField(2);
    private static final ByteArrayField FILLER_41 = factory.getByteArrayField(1);
    private byte[] filler41 = new byte[1];
    private static final StringField I_1_MDCY_SLSH_CC = factory.getStringField(2);
    private static final StringField I_1_MDCY_SLSH_YY = factory.getStringField(2);
    private static final ByteArrayField FILLER_42 = factory.getByteArrayField(2);
    private byte[] filler42 = new byte[2];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "I1-MDCY-SLSH-DATE record at MXWW01.CPY:140"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        I_1_MDCY_SLSH_MM.putString(i1MdcySlshMm, bytes, offset);
        FILLER_40.putByteArray(filler40, bytes, offset);
        I_1_MDCY_SLSH_DD.putString(i1MdcySlshDd, bytes, offset);
        FILLER_41.putByteArray(filler41, bytes, offset);
        I_1_MDCY_SLSH_CC.putString(i1MdcySlshCc, bytes, offset);
        I_1_MDCY_SLSH_YY.putString(i1MdcySlshYy, bytes, offset);
        FILLER_42.putByteArray(filler42, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#setBytes(byte[], int)} to set parent-class state.
     * @see "I1-MDCY-SLSH-DATE record at MXWW01.CPY:140"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        i1MdcySlshMm = I_1_MDCY_SLSH_MM.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_40.getByteArray(bytes, offset);
        i1MdcySlshDd = I_1_MDCY_SLSH_DD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_41.getByteArray(bytes, offset);
        i1MdcySlshCc = I_1_MDCY_SLSH_CC.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        i1MdcySlshYy = I_1_MDCY_SLSH_YY.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_42.getByteArray(bytes, offset);
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
