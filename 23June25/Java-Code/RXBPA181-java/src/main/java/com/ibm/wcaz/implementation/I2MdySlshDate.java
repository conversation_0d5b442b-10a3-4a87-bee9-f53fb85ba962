package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class I2MdySlshDate extends DateParmArea {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String i2MdySlshMm = "";
    private String i2MdySlshDd = "";
    private String i2MdySlshYy = "";
    
    /** Initialize fields to non-null default values */
    public I2MdySlshDate() {}
    
    /** Initialize all fields to provided values */
    public I2MdySlshDate(int reqDay, int manipulateReqType, int numberOfDays, int numberOfMonths, int inputDateType, int oIsoCc, int oIsoYy, int oIsoMm, int oIsoDd, int oUsaMm, int oUsaDd, int oUsaCc, int oUsaYy, int oEurDd, int oEurMm, int oEurCc, int oEurYy, int oJisCc, int oJisYy, int oJisMm, int oJisDd, int oMdySlshMm, int oMdySlshDd, int oMdySlshYy, int oMdyMm, int oMdyDd, int oMdyYy, int oMdcySlshMm, int oMdcySlshDd, int oMdcySlshCc, int oMdcySlshYy, int oMdcyMm, int oMdcyDd, int oMdcyCc, int oMdcyYy, int oYmdYy, int oYmdMm, int oYmdDd, int oCymdCc, int oCymdYy, int oCymdMm, int oCymdDd, int oJulYy, int oJulDdd, String oExpMm, int oExpDd, int oExpCc, int oExpYy, int differenceInDays, int oDayNumber, String reqCountry, OutputNextMoYr outputNextMoYr, OutputLastMoYr outputLastMoYr, String i2MdySlshMm, String i2MdySlshDd, String i2MdySlshYy) {
        super(reqDay, manipulateReqType, numberOfDays, numberOfMonths, inputDateType, oIsoCc, oIsoYy, oIsoMm, oIsoDd, oUsaMm, oUsaDd, oUsaCc, oUsaYy, oEurDd, oEurMm, oEurCc, oEurYy, oJisCc, oJisYy, oJisMm, oJisDd, oMdySlshMm, oMdySlshDd, oMdySlshYy, oMdyMm, oMdyDd, oMdyYy, oMdcySlshMm, oMdcySlshDd, oMdcySlshCc, oMdcySlshYy, oMdcyMm, oMdcyDd, oMdcyCc, oMdcyYy, oYmdYy, oYmdMm, oYmdDd, oCymdCc, oCymdYy, oCymdMm, oCymdDd, oJulYy, oJulDdd, oExpMm, oExpDd, oExpCc, oExpYy, differenceInDays, oDayNumber, reqCountry, outputNextMoYr, outputLastMoYr);
        this.i2MdySlshMm = i2MdySlshMm;
        this.i2MdySlshDd = i2MdySlshDd;
        this.i2MdySlshYy = i2MdySlshYy;
    }
    
    @Override
    public I2MdySlshDate clone() throws CloneNotSupportedException {
        I2MdySlshDate cloned = (I2MdySlshDate) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code I2MdySlshDate} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected I2MdySlshDate(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code I2MdySlshDate} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected I2MdySlshDate(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I2MdySlshDate} object
     * @see #setBytes(byte[], int)
     */
    public static I2MdySlshDate fromBytes(byte[] bytes, int offset) {
        return new I2MdySlshDate(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I2MdySlshDate} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static I2MdySlshDate fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code I2MdySlshDate} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static I2MdySlshDate fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getI2MdySlshMm() {
        return this.i2MdySlshMm;
    }
    
    public void setI2MdySlshMm(String i2MdySlshMm) {
        this.i2MdySlshMm = i2MdySlshMm;
    }
    
    public String getI2MdySlshDd() {
        return this.i2MdySlshDd;
    }
    
    public void setI2MdySlshDd(String i2MdySlshDd) {
        this.i2MdySlshDd = i2MdySlshDd;
    }
    
    public String getI2MdySlshYy() {
        return this.i2MdySlshYy;
    }
    
    public void setI2MdySlshYy(String i2MdySlshYy) {
        this.i2MdySlshYy = i2MdySlshYy;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        i2MdySlshMm = "";
        i2MdySlshDd = "";
        i2MdySlshYy = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ i2MdySlshMm=\"");
        s.append(getI2MdySlshMm());
        s.append("\"");
        s.append(", filler61=\"");
        s.append(new String(filler61, encoding));
        s.append("\"");
        s.append(", i2MdySlshDd=\"");
        s.append(getI2MdySlshDd());
        s.append("\"");
        s.append(", filler62=\"");
        s.append(new String(filler62, encoding));
        s.append("\"");
        s.append(", i2MdySlshYy=\"");
        s.append(getI2MdySlshYy());
        s.append("\"");
        s.append(", filler63=\"");
        s.append(new String(filler63, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(I2MdySlshDate that) {
        return super.equals(that) &&
            this.i2MdySlshMm.equals(that.i2MdySlshMm) &&
            Arrays.equals(this.filler61, that.filler61) &&
            this.i2MdySlshDd.equals(that.i2MdySlshDd) &&
            Arrays.equals(this.filler62, that.filler62) &&
            this.i2MdySlshYy.equals(that.i2MdySlshYy) &&
            Arrays.equals(this.filler63, that.filler63);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof I2MdySlshDate other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof I2MdySlshDate;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(i2MdySlshMm);
        result = 31 * result + Arrays.hashCode(filler61);
        result = 31 * result + Objects.hashCode(i2MdySlshDd);
        result = 31 * result + Arrays.hashCode(filler62);
        result = 31 * result + Objects.hashCode(i2MdySlshYy);
        result = 31 * result + Arrays.hashCode(filler63);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(I2MdySlshDate that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.i2MdySlshMm.compareTo(that.i2MdySlshMm);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler61, that.filler61);
        if ( c != 0 ) return c;
        c = this.i2MdySlshDd.compareTo(that.i2MdySlshDd);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler62, that.filler62);
        if ( c != 0 ) return c;
        c = this.i2MdySlshYy.compareTo(that.i2MdySlshYy);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler63, that.filler63);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(DateParmArea that) {
        if (that instanceof I2MdySlshDate other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(DateParmArea.SIZE);
    }
    
    private static final StringField I_2_MDY_SLSH_MM = factory.getStringField(2);
    private static final ByteArrayField FILLER_61 = factory.getByteArrayField(1);
    private byte[] filler61 = new byte[1];
    private static final StringField I_2_MDY_SLSH_DD = factory.getStringField(2);
    private static final ByteArrayField FILLER_62 = factory.getByteArrayField(1);
    private byte[] filler62 = new byte[1];
    private static final StringField I_2_MDY_SLSH_YY = factory.getStringField(2);
    private static final ByteArrayField FILLER_63 = factory.getByteArrayField(4);
    private byte[] filler63 = new byte[4];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "I2-MDY-SLSH-DATE record at MXWW01.CPY:217"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        I_2_MDY_SLSH_MM.putString(i2MdySlshMm, bytes, offset);
        FILLER_61.putByteArray(filler61, bytes, offset);
        I_2_MDY_SLSH_DD.putString(i2MdySlshDd, bytes, offset);
        FILLER_62.putByteArray(filler62, bytes, offset);
        I_2_MDY_SLSH_YY.putString(i2MdySlshYy, bytes, offset);
        FILLER_63.putByteArray(filler63, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#setBytes(byte[], int)} to set parent-class state.
     * @see "I2-MDY-SLSH-DATE record at MXWW01.CPY:217"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        i2MdySlshMm = I_2_MDY_SLSH_MM.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_61.getByteArray(bytes, offset);
        i2MdySlshDd = I_2_MDY_SLSH_DD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_62.getByteArray(bytes, offset);
        i2MdySlshYy = I_2_MDY_SLSH_YY.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_63.getByteArray(bytes, offset);
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
