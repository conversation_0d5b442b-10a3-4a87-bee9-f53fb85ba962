package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class AbtProgramFunction extends AbtPgmErrorData {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String abtProgramFunction = "";
    
    /** Initialize fields to non-null default values */
    public AbtProgramFunction() {}
    
    /** Initialize all fields to provided values */
    public AbtProgramFunction(String abtErrorActivity, int abtErrorAbendCode, String abtProgramFunction) {
        super(abtErrorActivity, abtErrorAbendCode);
        this.abtProgramFunction = abtProgramFunction;
    }
    
    @Override
    public AbtProgramFunction clone() throws CloneNotSupportedException {
        AbtProgramFunction cloned = (AbtProgramFunction) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code AbtProgramFunction} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected AbtProgramFunction(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code AbtProgramFunction} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected AbtProgramFunction(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code AbtProgramFunction} object
     * @see #setBytes(byte[], int)
     */
    public static AbtProgramFunction fromBytes(byte[] bytes, int offset) {
        return new AbtProgramFunction(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code AbtProgramFunction} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static AbtProgramFunction fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code AbtProgramFunction} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static AbtProgramFunction fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getAbtProgramFunction() {
        return this.abtProgramFunction;
    }
    
    public void setAbtProgramFunction(String abtProgramFunction) {
        this.abtProgramFunction = abtProgramFunction;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        abtProgramFunction = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ abtProgramFunction=\"");
        s.append(getAbtProgramFunction());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(AbtProgramFunction that) {
        return super.equals(that) &&
            this.abtProgramFunction.equals(that.abtProgramFunction);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof AbtProgramFunction other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof AbtProgramFunction;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(abtProgramFunction);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(AbtProgramFunction that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.abtProgramFunction.compareTo(that.abtProgramFunction);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(AbtPgmErrorData that) {
        if (that instanceof AbtProgramFunction other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(AbtPgmErrorData.SIZE);
    }
    
    private static final StringField ABT_PROGRAM_FUNCTION = factory.getStringField(8);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link AbtPgmErrorData#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "ABT-PROGRAM-FUNCTION record at MXWW03.CPY:56"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        ABT_PROGRAM_FUNCTION.putString(abtProgramFunction, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link AbtPgmErrorData#setBytes(byte[], int)} to set parent-class state.
     * @see "ABT-PROGRAM-FUNCTION record at MXWW03.CPY:56"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        abtProgramFunction = ABT_PROGRAM_FUNCTION.getString(bytes, offset).replaceFirst("\\s+\\z", "");
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
