package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.ExternalDecimalAsIntField;
import java.nio.charset.Charset;
import java.util.Arrays;

public class Filler92 extends OutputNextMoYr {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private int outputNextCent;
    
    /** Initialize fields to non-null default values */
    public Filler92() {}
    
    /** Initialize all fields to provided values */
    public Filler92(int outputNextMonth, int outputNextCent) {
        super(outputNextMonth);
        this.outputNextCent = outputNextCent;
    }
    
    @Override
    public Filler92 clone() throws CloneNotSupportedException {
        Filler92 cloned = (Filler92) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code Filler92} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected Filler92(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code Filler92} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected Filler92(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code Filler92} object
     * @see #setBytes(byte[], int)
     */
    public static Filler92 fromBytes(byte[] bytes, int offset) {
        return new Filler92(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code Filler92} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static Filler92 fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code Filler92} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static Filler92 fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public int getOutputNextCent() {
        return this.outputNextCent;
    }
    
    public void setOutputNextCent(int outputNextCent) {
        this.outputNextCent = outputNextCent;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        outputNextCent = 0;
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ outputNextCent=\"");
        s.append(getOutputNextCent());
        s.append("\"");
        s.append(", filler93=\"");
        s.append(new String(filler93, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(Filler92 that) {
        return super.equals(that) &&
            this.outputNextCent == that.outputNextCent &&
            Arrays.equals(this.filler93, that.filler93);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof Filler92 other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof Filler92;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Integer.hashCode(outputNextCent);
        result = 31 * result + Arrays.hashCode(filler93);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(Filler92 that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = Integer.compare(this.outputNextCent, that.outputNextCent);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler93, that.filler93);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(OutputNextMoYr that) {
        if (that instanceof Filler92 other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(OutputNextMoYr.SIZE);
    }
    
    private static final ExternalDecimalAsIntField OUTPUT_NEXT_CENT = factory.getExternalDecimalAsIntField(2, true);
    private static final ByteArrayField FILLER_93 = factory.getByteArrayField(2);
    private byte[] filler93 = new byte[2];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link OutputNextMoYr#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "FILLER #92 record at MXWW01.CPY:385"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        OUTPUT_NEXT_CENT.putInt(outputNextCent, bytes, offset);
        FILLER_93.putByteArray(filler93, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link OutputNextMoYr#setBytes(byte[], int)} to set parent-class state.
     * @see "FILLER #92 record at MXWW01.CPY:385"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        outputNextCent = OUTPUT_NEXT_CENT.getInt(bytes, offset);
        FILLER_93.getByteArray(bytes, offset);
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
