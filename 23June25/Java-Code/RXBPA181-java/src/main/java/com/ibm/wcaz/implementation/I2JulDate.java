package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class I2JulDate extends DateParmArea {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String i2JulYy = "";
    private String i2JulDdd = "";
    
    /** Initialize fields to non-null default values */
    public I2JulDate() {}
    
    /** Initialize all fields to provided values */
    public I2JulDate(int reqDay, int manipulateReqType, int numberOfDays, int numberOfMonths, int inputDateType, int oIsoCc, int oIsoYy, int oIsoMm, int oIsoDd, int oUsaMm, int oUsaDd, int oUsaCc, int oUsaYy, int oEurDd, int oEurMm, int oEurCc, int oEurYy, int oJisCc, int oJisYy, int oJisMm, int oJisDd, int oMdySlshMm, int oMdySlshDd, int oMdySlshYy, int oMdyMm, int oMdyDd, int oMdyYy, int oMdcySlshMm, int oMdcySlshDd, int oMdcySlshCc, int oMdcySlshYy, int oMdcyMm, int oMdcyDd, int oMdcyCc, int oMdcyYy, int oYmdYy, int oYmdMm, int oYmdDd, int oCymdCc, int oCymdYy, int oCymdMm, int oCymdDd, int oJulYy, int oJulDdd, String oExpMm, int oExpDd, int oExpCc, int oExpYy, int differenceInDays, int oDayNumber, String reqCountry, OutputNextMoYr outputNextMoYr, OutputLastMoYr outputLastMoYr, String i2JulYy, String i2JulDdd) {
        super(reqDay, manipulateReqType, numberOfDays, numberOfMonths, inputDateType, oIsoCc, oIsoYy, oIsoMm, oIsoDd, oUsaMm, oUsaDd, oUsaCc, oUsaYy, oEurDd, oEurMm, oEurCc, oEurYy, oJisCc, oJisYy, oJisMm, oJisDd, oMdySlshMm, oMdySlshDd, oMdySlshYy, oMdyMm, oMdyDd, oMdyYy, oMdcySlshMm, oMdcySlshDd, oMdcySlshCc, oMdcySlshYy, oMdcyMm, oMdcyDd, oMdcyCc, oMdcyYy, oYmdYy, oYmdMm, oYmdDd, oCymdCc, oCymdYy, oCymdMm, oCymdDd, oJulYy, oJulDdd, oExpMm, oExpDd, oExpCc, oExpYy, differenceInDays, oDayNumber, reqCountry, outputNextMoYr, outputLastMoYr);
        this.i2JulYy = i2JulYy;
        this.i2JulDdd = i2JulDdd;
    }
    
    @Override
    public I2JulDate clone() throws CloneNotSupportedException {
        I2JulDate cloned = (I2JulDate) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code I2JulDate} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected I2JulDate(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code I2JulDate} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected I2JulDate(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I2JulDate} object
     * @see #setBytes(byte[], int)
     */
    public static I2JulDate fromBytes(byte[] bytes, int offset) {
        return new I2JulDate(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I2JulDate} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static I2JulDate fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code I2JulDate} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static I2JulDate fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getI2JulYy() {
        return this.i2JulYy;
    }
    
    public void setI2JulYy(String i2JulYy) {
        this.i2JulYy = i2JulYy;
    }
    
    public String getI2JulDdd() {
        return this.i2JulDdd;
    }
    
    public void setI2JulDdd(String i2JulDdd) {
        this.i2JulDdd = i2JulDdd;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        i2JulYy = "";
        i2JulDdd = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ i2JulYy=\"");
        s.append(getI2JulYy());
        s.append("\"");
        s.append(", i2JulDdd=\"");
        s.append(getI2JulDdd());
        s.append("\"");
        s.append(", filler71=\"");
        s.append(new String(filler71, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(I2JulDate that) {
        return super.equals(that) &&
            this.i2JulYy.equals(that.i2JulYy) &&
            this.i2JulDdd.equals(that.i2JulDdd) &&
            Arrays.equals(this.filler71, that.filler71);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof I2JulDate other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof I2JulDate;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(i2JulYy);
        result = 31 * result + Objects.hashCode(i2JulDdd);
        result = 31 * result + Arrays.hashCode(filler71);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(I2JulDate that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.i2JulYy.compareTo(that.i2JulYy);
        if ( c != 0 ) return c;
        c = this.i2JulDdd.compareTo(that.i2JulDdd);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler71, that.filler71);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(DateParmArea that) {
        if (that instanceof I2JulDate other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(DateParmArea.SIZE);
    }
    
    private static final StringField I_2_JUL_YY = factory.getStringField(2);
    private static final StringField I_2_JUL_DDD = factory.getStringField(3);
    private static final ByteArrayField FILLER_71 = factory.getByteArrayField(7);
    private byte[] filler71 = new byte[7];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "I2-JUL-DATE record at MXWW01.CPY:260"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        I_2_JUL_YY.putString(i2JulYy, bytes, offset);
        I_2_JUL_DDD.putString(i2JulDdd, bytes, offset);
        FILLER_71.putByteArray(filler71, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#setBytes(byte[], int)} to set parent-class state.
     * @see "I2-JUL-DATE record at MXWW01.CPY:260"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        i2JulYy = I_2_JUL_YY.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        i2JulDdd = I_2_JUL_DDD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_71.getByteArray(bytes, offset);
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
