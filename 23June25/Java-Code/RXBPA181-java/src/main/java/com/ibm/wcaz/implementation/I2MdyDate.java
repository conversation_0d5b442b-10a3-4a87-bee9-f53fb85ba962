package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class I2MdyDate extends DateParmArea {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String i2MdyMm = "";
    private String i2MdyDd = "";
    private String i2MdyYy = "";
    
    /** Initialize fields to non-null default values */
    public I2MdyDate() {}
    
    /** Initialize all fields to provided values */
    public I2MdyDate(int reqDay, int manipulateReqType, int numberOfDays, int numberOfMonths, int inputDateType, int oIsoCc, int oIsoYy, int oIsoMm, int oIsoDd, int oUsaMm, int oUsaDd, int oUsaCc, int oUsaYy, int oEurDd, int oEurMm, int oEurCc, int oEurYy, int oJisCc, int oJisYy, int oJisMm, int oJisDd, int oMdySlshMm, int oMdySlshDd, int oMdySlshYy, int oMdyMm, int oMdyDd, int oMdyYy, int oMdcySlshMm, int oMdcySlshDd, int oMdcySlshCc, int oMdcySlshYy, int oMdcyMm, int oMdcyDd, int oMdcyCc, int oMdcyYy, int oYmdYy, int oYmdMm, int oYmdDd, int oCymdCc, int oCymdYy, int oCymdMm, int oCymdDd, int oJulYy, int oJulDdd, String oExpMm, int oExpDd, int oExpCc, int oExpYy, int differenceInDays, int oDayNumber, String reqCountry, OutputNextMoYr outputNextMoYr, OutputLastMoYr outputLastMoYr, String i2MdyMm, String i2MdyDd, String i2MdyYy) {
        super(reqDay, manipulateReqType, numberOfDays, numberOfMonths, inputDateType, oIsoCc, oIsoYy, oIsoMm, oIsoDd, oUsaMm, oUsaDd, oUsaCc, oUsaYy, oEurDd, oEurMm, oEurCc, oEurYy, oJisCc, oJisYy, oJisMm, oJisDd, oMdySlshMm, oMdySlshDd, oMdySlshYy, oMdyMm, oMdyDd, oMdyYy, oMdcySlshMm, oMdcySlshDd, oMdcySlshCc, oMdcySlshYy, oMdcyMm, oMdcyDd, oMdcyCc, oMdcyYy, oYmdYy, oYmdMm, oYmdDd, oCymdCc, oCymdYy, oCymdMm, oCymdDd, oJulYy, oJulDdd, oExpMm, oExpDd, oExpCc, oExpYy, differenceInDays, oDayNumber, reqCountry, outputNextMoYr, outputLastMoYr);
        this.i2MdyMm = i2MdyMm;
        this.i2MdyDd = i2MdyDd;
        this.i2MdyYy = i2MdyYy;
    }
    
    @Override
    public I2MdyDate clone() throws CloneNotSupportedException {
        I2MdyDate cloned = (I2MdyDate) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code I2MdyDate} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected I2MdyDate(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code I2MdyDate} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected I2MdyDate(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I2MdyDate} object
     * @see #setBytes(byte[], int)
     */
    public static I2MdyDate fromBytes(byte[] bytes, int offset) {
        return new I2MdyDate(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I2MdyDate} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static I2MdyDate fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code I2MdyDate} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static I2MdyDate fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getI2MdyMm() {
        return this.i2MdyMm;
    }
    
    public void setI2MdyMm(String i2MdyMm) {
        this.i2MdyMm = i2MdyMm;
    }
    
    public String getI2MdyDd() {
        return this.i2MdyDd;
    }
    
    public void setI2MdyDd(String i2MdyDd) {
        this.i2MdyDd = i2MdyDd;
    }
    
    public String getI2MdyYy() {
        return this.i2MdyYy;
    }
    
    public void setI2MdyYy(String i2MdyYy) {
        this.i2MdyYy = i2MdyYy;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        i2MdyMm = "";
        i2MdyDd = "";
        i2MdyYy = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ i2MdyMm=\"");
        s.append(getI2MdyMm());
        s.append("\"");
        s.append(", i2MdyDd=\"");
        s.append(getI2MdyDd());
        s.append("\"");
        s.append(", i2MdyYy=\"");
        s.append(getI2MdyYy());
        s.append("\"");
        s.append(", filler64=\"");
        s.append(new String(filler64, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(I2MdyDate that) {
        return super.equals(that) &&
            this.i2MdyMm.equals(that.i2MdyMm) &&
            this.i2MdyDd.equals(that.i2MdyDd) &&
            this.i2MdyYy.equals(that.i2MdyYy) &&
            Arrays.equals(this.filler64, that.filler64);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof I2MdyDate other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof I2MdyDate;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(i2MdyMm);
        result = 31 * result + Objects.hashCode(i2MdyDd);
        result = 31 * result + Objects.hashCode(i2MdyYy);
        result = 31 * result + Arrays.hashCode(filler64);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(I2MdyDate that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.i2MdyMm.compareTo(that.i2MdyMm);
        if ( c != 0 ) return c;
        c = this.i2MdyDd.compareTo(that.i2MdyDd);
        if ( c != 0 ) return c;
        c = this.i2MdyYy.compareTo(that.i2MdyYy);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler64, that.filler64);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(DateParmArea that) {
        if (that instanceof I2MdyDate other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(DateParmArea.SIZE);
    }
    
    private static final StringField I_2_MDY_MM = factory.getStringField(2);
    private static final StringField I_2_MDY_DD = factory.getStringField(2);
    private static final StringField I_2_MDY_YY = factory.getStringField(2);
    private static final ByteArrayField FILLER_64 = factory.getByteArrayField(6);
    private byte[] filler64 = new byte[6];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "I2-MDY-DATE record at MXWW01.CPY:224"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        I_2_MDY_MM.putString(i2MdyMm, bytes, offset);
        I_2_MDY_DD.putString(i2MdyDd, bytes, offset);
        I_2_MDY_YY.putString(i2MdyYy, bytes, offset);
        FILLER_64.putByteArray(filler64, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#setBytes(byte[], int)} to set parent-class state.
     * @see "I2-MDY-DATE record at MXWW01.CPY:224"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        i2MdyMm = I_2_MDY_MM.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        i2MdyDd = I_2_MDY_DD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        i2MdyYy = I_2_MDY_YY.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_64.getByteArray(bytes, offset);
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
