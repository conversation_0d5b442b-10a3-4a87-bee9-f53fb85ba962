package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class I1MdySlshDate extends DateParmArea {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String i1MdySlshMm = "";
    private String i1MdySlshDd = "";
    private String i1MdySlshYy = "";
    
    /** Initialize fields to non-null default values */
    public I1MdySlshDate() {}
    
    /** Initialize all fields to provided values */
    public I1MdySlshDate(int reqDay, int manipulateReqType, int numberOfDays, int numberOfMonths, int inputDateType, int oIsoCc, int oIsoYy, int oIsoMm, int oIsoDd, int oUsaMm, int oUsaDd, int oUsaCc, int oUsaYy, int oEurDd, int oEurMm, int oEurCc, int oEurYy, int oJisCc, int oJisYy, int oJisMm, int oJisDd, int oMdySlshMm, int oMdySlshDd, int oMdySlshYy, int oMdyMm, int oMdyDd, int oMdyYy, int oMdcySlshMm, int oMdcySlshDd, int oMdcySlshCc, int oMdcySlshYy, int oMdcyMm, int oMdcyDd, int oMdcyCc, int oMdcyYy, int oYmdYy, int oYmdMm, int oYmdDd, int oCymdCc, int oCymdYy, int oCymdMm, int oCymdDd, int oJulYy, int oJulDdd, String oExpMm, int oExpDd, int oExpCc, int oExpYy, int differenceInDays, int oDayNumber, String reqCountry, OutputNextMoYr outputNextMoYr, OutputLastMoYr outputLastMoYr, String i1MdySlshMm, String i1MdySlshDd, String i1MdySlshYy) {
        super(reqDay, manipulateReqType, numberOfDays, numberOfMonths, inputDateType, oIsoCc, oIsoYy, oIsoMm, oIsoDd, oUsaMm, oUsaDd, oUsaCc, oUsaYy, oEurDd, oEurMm, oEurCc, oEurYy, oJisCc, oJisYy, oJisMm, oJisDd, oMdySlshMm, oMdySlshDd, oMdySlshYy, oMdyMm, oMdyDd, oMdyYy, oMdcySlshMm, oMdcySlshDd, oMdcySlshCc, oMdcySlshYy, oMdcyMm, oMdcyDd, oMdcyCc, oMdcyYy, oYmdYy, oYmdMm, oYmdDd, oCymdCc, oCymdYy, oCymdMm, oCymdDd, oJulYy, oJulDdd, oExpMm, oExpDd, oExpCc, oExpYy, differenceInDays, oDayNumber, reqCountry, outputNextMoYr, outputLastMoYr);
        this.i1MdySlshMm = i1MdySlshMm;
        this.i1MdySlshDd = i1MdySlshDd;
        this.i1MdySlshYy = i1MdySlshYy;
    }
    
    @Override
    public I1MdySlshDate clone() throws CloneNotSupportedException {
        I1MdySlshDate cloned = (I1MdySlshDate) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code I1MdySlshDate} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected I1MdySlshDate(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code I1MdySlshDate} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected I1MdySlshDate(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I1MdySlshDate} object
     * @see #setBytes(byte[], int)
     */
    public static I1MdySlshDate fromBytes(byte[] bytes, int offset) {
        return new I1MdySlshDate(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I1MdySlshDate} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static I1MdySlshDate fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code I1MdySlshDate} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static I1MdySlshDate fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getI1MdySlshMm() {
        return this.i1MdySlshMm;
    }
    
    public void setI1MdySlshMm(String i1MdySlshMm) {
        this.i1MdySlshMm = i1MdySlshMm;
    }
    
    public String getI1MdySlshDd() {
        return this.i1MdySlshDd;
    }
    
    public void setI1MdySlshDd(String i1MdySlshDd) {
        this.i1MdySlshDd = i1MdySlshDd;
    }
    
    public String getI1MdySlshYy() {
        return this.i1MdySlshYy;
    }
    
    public void setI1MdySlshYy(String i1MdySlshYy) {
        this.i1MdySlshYy = i1MdySlshYy;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        i1MdySlshMm = "";
        i1MdySlshDd = "";
        i1MdySlshYy = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ i1MdySlshMm=\"");
        s.append(getI1MdySlshMm());
        s.append("\"");
        s.append(", filler36=\"");
        s.append(new String(filler36, encoding));
        s.append("\"");
        s.append(", i1MdySlshDd=\"");
        s.append(getI1MdySlshDd());
        s.append("\"");
        s.append(", filler37=\"");
        s.append(new String(filler37, encoding));
        s.append("\"");
        s.append(", i1MdySlshYy=\"");
        s.append(getI1MdySlshYy());
        s.append("\"");
        s.append(", filler38=\"");
        s.append(new String(filler38, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(I1MdySlshDate that) {
        return super.equals(that) &&
            this.i1MdySlshMm.equals(that.i1MdySlshMm) &&
            Arrays.equals(this.filler36, that.filler36) &&
            this.i1MdySlshDd.equals(that.i1MdySlshDd) &&
            Arrays.equals(this.filler37, that.filler37) &&
            this.i1MdySlshYy.equals(that.i1MdySlshYy) &&
            Arrays.equals(this.filler38, that.filler38);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof I1MdySlshDate other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof I1MdySlshDate;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(i1MdySlshMm);
        result = 31 * result + Arrays.hashCode(filler36);
        result = 31 * result + Objects.hashCode(i1MdySlshDd);
        result = 31 * result + Arrays.hashCode(filler37);
        result = 31 * result + Objects.hashCode(i1MdySlshYy);
        result = 31 * result + Arrays.hashCode(filler38);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(I1MdySlshDate that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.i1MdySlshMm.compareTo(that.i1MdySlshMm);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler36, that.filler36);
        if ( c != 0 ) return c;
        c = this.i1MdySlshDd.compareTo(that.i1MdySlshDd);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler37, that.filler37);
        if ( c != 0 ) return c;
        c = this.i1MdySlshYy.compareTo(that.i1MdySlshYy);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler38, that.filler38);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(DateParmArea that) {
        if (that instanceof I1MdySlshDate other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(DateParmArea.SIZE);
    }
    
    private static final StringField I_1_MDY_SLSH_MM = factory.getStringField(2);
    private static final ByteArrayField FILLER_36 = factory.getByteArrayField(1);
    private byte[] filler36 = new byte[1];
    private static final StringField I_1_MDY_SLSH_DD = factory.getStringField(2);
    private static final ByteArrayField FILLER_37 = factory.getByteArrayField(1);
    private byte[] filler37 = new byte[1];
    private static final StringField I_1_MDY_SLSH_YY = factory.getStringField(2);
    private static final ByteArrayField FILLER_38 = factory.getByteArrayField(4);
    private byte[] filler38 = new byte[4];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "I1-MDY-SLSH-DATE record at MXWW01.CPY:128"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        I_1_MDY_SLSH_MM.putString(i1MdySlshMm, bytes, offset);
        FILLER_36.putByteArray(filler36, bytes, offset);
        I_1_MDY_SLSH_DD.putString(i1MdySlshDd, bytes, offset);
        FILLER_37.putByteArray(filler37, bytes, offset);
        I_1_MDY_SLSH_YY.putString(i1MdySlshYy, bytes, offset);
        FILLER_38.putByteArray(filler38, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#setBytes(byte[], int)} to set parent-class state.
     * @see "I1-MDY-SLSH-DATE record at MXWW01.CPY:128"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        i1MdySlshMm = I_1_MDY_SLSH_MM.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_36.getByteArray(bytes, offset);
        i1MdySlshDd = I_1_MDY_SLSH_DD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_37.getByteArray(bytes, offset);
        i1MdySlshYy = I_1_MDY_SLSH_YY.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_38.getByteArray(bytes, offset);
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
