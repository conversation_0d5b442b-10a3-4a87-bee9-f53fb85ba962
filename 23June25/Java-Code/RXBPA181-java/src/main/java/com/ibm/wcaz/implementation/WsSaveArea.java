package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class WsSaveArea implements Cloneable, Comparable<WsSaveArea> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String wsSaveCdfCustId = "";
    private String wsSaveCdfCustCode = "";
    private String wsSaveCreatDate = "";
    private String wsSaveCreatTime = "";
    private String wsSaveMfgName = "";
    
    /** Initialize fields to non-null default values */
    public WsSaveArea() {}
    
    /** Initialize all fields to provided values */
    public WsSaveArea(String wsSaveCdfCustId, String wsSaveCdfCustCode, String wsSaveCreatDate, String wsSaveCreatTime, String wsSaveMfgName) {
        this.wsSaveCdfCustId = wsSaveCdfCustId;
        this.wsSaveCdfCustCode = wsSaveCdfCustCode;
        this.wsSaveCreatDate = wsSaveCreatDate;
        this.wsSaveCreatTime = wsSaveCreatTime;
        this.wsSaveMfgName = wsSaveMfgName;
    }
    
    @Override
    public WsSaveArea clone() throws CloneNotSupportedException {
        WsSaveArea cloned = (WsSaveArea) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code WsSaveArea} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected WsSaveArea(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code WsSaveArea} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected WsSaveArea(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsSaveArea} object
     * @see #setBytes(byte[], int)
     */
    public static WsSaveArea fromBytes(byte[] bytes, int offset) {
        return new WsSaveArea(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsSaveArea} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static WsSaveArea fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code WsSaveArea} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static WsSaveArea fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getWsSaveCdfCustId() {
        return this.wsSaveCdfCustId;
    }
    
    public void setWsSaveCdfCustId(String wsSaveCdfCustId) {
        this.wsSaveCdfCustId = wsSaveCdfCustId;
    }
    
    public String getWsSaveCdfCustCode() {
        return this.wsSaveCdfCustCode;
    }
    
    public void setWsSaveCdfCustCode(String wsSaveCdfCustCode) {
        this.wsSaveCdfCustCode = wsSaveCdfCustCode;
    }
    
    public String getWsSaveCreatDate() {
        return this.wsSaveCreatDate;
    }
    
    public void setWsSaveCreatDate(String wsSaveCreatDate) {
        this.wsSaveCreatDate = wsSaveCreatDate;
    }
    
    public String getWsSaveCreatTime() {
        return this.wsSaveCreatTime;
    }
    
    public void setWsSaveCreatTime(String wsSaveCreatTime) {
        this.wsSaveCreatTime = wsSaveCreatTime;
    }
    
    public String getWsSaveMfgName() {
        return this.wsSaveMfgName;
    }
    
    public void setWsSaveMfgName(String wsSaveMfgName) {
        this.wsSaveMfgName = wsSaveMfgName;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        wsSaveCdfCustId = "";
        wsSaveCdfCustCode = "";
        wsSaveCreatDate = "";
        wsSaveCreatTime = "";
        wsSaveMfgName = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ wsSaveCdfCustId=\"");
        s.append(getWsSaveCdfCustId());
        s.append("\"");
        s.append(", wsSaveCdfCustCode=\"");
        s.append(getWsSaveCdfCustCode());
        s.append("\"");
        s.append(", wsSaveCreatDate=\"");
        s.append(getWsSaveCreatDate());
        s.append("\"");
        s.append(", wsSaveCreatTime=\"");
        s.append(getWsSaveCreatTime());
        s.append("\"");
        s.append(", wsSaveMfgName=\"");
        s.append(getWsSaveMfgName());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(WsSaveArea that) {
        return this.wsSaveCdfCustId.equals(that.wsSaveCdfCustId) &&
            this.wsSaveCdfCustCode.equals(that.wsSaveCdfCustCode) &&
            this.wsSaveCreatDate.equals(that.wsSaveCreatDate) &&
            this.wsSaveCreatTime.equals(that.wsSaveCreatTime) &&
            this.wsSaveMfgName.equals(that.wsSaveMfgName);
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof WsSaveArea other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof WsSaveArea;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Objects.hashCode(wsSaveCdfCustId);
        result = 31 * result + Objects.hashCode(wsSaveCdfCustCode);
        result = 31 * result + Objects.hashCode(wsSaveCreatDate);
        result = 31 * result + Objects.hashCode(wsSaveCreatTime);
        result = 31 * result + Objects.hashCode(wsSaveMfgName);
        return result;
    }
    
    @Override
    public int compareTo(WsSaveArea that) {
        int c = 0;
        c = this.wsSaveCdfCustId.compareTo(that.wsSaveCdfCustId);
        if ( c != 0 ) return c;
        c = this.wsSaveCdfCustCode.compareTo(that.wsSaveCdfCustCode);
        if ( c != 0 ) return c;
        c = this.wsSaveCreatDate.compareTo(that.wsSaveCreatDate);
        if ( c != 0 ) return c;
        c = this.wsSaveCreatTime.compareTo(that.wsSaveCreatTime);
        if ( c != 0 ) return c;
        c = this.wsSaveMfgName.compareTo(that.wsSaveMfgName);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final StringField WS_SAVE_CDF_CUST_ID = factory.getStringField(4);
    private static final StringField WS_SAVE_CDF_CUST_CODE = factory.getStringField(4);
    private static final StringField WS_SAVE_CREAT_DATE = factory.getStringField(10);
    private static final StringField WS_SAVE_CREAT_TIME = factory.getStringField(5);
    private static final StringField WS_SAVE_MFG_NAME = factory.getStringField(20);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsSaveArea} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code WS-SAVE-AREA} record
     * @see "WS-SAVE-AREA record at RXBPASVC.cbl:97"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        WS_SAVE_CDF_CUST_ID.putString(wsSaveCdfCustId, bytes, offset);
        WS_SAVE_CDF_CUST_CODE.putString(wsSaveCdfCustCode, bytes, offset);
        WS_SAVE_CREAT_DATE.putString(wsSaveCreatDate, bytes, offset);
        WS_SAVE_CREAT_TIME.putString(wsSaveCreatTime, bytes, offset);
        WS_SAVE_MFG_NAME.putString(wsSaveMfgName, bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsSaveArea} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsSaveArea} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code WsSaveArea} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code WS-SAVE-AREA} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "WS-SAVE-AREA record at RXBPASVC.cbl:97"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        wsSaveCdfCustId = WS_SAVE_CDF_CUST_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        wsSaveCdfCustCode = WS_SAVE_CDF_CUST_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        wsSaveCreatDate = WS_SAVE_CREAT_DATE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        wsSaveCreatTime = WS_SAVE_CREAT_TIME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        wsSaveMfgName = WS_SAVE_MFG_NAME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
