package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class I2JisDate extends DateParmArea {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String i2JisCc = "";
    private String i2JisYy = "";
    private String i2JisMm = "";
    private String i2JisDd = "";
    
    /** Initialize fields to non-null default values */
    public I2JisDate() {}
    
    /** Initialize all fields to provided values */
    public I2JisDate(int reqDay, int manipulateReqType, int numberOfDays, int numberOfMonths, int inputDateType, int oIsoCc, int oIsoYy, int oIsoMm, int oIsoDd, int oUsaMm, int oUsaDd, int oUsaCc, int oUsaYy, int oEurDd, int oEurMm, int oEurCc, int oEurYy, int oJisCc, int oJisYy, int oJisMm, int oJisDd, int oMdySlshMm, int oMdySlshDd, int oMdySlshYy, int oMdyMm, int oMdyDd, int oMdyYy, int oMdcySlshMm, int oMdcySlshDd, int oMdcySlshCc, int oMdcySlshYy, int oMdcyMm, int oMdcyDd, int oMdcyCc, int oMdcyYy, int oYmdYy, int oYmdMm, int oYmdDd, int oCymdCc, int oCymdYy, int oCymdMm, int oCymdDd, int oJulYy, int oJulDdd, String oExpMm, int oExpDd, int oExpCc, int oExpYy, int differenceInDays, int oDayNumber, String reqCountry, OutputNextMoYr outputNextMoYr, OutputLastMoYr outputLastMoYr, String i2JisCc, String i2JisYy, String i2JisMm, String i2JisDd) {
        super(reqDay, manipulateReqType, numberOfDays, numberOfMonths, inputDateType, oIsoCc, oIsoYy, oIsoMm, oIsoDd, oUsaMm, oUsaDd, oUsaCc, oUsaYy, oEurDd, oEurMm, oEurCc, oEurYy, oJisCc, oJisYy, oJisMm, oJisDd, oMdySlshMm, oMdySlshDd, oMdySlshYy, oMdyMm, oMdyDd, oMdyYy, oMdcySlshMm, oMdcySlshDd, oMdcySlshCc, oMdcySlshYy, oMdcyMm, oMdcyDd, oMdcyCc, oMdcyYy, oYmdYy, oYmdMm, oYmdDd, oCymdCc, oCymdYy, oCymdMm, oCymdDd, oJulYy, oJulDdd, oExpMm, oExpDd, oExpCc, oExpYy, differenceInDays, oDayNumber, reqCountry, outputNextMoYr, outputLastMoYr);
        this.i2JisCc = i2JisCc;
        this.i2JisYy = i2JisYy;
        this.i2JisMm = i2JisMm;
        this.i2JisDd = i2JisDd;
    }
    
    @Override
    public I2JisDate clone() throws CloneNotSupportedException {
        I2JisDate cloned = (I2JisDate) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code I2JisDate} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected I2JisDate(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code I2JisDate} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected I2JisDate(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I2JisDate} object
     * @see #setBytes(byte[], int)
     */
    public static I2JisDate fromBytes(byte[] bytes, int offset) {
        return new I2JisDate(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code I2JisDate} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static I2JisDate fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code I2JisDate} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static I2JisDate fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getI2JisCc() {
        return this.i2JisCc;
    }
    
    public void setI2JisCc(String i2JisCc) {
        this.i2JisCc = i2JisCc;
    }
    
    public String getI2JisYy() {
        return this.i2JisYy;
    }
    
    public void setI2JisYy(String i2JisYy) {
        this.i2JisYy = i2JisYy;
    }
    
    public String getI2JisMm() {
        return this.i2JisMm;
    }
    
    public void setI2JisMm(String i2JisMm) {
        this.i2JisMm = i2JisMm;
    }
    
    public String getI2JisDd() {
        return this.i2JisDd;
    }
    
    public void setI2JisDd(String i2JisDd) {
        this.i2JisDd = i2JisDd;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        i2JisCc = "";
        i2JisYy = "";
        i2JisMm = "";
        i2JisDd = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ i2JisCc=\"");
        s.append(getI2JisCc());
        s.append("\"");
        s.append(", i2JisYy=\"");
        s.append(getI2JisYy());
        s.append("\"");
        s.append(", filler58=\"");
        s.append(new String(filler58, encoding));
        s.append("\"");
        s.append(", i2JisMm=\"");
        s.append(getI2JisMm());
        s.append("\"");
        s.append(", filler59=\"");
        s.append(new String(filler59, encoding));
        s.append("\"");
        s.append(", i2JisDd=\"");
        s.append(getI2JisDd());
        s.append("\"");
        s.append(", filler60=\"");
        s.append(new String(filler60, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(I2JisDate that) {
        return super.equals(that) &&
            this.i2JisCc.equals(that.i2JisCc) &&
            this.i2JisYy.equals(that.i2JisYy) &&
            Arrays.equals(this.filler58, that.filler58) &&
            this.i2JisMm.equals(that.i2JisMm) &&
            Arrays.equals(this.filler59, that.filler59) &&
            this.i2JisDd.equals(that.i2JisDd) &&
            Arrays.equals(this.filler60, that.filler60);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof I2JisDate other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof I2JisDate;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(i2JisCc);
        result = 31 * result + Objects.hashCode(i2JisYy);
        result = 31 * result + Arrays.hashCode(filler58);
        result = 31 * result + Objects.hashCode(i2JisMm);
        result = 31 * result + Arrays.hashCode(filler59);
        result = 31 * result + Objects.hashCode(i2JisDd);
        result = 31 * result + Arrays.hashCode(filler60);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(I2JisDate that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.i2JisCc.compareTo(that.i2JisCc);
        if ( c != 0 ) return c;
        c = this.i2JisYy.compareTo(that.i2JisYy);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler58, that.filler58);
        if ( c != 0 ) return c;
        c = this.i2JisMm.compareTo(that.i2JisMm);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler59, that.filler59);
        if ( c != 0 ) return c;
        c = this.i2JisDd.compareTo(that.i2JisDd);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler60, that.filler60);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(DateParmArea that) {
        if (that instanceof I2JisDate other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(DateParmArea.SIZE);
    }
    
    private static final StringField I_2_JIS_CC = factory.getStringField(2);
    private static final StringField I_2_JIS_YY = factory.getStringField(2);
    private static final ByteArrayField FILLER_58 = factory.getByteArrayField(1);
    private byte[] filler58 = new byte[1];
    private static final StringField I_2_JIS_MM = factory.getStringField(2);
    private static final ByteArrayField FILLER_59 = factory.getByteArrayField(1);
    private byte[] filler59 = new byte[1];
    private static final StringField I_2_JIS_DD = factory.getStringField(2);
    private static final ByteArrayField FILLER_60 = factory.getByteArrayField(2);
    private byte[] filler60 = new byte[2];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "I2-JIS-DATE record at MXWW01.CPY:208"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        I_2_JIS_CC.putString(i2JisCc, bytes, offset);
        I_2_JIS_YY.putString(i2JisYy, bytes, offset);
        FILLER_58.putByteArray(filler58, bytes, offset);
        I_2_JIS_MM.putString(i2JisMm, bytes, offset);
        FILLER_59.putByteArray(filler59, bytes, offset);
        I_2_JIS_DD.putString(i2JisDd, bytes, offset);
        FILLER_60.putByteArray(filler60, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link DateParmArea#setBytes(byte[], int)} to set parent-class state.
     * @see "I2-JIS-DATE record at MXWW01.CPY:208"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        i2JisCc = I_2_JIS_CC.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        i2JisYy = I_2_JIS_YY.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_58.getByteArray(bytes, offset);
        i2JisMm = I_2_JIS_MM.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_59.getByteArray(bytes, offset);
        i2JisDd = I_2_JIS_DD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_60.getByteArray(bytes, offset);
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
